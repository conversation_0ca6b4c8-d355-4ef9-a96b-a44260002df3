<?php

declare(strict_types=1);

namespace DOM5MemberDirectory\Models;

/**
 * Member model class
 * 
 * Handles CRUD operations for members
 */
class Member
{
    private $wpdb;
    private string $table_name;

    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dom5md_members';
    }

    /**
     * Create a new member
     */
    public function create(array $data): int|false
    {
        // Validate required fields
        if (empty($data['first_name']) || empty($data['last_name']) || empty($data['email'])) {
            return false;
        }

        // Check for duplicate email
        if ($this->email_exists($data['email'])) {
            return false;
        }

        // Generate slug
        $data['slug'] = $this->generate_slug($data['first_name'], $data['last_name']);

        // Sanitize data
        $sanitized_data = $this->sanitize_member_data($data);

        $result = $this->wpdb->insert(
            $this->table_name,
            $sanitized_data,
            [
                '%s', // first_name
                '%s', // last_name
                '%s', // email
                '%d', // profile_image_id
                '%d', // cover_image_id
                '%s', // address
                '%s', // favorite_color
                '%s', // status
                '%s', // slug
            ]
        );

        return $result ? $this->wpdb->insert_id : false;
    }

    /**
     * Get member by ID
     */
    public function get_by_id(int $id): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $id
            )
        );

        return $result ?: null;
    }

    /**
     * Get member by slug
     */
    public function get_by_slug(string $slug): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE slug = %s",
                $slug
            )
        );

        return $result ?: null;
    }

    /**
     * Get member by email
     */
    public function get_by_email(string $email): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE email = %s",
                $email
            )
        );

        return $result ?: null;
    }

    /**
     * Update member
     */
    public function update(int $id, array $data): bool
    {
        // Check if member exists
        if (!$this->get_by_id($id)) {
            return false;
        }

        // Check for duplicate email (excluding current member)
        if (isset($data['email']) && $this->email_exists($data['email'], $id)) {
            return false;
        }

        // Update slug if name changed
        if (isset($data['first_name']) || isset($data['last_name'])) {
            $current_member = $this->get_by_id($id);
            $first_name = $data['first_name'] ?? $current_member->first_name;
            $last_name = $data['last_name'] ?? $current_member->last_name;
            $data['slug'] = $this->generate_slug($first_name, $last_name, $id);
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_member_data($data);

        $result = $this->wpdb->update(
            $this->table_name,
            $sanitized_data,
            ['id' => $id],
            [
                '%s', // first_name
                '%s', // last_name
                '%s', // email
                '%d', // profile_image_id
                '%d', // cover_image_id
                '%s', // address
                '%s', // favorite_color
                '%s', // status
                '%s', // slug
            ],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Delete member
     */
    public function delete(int $id): bool
    {
        $result = $this->wpdb->delete(
            $this->table_name,
            ['id' => $id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get all members with pagination and filtering
     */
    public function get_all(array $args = []): array
    {
        $defaults = [
            'status' => 'active',
            'limit' => 12,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'search' => '',
        ];

        $args = wp_parse_args($args, $defaults);

        $where_clauses = [];
        $where_values = [];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(first_name LIKE %s OR last_name LIKE %s OR email LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        // Build query
        $sql = "SELECT * FROM {$this->table_name} 
                {$where_sql} 
                ORDER BY {$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";

        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results ?: [];
    }

    /**
     * Get total count of members
     */
    public function get_total_count(array $args = []): int
    {
        $where_clauses = [];
        $where_values = [];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(first_name LIKE %s OR last_name LIKE %s OR email LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        $sql = "SELECT COUNT(*) FROM {$this->table_name} {$where_sql}";

        if (!empty($where_values)) {
            $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $where_values));
        } else {
            $count = $this->wpdb->get_var($sql);
        }

        return (int) $count;
    }

    /**
     * Check if email exists
     */
    private function email_exists(string $email, int $exclude_id = 0): bool
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE email = %s";
        $values = [$email];

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        return (int) $count > 0;
    }

    /**
     * Generate unique slug
     */
    private function generate_slug(string $first_name, string $last_name, int $exclude_id = 0): string
    {
        $base_slug = sanitize_title($first_name . '_' . $last_name);
        $slug = $base_slug;
        $counter = 1;

        while ($this->slug_exists($slug, $exclude_id)) {
            $slug = $base_slug . '_' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slug_exists(string $slug, int $exclude_id = 0): bool
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE slug = %s";
        $values = [$slug];

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        return (int) $count > 0;
    }

    /**
     * Sanitize member data
     */
    private function sanitize_member_data(array $data): array
    {
        $sanitized = [];

        if (isset($data['first_name'])) {
            $sanitized['first_name'] = sanitize_text_field($data['first_name']);
        }

        if (isset($data['last_name'])) {
            $sanitized['last_name'] = sanitize_text_field($data['last_name']);
        }

        if (isset($data['email'])) {
            $sanitized['email'] = sanitize_email($data['email']);
        }

        if (isset($data['profile_image_id'])) {
            $sanitized['profile_image_id'] = absint($data['profile_image_id']) ?: null;
        }

        if (isset($data['cover_image_id'])) {
            $sanitized['cover_image_id'] = absint($data['cover_image_id']) ?: null;
        }

        if (isset($data['address'])) {
            $sanitized['address'] = sanitize_textarea_field($data['address']);
        }

        if (isset($data['favorite_color'])) {
            $sanitized['favorite_color'] = sanitize_hex_color($data['favorite_color']);
        }

        if (isset($data['status'])) {
            $sanitized['status'] = in_array($data['status'], ['active', 'draft']) ? $data['status'] : 'draft';
        }

        if (isset($data['slug'])) {
            $sanitized['slug'] = sanitize_title($data['slug']);
        }

        return $sanitized;
    }
}
