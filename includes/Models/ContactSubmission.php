<?php

declare(strict_types=1);

namespace DOM5MemberDirectory\Models;

/**
 * Contact Submission model class
 * 
 * Handles CRUD operations for contact form submissions
 */
class ContactSubmission
{
    private $wpdb;
    private string $table_name;

    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dom5md_contact_submissions';
    }

    /**
     * Create a new contact submission
     */
    public function create(array $data): int|false
    {
        // Validate required fields
        if (empty($data['member_id']) || empty($data['sender_name']) || 
            empty($data['sender_email']) || empty($data['message'])) {
            return false;
        }

        // Add IP address and user agent
        $data['ip_address'] = $this->get_client_ip();
        $data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Sanitize data
        $sanitized_data = $this->sanitize_submission_data($data);

        $result = $this->wpdb->insert(
            $this->table_name,
            $sanitized_data,
            [
                '%d', // member_id
                '%s', // sender_name
                '%s', // sender_email
                '%s', // message
                '%s', // ip_address
                '%s', // user_agent
                '%s', // status
            ]
        );

        return $result ? $this->wpdb->insert_id : false;
    }

    /**
     * Get submission by ID
     */
    public function get_by_id(int $id): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $id
            )
        );

        return $result ?: null;
    }

    /**
     * Update submission status
     */
    public function update_status(int $id, string $status): bool
    {
        if (!in_array($status, ['unread', 'read', 'replied'])) {
            return false;
        }

        $result = $this->wpdb->update(
            $this->table_name,
            ['status' => $status],
            ['id' => $id],
            ['%s'],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Delete submission
     */
    public function delete(int $id): bool
    {
        $result = $this->wpdb->delete(
            $this->table_name,
            ['id' => $id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get submissions for a specific member
     */
    public function get_by_member(int $member_id, array $args = []): array
    {
        $defaults = [
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC',
        ];

        $args = wp_parse_args($args, $defaults);

        $where_clauses = ['member_id = %d'];
        $where_values = [$member_id];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);

        $sql = "SELECT * FROM {$this->table_name} 
                {$where_sql} 
                ORDER BY {$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";

        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results ?: [];
    }

    /**
     * Get all submissions with pagination and filtering
     */
    public function get_all(array $args = []): array
    {
        $defaults = [
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'search' => '',
        ];

        $args = wp_parse_args($args, $defaults);

        $where_clauses = [];
        $where_values = [];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(sender_name LIKE %s OR sender_email LIKE %s OR message LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        $sql = "SELECT cs.*, m.first_name, m.last_name, m.email as member_email 
                FROM {$this->table_name} cs 
                LEFT JOIN {$this->wpdb->prefix}dom5md_members m ON cs.member_id = m.id 
                {$where_sql} 
                ORDER BY cs.{$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";

        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results ?: [];
    }

    /**
     * Get total count of submissions
     */
    public function get_total_count(array $args = []): int
    {
        $where_clauses = [];
        $where_values = [];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(sender_name LIKE %s OR sender_email LIKE %s OR message LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        $sql = "SELECT COUNT(*) FROM {$this->table_name} {$where_sql}";

        if (!empty($where_values)) {
            $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $where_values));
        } else {
            $count = $this->wpdb->get_var($sql);
        }

        return (int) $count;
    }

    /**
     * Get submission count for a specific member
     */
    public function get_member_submission_count(int $member_id, string $status = ''): int
    {
        $where_clauses = ['member_id = %d'];
        $where_values = [$member_id];

        if (!empty($status)) {
            $where_clauses[] = 'status = %s';
            $where_values[] = $status;
        }

        $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);

        $sql = "SELECT COUNT(*) FROM {$this->table_name} {$where_sql}";

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $where_values));
        return (int) $count;
    }

    /**
     * Get submission counts by status
     */
    public function get_status_counts(): array
    {
        $sql = "SELECT status, COUNT(*) as count 
                FROM {$this->table_name} 
                GROUP BY status";

        $results = $this->wpdb->get_results($sql);

        $counts = [
            'unread' => 0,
            'read' => 0,
            'replied' => 0,
        ];

        foreach ($results as $result) {
            $counts[$result->status] = (int) $result->count;
        }

        return $counts;
    }

    /**
     * Mark submission as read
     */
    public function mark_as_read(int $id): bool
    {
        return $this->update_status($id, 'read');
    }

    /**
     * Mark submission as replied
     */
    public function mark_as_replied(int $id): bool
    {
        return $this->update_status($id, 'replied');
    }

    /**
     * Get client IP address
     */
    private function get_client_ip(): string
    {
        $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '';
    }

    /**
     * Sanitize submission data
     */
    private function sanitize_submission_data(array $data): array
    {
        $sanitized = [];

        if (isset($data['member_id'])) {
            $sanitized['member_id'] = absint($data['member_id']);
        }

        if (isset($data['sender_name'])) {
            $sanitized['sender_name'] = sanitize_text_field($data['sender_name']);
        }

        if (isset($data['sender_email'])) {
            $sanitized['sender_email'] = sanitize_email($data['sender_email']);
        }

        if (isset($data['message'])) {
            $sanitized['message'] = sanitize_textarea_field($data['message']);
        }

        if (isset($data['ip_address'])) {
            $sanitized['ip_address'] = sanitize_text_field($data['ip_address']);
        }

        if (isset($data['user_agent'])) {
            $sanitized['user_agent'] = sanitize_text_field($data['user_agent']);
        }

        if (isset($data['status'])) {
            $sanitized['status'] = in_array($data['status'], ['unread', 'read', 'replied']) ? $data['status'] : 'unread';
        } else {
            $sanitized['status'] = 'unread';
        }

        return $sanitized;
    }
}
