<?php

declare(strict_types=1);

namespace DOM5MemberDirectory\Models;

/**
 * Team model class
 * 
 * Handles CRUD operations for teams
 */
class Team
{
    private $wpdb;
    private string $table_name;
    private string $member_teams_table;

    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dom5md_teams';
        $this->member_teams_table = $wpdb->prefix . 'dom5md_member_teams';
    }

    /**
     * Create a new team
     */
    public function create(array $data): int|false
    {
        // Validate required fields
        if (empty($data['name'])) {
            return false;
        }

        // Check for duplicate name
        if ($this->name_exists($data['name'])) {
            return false;
        }

        // Generate slug
        $data['slug'] = $this->generate_slug($data['name']);

        // Sanitize data
        $sanitized_data = $this->sanitize_team_data($data);

        $result = $this->wpdb->insert(
            $this->table_name,
            $sanitized_data,
            [
                '%s', // name
                '%s', // short_description
                '%s', // slug
            ]
        );

        return $result ? $this->wpdb->insert_id : false;
    }

    /**
     * Get team by ID
     */
    public function get_by_id(int $id): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $id
            )
        );

        return $result ?: null;
    }

    /**
     * Get team by slug
     */
    public function get_by_slug(string $slug): ?object
    {
        $result = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE slug = %s",
                $slug
            )
        );

        return $result ?: null;
    }

    /**
     * Update team
     */
    public function update(int $id, array $data): bool
    {
        // Check if team exists
        if (!$this->get_by_id($id)) {
            return false;
        }

        // Check for duplicate name (excluding current team)
        if (isset($data['name']) && $this->name_exists($data['name'], $id)) {
            return false;
        }

        // Update slug if name changed
        if (isset($data['name'])) {
            $data['slug'] = $this->generate_slug($data['name'], $id);
        }

        // Sanitize data
        $sanitized_data = $this->sanitize_team_data($data);

        $result = $this->wpdb->update(
            $this->table_name,
            $sanitized_data,
            ['id' => $id],
            [
                '%s', // name
                '%s', // short_description
                '%s', // slug
            ],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Delete team
     */
    public function delete(int $id): bool
    {
        $result = $this->wpdb->delete(
            $this->table_name,
            ['id' => $id],
            ['%d']
        );

        return $result !== false;
    }

    /**
     * Get all teams with pagination and filtering
     */
    public function get_all(array $args = []): array
    {
        $defaults = [
            'limit' => 12,
            'offset' => 0,
            'orderby' => 'name',
            'order' => 'ASC',
            'search' => '',
        ];

        $args = wp_parse_args($args, $defaults);

        $where_clauses = [];
        $where_values = [];

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(name LIKE %s OR short_description LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        $query = 'select t.*, mt.member_count from wp_dom5md_teams as t left join (select id,member_id, team_id, (select count(member_id) from wp_dom5md_member_teams where member_id=1 group by member_id) as member_count from wp_dom5md_member_teams) as mt on mt.team_id=t.id';
        // Build query
        $sql = "{$query}  
                {$where_sql} 
                ORDER BY {$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";

        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results ?: [];
    }

    /**
     * Get total count of teams
     */
    public function get_total_count(array $args = []): int
    {
        $where_clauses = [];
        $where_values = [];

        // Search filter
        if (!empty($args['search'])) {
            $where_clauses[] = '(name LIKE %s OR short_description LIKE %s)';
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

        $sql = "SELECT COUNT(*) FROM {$this->table_name} {$where_sql}";

        if (!empty($where_values)) {
            $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $where_values));
        } else {
            $count = $this->wpdb->get_var($sql);
        }

        return (int) $count;
    }

    /**
     * Get team members
     */
    public function get_members(int $team_id, array $args = []): array
    {
        $defaults = [
            'status' => 'active',
            'limit' => 50,
            'offset' => 0,
            'orderby' => 'm.first_name',
            'order' => 'ASC',
        ];

        $args = wp_parse_args($args, $defaults);

        $members_table = $this->wpdb->prefix . 'dom5md_members';

        $where_clauses = ['mt.team_id = %d'];
        $where_values = [$team_id];

        // Status filter
        if (!empty($args['status'])) {
            $where_clauses[] = 'm.status = %s';
            $where_values[] = $args['status'];
        }

        $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);

        $sql = "SELECT m.*, mt.created_at as joined_at 
                FROM {$members_table} m 
                INNER JOIN {$this->member_teams_table} mt ON m.id = mt.member_id 
                {$where_sql} 
                ORDER BY {$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";

        $where_values[] = $args['limit'];
        $where_values[] = $args['offset'];

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results ?: [];
    }

    /**
     * Add member to team
     */
    public function add_member(int $team_id, int $member_id): bool
    {
        // Check if relationship already exists
        $exists = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->member_teams_table} 
                 WHERE team_id = %d AND member_id = %d",
                $team_id,
                $member_id
            )
        );

        if ($exists > 0) {
            return false; // Relationship already exists
        }

        $result = $this->wpdb->insert(
            $this->member_teams_table,
            [
                'team_id' => $team_id,
                'member_id' => $member_id,
            ],
            ['%d', '%d']
        );

        return $result !== false;
    }

    /**
     * Remove member from team
     */
    public function remove_member(int $team_id, int $member_id): bool
    {
        $result = $this->wpdb->delete(
            $this->member_teams_table,
            [
                'team_id' => $team_id,
                'member_id' => $member_id,
            ],
            ['%d', '%d']
        );

        return $result !== false;
    }

    /**
     * Get teams for a specific member
     */
    public function get_member_teams(int $member_id): array
    {
        $sql = "SELECT t.*, mt.created_at as joined_at 
                FROM {$this->table_name} t 
                INNER JOIN {$this->member_teams_table} mt ON t.id = mt.team_id 
                WHERE mt.member_id = %d 
                ORDER BY t.name ASC";

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $member_id)
        );

        return $results ?: [];
    }

    /**
     * Check if team name exists
     */
    private function name_exists(string $name, int $exclude_id = 0): bool
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE name = %s";
        $values = [$name];

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        return (int) $count > 0;
    }

    /**
     * Generate unique slug
     */
    private function generate_slug(string $name, int $exclude_id = 0): string
    {
        $base_slug = sanitize_title($name);
        $slug = $base_slug;
        $counter = 1;

        while ($this->slug_exists($slug, $exclude_id)) {
            $slug = $base_slug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slug_exists(string $slug, int $exclude_id = 0): bool
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE slug = %s";
        $values = [$slug];

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        return (int) $count > 0;
    }

    /**
     * Sanitize team data
     */
    private function sanitize_team_data(array $data): array
    {
        $sanitized = [];

        if (isset($data['name'])) {
            $sanitized['name'] = sanitize_text_field($data['name']);
        }

        if (isset($data['short_description'])) {
            $sanitized['short_description'] = sanitize_textarea_field($data['short_description']);
        }

        if (isset($data['slug'])) {
            $sanitized['slug'] = sanitize_title($data['slug']);
        }

        return $sanitized;
    }
}
