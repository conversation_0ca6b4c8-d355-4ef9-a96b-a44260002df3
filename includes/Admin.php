<?php

declare(strict_types=1);

namespace DOM5MemberDirectory;

/**
 * Admin handler
 *
 * Manages all admin-related functionality
 */
class Admin
{
    /**
     * Menu handler instance
     */
    private Admin\Menu $menu;

    /**
     * Assets handler instance
     */
    private Admin\Assets $assets;

    public function __construct()
    {
        $this->init_admin_components();
        $this->add_admin_hooks();
    }

    /**
     * Initialize admin components
     */
    private function init_admin_components(): void
    {
        $this->menu = new Admin\Menu();
        $this->assets = new Admin\Assets();
    }

    /**
     * Add admin-specific hooks
     */
    private function add_admin_hooks(): void
    {
        // Add admin notices
        add_action('admin_notices', [$this, 'admin_notices']);

        // Add admin body class
        add_filter('admin_body_class', [$this, 'admin_body_class']);

        // Add custom admin columns for members
        add_filter('manage_users_columns', [$this, 'add_member_columns']);
        add_filter('manage_users_custom_column', [$this, 'manage_member_columns'], 10, 3);
    }

    /**
     * Display admin notices
     */
    public function admin_notices(): void
    {
        // Check if database tables exist
        if (!$this->check_database_tables()) {
            ?>
            <div class="notice notice-warning">
                <p>
                    <?php esc_html_e('DOM5 Member Directory: Database tables are missing. Please deactivate and reactivate the plugin.', 'dom5-member-directory'); ?>
                </p>
            </div>
            <?php
        }

        // Check for plugin updates
        $this->check_plugin_updates();
    }

    /**
     * Add custom admin body class
     */
    public function admin_body_class(string $classes): string
    {
        $screen = get_current_screen();

        if ($screen && strpos($screen->id, 'dom5-member-directory') !== false) {
            $classes .= ' dom5md-admin-page';
        }

        return $classes;
    }

    /**
     * Add custom columns to users table (for reference)
     */
    public function add_member_columns(array $columns): array
    {
        $columns['dom5md_member'] = __('Member Directory', 'dom5-member-directory');
        return $columns;
    }

    /**
     * Manage custom columns content
     */
    public function manage_member_columns(string $value, string $column_name, int $user_id): string
    {
        if ($column_name === 'dom5md_member') {
            $member_model = new Models\Member();
            $user = get_userdata($user_id);

            if ($user) {
                $member = $member_model->get_by_email($user->user_email);
                if ($member) {
                    $edit_url = admin_url('admin.php?page=dom5-member-directory-members&action=edit&id=' . $member->id);
                    $value = sprintf(
                        '<a href="%s">%s</a>',
                        esc_url($edit_url),
                        esc_html__('View/Edit Member', 'dom5-member-directory')
                    );
                } else {
                    $value = '<span class="description">' . esc_html__('Not a member', 'dom5-member-directory') . '</span>';
                }
            }
        }

        return $value;
    }

    /**
     * Check if database tables exist
     */
    private function check_database_tables(): bool
    {
        global $wpdb;

        $tables = [
            $wpdb->prefix . 'dom5md_members',
            $wpdb->prefix . 'dom5md_teams',
            $wpdb->prefix . 'dom5md_member_teams',
            $wpdb->prefix . 'dom5md_contact_submissions',
        ];

        foreach ($tables as $table) {
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table}'") !== $table) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check for plugin updates
     */
    private function check_plugin_updates(): void
    {
        $current_version = get_option('dom5md_version', '0.0.0');

        if (version_compare($current_version, DOM5MD_VERSION, '<')) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p>
                    <?php
                    printf(
                        esc_html__('DOM5 Member Directory has been updated to version %s. Database will be updated automatically.', 'dom5-member-directory'),
                        DOM5MD_VERSION
                    );
                    ?>
                </p>
            </div>
            <?php

            // Update version option
            update_option('dom5md_version', DOM5MD_VERSION);
        }
    }

    /**
     * Get menu handler
     */
    public function get_menu(): Admin\Menu
    {
        return $this->menu;
    }

    /**
     * Get assets handler
     */
    public function get_assets(): Admin\Assets
    {
        return $this->assets;
    }
}