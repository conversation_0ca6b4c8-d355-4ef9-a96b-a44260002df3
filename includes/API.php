<?php

declare(strict_types=1);

namespace DOM5MemberDirectory;

/**
 * REST API handler
 * 
 * Registers and handles REST API endpoints for the plugin
 */
class API
{
    /**
     * API namespace
     */
    private const NAMESPACE = 'dom5md/v1';

    public function __construct()
    {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    /**
     * Register REST API routes
     */
    public function register_routes(): void
    {
        // Members endpoints
        register_rest_route(self::NAMESPACE, '/members', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_members'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'create_member'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_member_schema(),
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/members/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_member'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_member'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_member_schema(),
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_member'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Teams endpoints
        register_rest_route(self::NAMESPACE, '/teams', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_teams'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'create_team'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_team_schema(),
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/teams/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_team'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_team_schema(),
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Member-Team relationships
        register_rest_route(self::NAMESPACE, '/members/(?P<member_id>\d+)/teams', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_member_teams'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'add_member_to_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/members/(?P<member_id>\d+)/teams/(?P<team_id>\d+)', [
            'methods' => 'DELETE',
            'callback' => [$this, 'remove_member_from_team'],
            'permission_callback' => [$this, 'check_permissions'],
        ]);

        // Contact submissions endpoints
        register_rest_route(self::NAMESPACE, '/submissions', [
            'methods' => 'GET',
            'callback' => [$this, 'get_submissions'],
            'permission_callback' => [$this, 'check_permissions'],
        ]);

        register_rest_route(self::NAMESPACE, '/submissions/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Public contact form submission (no auth required)
        register_rest_route(self::NAMESPACE, '/contact', [
            'methods' => 'POST',
            'callback' => [$this, 'submit_contact_form'],
            'permission_callback' => '__return_true',
            'args' => $this->get_contact_schema(),
        ]);
    }

    /**
     * Check user permissions
     */
    public function check_permissions(): bool
    {
        return current_user_can('manage_options');
    }

    /**
     * Get members
     */
    public function get_members(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        
        $args = [
            'status' => $request->get_param('status') ?: '',
            'search' => $request->get_param('search') ?: '',
            'limit' => $request->get_param('per_page') ?: 12,
            'offset' => ($request->get_param('page') - 1) * ($request->get_param('per_page') ?: 12),
            'orderby' => $request->get_param('orderby') ?: 'created_at',
            'order' => $request->get_param('order') ?: 'DESC',
        ];

        $members = $member_model->get_all($args);
        $total = $member_model->get_total_count($args);

        return new \WP_REST_Response([
            'members' => $members,
            'total' => $total,
            'pages' => ceil($total / $args['limit']),
        ]);
    }

    /**
     * Get single member
     */
    public function get_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member = $member_model->get_by_id((int) $request->get_param('id'));

        if (!$member) {
            return new \WP_REST_Response(['error' => 'Member not found'], 404);
        }

        return new \WP_REST_Response(['member' => $member]);
    }

    /**
     * Create member
     */
    public function create_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $data = $request->get_json_params();

        $member_id = $member_model->create($data);

        if (!$member_id) {
            return new \WP_REST_Response(['error' => 'Failed to create member'], 400);
        }

        $member = $member_model->get_by_id($member_id);
        return new \WP_REST_Response(['member' => $member], 201);
    }

    /**
     * Update member
     */
    public function update_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member_id = (int) $request->get_param('id');
        $data = $request->get_json_params();

        $success = $member_model->update($member_id, $data);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to update member'], 400);
        }

        $member = $member_model->get_by_id($member_id);
        return new \WP_REST_Response(['member' => $member]);
    }

    /**
     * Delete member
     */
    public function delete_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member_id = (int) $request->get_param('id');

        $success = $member_model->delete($member_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to delete member'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Get teams
     */
    public function get_teams(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        
        $args = [
            'search' => $request->get_param('search') ?: '',
            'limit' => $request->get_param('per_page') ?: 12,
            'offset' => ($request->get_param('page') - 1) * ($request->get_param('per_page') ?: 12),
            'orderby' => $request->get_param('orderby') ?: 'name',
            'order' => $request->get_param('order') ?: 'ASC',
        ];

        $teams = $team_model->get_all($args);
        $total = $team_model->get_total_count($args);

        return new \WP_REST_Response([
            'teams' => $teams,
            'total' => $total,
            'pages' => ceil($total / $args['limit']),
        ]);
    }

    /**
     * Get member schema for validation
     */
    private function get_member_schema(): array
    {
        return [
            'first_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'last_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'email' => [
                'required' => true,
                'type' => 'string',
                'format' => 'email',
                'sanitize_callback' => 'sanitize_email',
            ],
            'profile_image_id' => [
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'cover_image_id' => [
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'address' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'favorite_color' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_hex_color',
            ],
            'status' => [
                'type' => 'string',
                'enum' => ['active', 'draft'],
                'default' => 'draft',
            ],
        ];
    }

    /**
     * Get team schema for validation
     */
    private function get_team_schema(): array
    {
        return [
            'name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'short_description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
        ];
    }

    /**
     * Get contact form schema for validation
     */
    private function get_contact_schema(): array
    {
        return [
            'member_id' => [
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'sender_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'sender_email' => [
                'required' => true,
                'type' => 'string',
                'format' => 'email',
                'sanitize_callback' => 'sanitize_email',
            ],
            'message' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
        ];
    }
}
