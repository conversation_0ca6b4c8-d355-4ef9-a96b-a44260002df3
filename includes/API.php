<?php

declare(strict_types=1);

namespace DOM5MemberDirectory;

/**
 * REST API handler
 * 
 * Registers and handles REST API endpoints for the plugin
 */
class API
{
    /**
     * API namespace
     */
    private const NAMESPACE = 'dom5md/v1';

    public function __construct()
    {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    /**
     * Register REST API routes
     */
    public function register_routes(): void
    {
        // Members endpoints
        register_rest_route(self::NAMESPACE, '/members', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_members'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'create_member'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_member_schema(),
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/members/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_member'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_member'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_member_schema(),
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_member'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Teams endpoints
        register_rest_route(self::NAMESPACE, '/teams', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_teams'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'create_team'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_team_schema(),
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/teams/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_team'],
                'permission_callback' => [$this, 'check_permissions'],
                'args' => $this->get_team_schema(),
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Member-Team relationships
        register_rest_route(self::NAMESPACE, '/members/(?P<member_id>\d+)/teams', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_member_teams'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'POST',
                'callback' => [$this, 'add_member_to_team'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        register_rest_route(self::NAMESPACE, '/members/(?P<member_id>\d+)/teams/(?P<team_id>\d+)', [
            'methods' => 'DELETE',
            'callback' => [$this, 'remove_member_from_team'],
            'permission_callback' => [$this, 'check_permissions'],
        ]);

        // Contact submissions endpoints
        register_rest_route(self::NAMESPACE, '/submissions', [
            'methods' => 'GET',
            'callback' => [$this, 'get_submissions'],
            'permission_callback' => [$this, 'check_permissions'],
        ]);

        register_rest_route(self::NAMESPACE, '/submissions/(?P<id>\d+)', [
            [
                'methods' => 'GET',
                'callback' => [$this, 'get_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'PUT',
                'callback' => [$this, 'update_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
            [
                'methods' => 'DELETE',
                'callback' => [$this, 'delete_submission'],
                'permission_callback' => [$this, 'check_permissions'],
            ],
        ]);

        // Public contact form submission (no auth required)
        register_rest_route(self::NAMESPACE, '/contact', [
            'methods' => 'POST',
            'callback' => [$this, 'submit_contact_form'],
            'permission_callback' => '__return_true',
            'args' => $this->get_contact_schema(),
        ]);
    }

    /**
     * Check user permissions
     */
    public function check_permissions(): bool
    {
        return current_user_can('manage_options');
    }

    /**
     * Get members
     */
    public function get_members(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        
        $args = [
            'status' => $request->get_param('status') ?: '',
            'search' => $request->get_param('search') ?: '',
            'limit' => $request->get_param('per_page') ?: 12,
            'offset' => ($request->get_param('page') - 1) * ($request->get_param('per_page') ?: 12),
            'orderby' => $request->get_param('orderby') ?: 'created_at',
            'order' => $request->get_param('order') ?: 'DESC',
        ];

        $members = $member_model->get_all($args);

        error_log( print_r( $members, true ) );
        $total = $member_model->get_total_count($args);

        return new \WP_REST_Response([
            'members' => $members,
            'total' => $total,
            'pages' => ceil($total / $args['limit']),
        ]);
    }

    /**
     * Get single member
     */
    public function get_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member = $member_model->get_by_id((int) $request->get_param('id'));

        $member->profile_image_url = $member->profile_image_id 
            ? wp_get_attachment_url($member->profile_image_id)
            : null;

        if (!$member) {
            return new \WP_REST_Response(['error' => 'Member not found'], 404);
        }

        return new \WP_REST_Response(['member' => $member]);
    }

    /**
     * Create member
     */
    public function create_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $data = $request->get_json_params();

        $member_id = $member_model->create($data);

        if (!$member_id) {
            return new \WP_REST_Response(['error' => 'Failed to create member'], 400);
        }

        $member = $member_model->get_by_id($member_id);
        return new \WP_REST_Response(['member' => $member], 201);
    }

    /**
     * Update member
     */
    public function update_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member_id = (int) $request->get_param('id');
        $data = $request->get_json_params();

        $success = $member_model->update($member_id, $data);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to update member'], 400);
        }

        $member = $member_model->get_by_id($member_id);
        return new \WP_REST_Response(['member' => $member]);
    }

    /**
     * Delete member
     */
    public function delete_member(\WP_REST_Request $request): \WP_REST_Response
    {
        $member_model = new Models\Member();
        $member_id = (int) $request->get_param('id');

        $success = $member_model->delete($member_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to delete member'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Get teams
     */
    public function get_teams(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();

        $args = [
            'search' => $request->get_param('search') ?: '',
            'limit' => $request->get_param('per_page') ?: 12,
            'offset' => ($request->get_param('page') - 1) * ($request->get_param('per_page') ?: 12),
            'orderby' => $request->get_param('orderby') ?: 'name',
            'order' => $request->get_param('order') ?: 'ASC',
        ];

        $teams = $team_model->get_all($args);
        $total = $team_model->get_total_count($args);

        return new \WP_REST_Response([
            'teams' => $teams,
            'total' => $total,
            'pages' => ceil($total / $args['limit']),
        ]);
    }

    /**
     * Get single team
     */
    public function get_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $team = $team_model->get_by_id((int) $request->get_param('id'));

        if (!$team) {
            return new \WP_REST_Response(['error' => 'Team not found'], 404);
        }

        return new \WP_REST_Response(['team' => $team]);
    }

    /**
     * Create team
     */
    public function create_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $data = $request->get_json_params();

        $team_id = $team_model->create($data);

        if (!$team_id) {
            return new \WP_REST_Response(['error' => 'Failed to create team'], 400);
        }

        $team = $team_model->get_by_id($team_id);
        return new \WP_REST_Response(['team' => $team], 201);
    }

    /**
     * Update team
     */
    public function update_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $team_id = (int) $request->get_param('id');
        $data = $request->get_json_params();

        $success = $team_model->update($team_id, $data);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to update team'], 400);
        }

        $team = $team_model->get_by_id($team_id);
        return new \WP_REST_Response(['team' => $team]);
    }

    /**
     * Delete team
     */
    public function delete_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $team_id = (int) $request->get_param('id');

        $success = $team_model->delete($team_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to delete team'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Get member teams
     */
    public function get_member_teams(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $member_id = (int) $request->get_param('member_id');

        $teams = $team_model->get_member_teams($member_id);

        return new \WP_REST_Response(['teams' => $teams]);
    }

    /**
     * Add member to team
     */
    public function add_member_to_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $member_id = (int) $request->get_param('member_id');
        $data = $request->get_json_params();
        $team_id = (int) $data['team_id'];

        $success = $team_model->add_member($team_id, $member_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to add member to team'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Remove member from team
     */
    public function remove_member_from_team(\WP_REST_Request $request): \WP_REST_Response
    {
        $team_model = new Models\Team();
        $member_id = (int) $request->get_param('member_id');
        $team_id = (int) $request->get_param('team_id');

        $success = $team_model->remove_member($team_id, $member_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to remove member from team'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Get submissions
     */
    public function get_submissions(\WP_REST_Request $request): \WP_REST_Response
    {
        $submission_model = new Models\ContactSubmission();

        $args = [
            'status' => $request->get_param('status') ?: '',
            'search' => $request->get_param('search') ?: '',
            'limit' => $request->get_param('per_page') ?: 20,
            'offset' => ($request->get_param('page') - 1) * ($request->get_param('per_page') ?: 20),
            'orderby' => $request->get_param('orderby') ?: 'created_at',
            'order' => $request->get_param('order') ?: 'DESC',
        ];

        $submissions = $submission_model->get_all($args);
        $total = $submission_model->get_total_count($args);

        return new \WP_REST_Response([
            'submissions' => $submissions,
            'total' => $total,
            'pages' => ceil($total / $args['limit']),
        ]);
    }

    /**
     * Get single submission
     */
    public function get_submission(\WP_REST_Request $request): \WP_REST_Response
    {
        $submission_model = new Models\ContactSubmission();
        $submission = $submission_model->get_by_id((int) $request->get_param('id'));

        if (!$submission) {
            return new \WP_REST_Response(['error' => 'Submission not found'], 404);
        }

        return new \WP_REST_Response(['submission' => $submission]);
    }

    /**
     * Update submission
     */
    public function update_submission(\WP_REST_Request $request): \WP_REST_Response
    {
        $submission_model = new Models\ContactSubmission();
        $submission_id = (int) $request->get_param('id');
        $data = $request->get_json_params();

        if (isset($data['status'])) {
            $success = $submission_model->update_status($submission_id, $data['status']);

            if (!$success) {
                return new \WP_REST_Response(['error' => 'Failed to update submission'], 400);
            }
        }

        $submission = $submission_model->get_by_id($submission_id);
        return new \WP_REST_Response(['submission' => $submission]);
    }

    /**
     * Delete submission
     */
    public function delete_submission(\WP_REST_Request $request): \WP_REST_Response
    {
        $submission_model = new Models\ContactSubmission();
        $submission_id = (int) $request->get_param('id');

        $success = $submission_model->delete($submission_id);

        if (!$success) {
            return new \WP_REST_Response(['error' => 'Failed to delete submission'], 400);
        }

        return new \WP_REST_Response(['success' => true]);
    }

    /**
     * Submit contact form (public endpoint)
     */
    public function submit_contact_form(\WP_REST_Request $request): \WP_REST_Response
    {
        $submission_model = new Models\ContactSubmission();
        $member_model = new Models\Member();

        $data = $request->get_json_params();

        // Validate member exists and is active
        $member = $member_model->get_by_id((int) $data['member_id']);
        if (!$member || $member->status !== 'active') {
            return new \WP_REST_Response(['error' => 'Member not found or inactive'], 404);
        }

        // Create submission
        $submission_id = $submission_model->create($data);

        if (!$submission_id) {
            return new \WP_REST_Response(['error' => 'Failed to submit contact form'], 400);
        }

        // Send email to member
        $this->send_contact_email($member, $data);

        // Send notification to admin if enabled
        if (get_option('dom5md_contact_form_email_admin', true)) {
            $this->send_admin_notification($member, $data);
        }

        return new \WP_REST_Response([
            'success' => true,
            'message' => __('Message sent successfully!', 'dom5-member-directory')
        ]);
    }

    /**
     * Send contact email to member
     */
    private function send_contact_email(object $member, array $data): void
    {
        $subject = sprintf(
            __('New message from %s via Member Directory', 'dom5-member-directory'),
            $data['sender_name']
        );

        $message = sprintf(
            __("Hello %s,\n\nYou have received a new message through your member directory profile:\n\nFrom: %s <%s>\nMessage:\n%s\n\nYou can reply directly to this email to respond to the sender.\n\nBest regards,\n%s", 'dom5-member-directory'),
            $member->first_name,
            $data['sender_name'],
            $data['sender_email'],
            $data['message'],
            get_bloginfo('name')
        );

        $headers = [
            'Content-Type: text/plain; charset=UTF-8',
            'Reply-To: ' . $data['sender_name'] . ' <' . $data['sender_email'] . '>',
        ];

        wp_mail($member->email, $subject, $message, $headers);
    }

    /**
     * Send admin notification
     */
    private function send_admin_notification(object $member, array $data): void
    {
        $admin_email = get_option('admin_email');

        $subject = sprintf(
            __('New contact submission for %s %s', 'dom5-member-directory'),
            $member->first_name,
            $member->last_name
        );

        $message = sprintf(
            __("A new contact form submission has been received:\n\nMember: %s %s (%s)\nFrom: %s <%s>\nMessage:\n%s\n\nView all submissions: %s", 'dom5-member-directory'),
            $member->first_name,
            $member->last_name,
            $member->email,
            $data['sender_name'],
            $data['sender_email'],
            $data['message'],
            admin_url('admin.php?page=dom5-member-directory-submissions')
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Get member schema for validation
     */
    private function get_member_schema(): array
    {
        return [
            'first_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'last_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'email' => [
                'required' => true,
                'type' => 'string',
                'format' => 'email',
                'sanitize_callback' => 'sanitize_email',
            ],
            'profile_image_id' => [
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'cover_image_id' => [
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'address' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'favorite_color' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_hex_color',
            ],
            'status' => [
                'type' => 'string',
                'enum' => ['active', 'draft'],
                'default' => 'draft',
            ],
        ];
    }

    /**
     * Get team schema for validation
     */
    private function get_team_schema(): array
    {
        return [
            'name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'short_description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
        ];
    }

    /**
     * Get contact form schema for validation
     */
    private function get_contact_schema(): array
    {
        return [
            'member_id' => [
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'sender_name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'sender_email' => [
                'required' => true,
                'type' => 'string',
                'format' => 'email',
                'sanitize_callback' => 'sanitize_email',
            ],
            'message' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
        ];
    }
}
