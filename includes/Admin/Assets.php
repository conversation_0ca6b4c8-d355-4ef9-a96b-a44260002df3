<?php

namespace DOM5MemberDirectory\Admin;

class Assets {
    private $pages = ['settings_page_dom5-member-directory'];
    public function __construct() {
        add_action('admin_enqueue_scripts', [$this, 'plugin_scripts']);
    }

    public function plugin_scripts($hook) {
        if( in_array($hook, $this->pages) ){
            $dependencies = include_once DOM5MD_ASSETS_DIR_PATH . 'js/dom5-member-directory.core.min.asset.php';
            wp_enqueue_style('dom5-member-directory-admin', DOM5MD_ASSETS_URI . 'css/dom5-member-directory.css', [], $dependencies['version'], 'all');
            wp_enqueue_script(
                'dom5-member-directory',
                DOM5MD_ASSETS_URI . 'js/dom5-member-directory.core.min.js',
                array_merge($dependencies['dependencies'], ['regenerator-runtime']),
                $dependencies['version'],
                true
            );
        }
    }
}