<?php

declare(strict_types=1);

namespace DOM5MemberDirectory\Admin;

/**
 * Admin assets handler
 *
 * Manages CSS and JavaScript loading for admin pages
 */
class Assets
{
    /**
     * Admin pages where assets should be loaded
     */
    private array $pages = [
        'toplevel_page_dom5-member-directory',
        'member-directory_page_dom5-member-directory-members',
        'member-directory_page_dom5-member-directory-teams',
        'member-directory_page_dom5-member-directory-submissions',
        'member-directory_page_dom5-member-directory-settings',
    ];

    public function __construct()
    {
        add_action('admin_enqueue_scripts', [$this, 'plugin_scripts']);
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function plugin_scripts(string $hook): void
    {
        if (!in_array($hook, $this->pages)) {
            return;
        }
        $asset_file = DOM5MD_ASSETS_DIR_PATH . 'js/dom5-member-directory.core.min.asset.php';

        if( !file_exists($asset_file) ) {
            return;
        }

        $asset = include_once $asset_file;

        wp_enqueue_style(
            'dom5-member-directory-admin',
            DOM5MD_ASSETS_URI . 'js/dom5-member-directory.core.min.css',
            ['wp-color-picker'],
            $asset['version']
        );

        wp_enqueue_media();
        // wp_enqueue_style('wp-color-picker');
        // wp_enqueue_script('wp-color-picker');

        // Enqueue main admin script
        wp_enqueue_script(
            'dom5-member-directory-admin',
            DOM5MD_ASSETS_URI . 'js/dom5-member-directory.core.min.js',
            array_merge($asset['dependencies'], ['wp-color-picker']),
            $asset['version'],
            true
        );

        $this->localize_scripts($hook);
    }

    /**
     * Localize scripts with data
     */
    public function localize_scripts(string $hook): void
    {
        if (!in_array($hook, $this->pages)) {
            return;
        }

        $localized_data = [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'restUrl' => rest_url('dom5md/v1/'),
            'nonce' => wp_create_nonce('dom5_member_directory_nonce'),
            'restNonce' => wp_create_nonce('wp_rest'),
            'currentPage' => $this->get_current_page($hook),
            'siteUrl' => site_url(),
            'route_path' => parse_url( admin_url(), PHP_URL_PATH ),
            'singleMemberPageUrl' => get_option('dom5md_single_member_page_id') ? get_permalink(get_option('dom5md_single_member_page_id')) : home_url('/single-member'),
            'strings' => [
                'confirmDelete' => __('Are you sure you want to delete this item?', 'dom5-member-directory'),
                'saveSuccess' => __('Item saved successfully!', 'dom5-member-directory'),
                'saveError' => __('Error saving item. Please try again.', 'dom5-member-directory'),
                'deleteSuccess' => __('Item deleted successfully!', 'dom5-member-directory'),
                'deleteError' => __('Error deleting item. Please try again.', 'dom5-member-directory'),
                'loading' => __('Loading...', 'dom5-member-directory'),
                'noResults' => __('No results found.', 'dom5-member-directory'),
            ],
            'settings' => [
                'membersPerPage' => get_option('dom5md_members_per_page', 12),
                'teamsPerPage' => get_option('dom5md_teams_per_page', 12),
                'enableContactForm' => get_option('dom5md_enable_contact_form', true),
            ],
        ];

        wp_localize_script(
            'dom5-member-directory-admin',
            'dom5MemberDirectory',
            $localized_data
        );
    }

    /**
     * Get current page identifier from hook
     */
    private function get_current_page(string $hook): string
    {
        $page_map = [
            'toplevel_page_dom5-member-directory' => 'dashboard',
            'member-directory_page_dom5-member-directory-members' => 'members',
            'member-directory_page_dom5-member-directory-teams' => 'teams',
            'member-directory_page_dom5-member-directory-submissions' => 'submissions',
            'member-directory_page_dom5-member-directory-settings' => 'settings',
        ];

        return $page_map[$hook] ?? 'dashboard';
    }

    /**
     * Enqueue frontend assets
     */
    public function frontend_scripts(): void
    {
        // Only load on pages that need the member directory
        if (!$this->is_member_directory_page()) {
            return;
        }

        $asset_file = DOM5MD_ASSETS_DIR_PATH . 'js/dom5-member-directory.frontend.min.asset.php';

        if (file_exists($asset_file)) {
            $dependencies = include_once $asset_file;
        } else {
            $dependencies = [
                'dependencies' => ['wp-element', 'wp-api-fetch'],
                'version' => DOM5MD_VERSION,
            ];
        }

        // Enqueue frontend styles
        wp_enqueue_style(
            'dom5-member-directory-frontend',
            DOM5MD_ASSETS_URI . 'css/dom5-member-directory-frontend.css',
            [],
            $dependencies['version']
        );

        // Enqueue frontend script
        wp_enqueue_script(
            'dom5-member-directory-frontend',
            DOM5MD_ASSETS_URI . 'js/dom5-member-directory.frontend.min.js',
            $dependencies['dependencies'],
            $dependencies['version'],
            true
        );

        // Localize frontend script
        wp_localize_script(
            'dom5-member-directory-frontend',
            'dom5MemberDirectoryFrontend',
            [
                'restUrl' => rest_url('dom5md/v1/'),
                'nonce' => wp_create_nonce('wp_rest'),
                'strings' => [
                    'loading' => __('Loading...', 'dom5-member-directory'),
                    'noResults' => __('No members found.', 'dom5-member-directory'),
                    'contactFormSuccess' => __('Message sent successfully!', 'dom5-member-directory'),
                    'contactFormError' => __('Error sending message. Please try again.', 'dom5-member-directory'),
                ],
            ]
        );
    }

    /**
     * Check if current page needs member directory assets
     */
    private function is_member_directory_page(): bool
    {
        // Add logic to detect member directory pages
        // This could be based on page templates, shortcodes, or custom post types
        return false; // Placeholder - implement based on frontend requirements
    }
}