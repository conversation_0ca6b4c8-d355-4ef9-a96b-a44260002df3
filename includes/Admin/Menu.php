<?php

namespace DOM5MemberDirectory\Admin;

class Menu {
    /**
	 * add plugin menu page and submenu pages
	 */
	public function __construct()
	{
		add_action('admin_menu', [$this, 'admin_menu']);
	}

	/**
	 * add admin menu page
	 * @return hooks
	 */
	public function admin_menu()
	{
		add_submenu_page( 
			'options-general.php', 'DOM5 Member Directory', 'DOM5 Member Directory', 'manage_options', 'dom5-member-directory', array(&$this, 'load_main_template')
		);
	}
	public function load_main_template()
	{
		echo '<div id="dom5-member-directory-body" class="dom5-member-directory-body"></div>';
	}
}