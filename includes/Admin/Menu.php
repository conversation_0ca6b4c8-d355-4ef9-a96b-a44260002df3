<?php

declare(strict_types=1);

namespace DOM5MemberDirectory\Admin;

/**
 * Admin menu handler
 *
 * Creates WordPress admin menu structure for the plugin
 */
class Menu
{
    /**
     * Menu pages configuration
     */
    private array $menu_pages = [];

    public function __construct()
    {
        add_action('admin_menu', [$this, 'admin_menu']);
    }

    /**
     * Add admin menu pages
     */
    public function admin_menu(): void
    {
        // Main menu page
        $main_page = add_menu_page(
            __('Member Directory', 'dom5-member-directory'),
            __('Member Directory', 'dom5-member-directory'),
            'manage_options',
            'dom5-member-directory',
            [$this, 'load_main_template'],
            'dashicons-groups',
            30
        );

        // Members submenu
        $members_page = add_submenu_page(
            'dom5-member-directory',
            __('Members', 'dom5-member-directory'),
            __('Members', 'dom5-member-directory'),
            'manage_options',
            'dom5-member-directory-members',
            [$this, 'load_main_template']
        );

        // Teams submenu
        $teams_page = add_submenu_page(
            'dom5-member-directory',
            __('Teams', 'dom5-member-directory'),
            __('Teams', 'dom5-member-directory'),
            'manage_options',
            'dom5-member-directory-teams',
            [$this, 'load_main_template']
        );

        // Contact Submissions submenu
        $submissions_page = add_submenu_page(
            'dom5-member-directory',
            __('Contact Submissions', 'dom5-member-directory'),
            __('Contact Submissions', 'dom5-member-directory'),
            'manage_options',
            'dom5-member-directory-submissions',
            [$this, 'load_main_template']
        );

        // Settings submenu
        $settings_page = add_submenu_page(
            'dom5-member-directory',
            __('Settings', 'dom5-member-directory'),
            __('Settings', 'dom5-member-directory'),
            'manage_options',
            'dom5-member-directory-settings',
            [$this, 'load_main_template']
        );

        // Store page hooks for asset loading
        $this->menu_pages = [
            'main' => $main_page,
            'members' => $members_page,
            'teams' => $teams_page,
            'submissions' => $submissions_page,
            'settings' => $settings_page,
        ];
    }

    /**
     * Load main dashboard template
     */
    public function load_main_template(): void
    {
        echo '<div id="dom5-member-directory-body"></div>';
    }

    /**
     * Get menu page hooks
     */
    public function get_menu_pages(): array
    {
        return $this->menu_pages;
    }
}