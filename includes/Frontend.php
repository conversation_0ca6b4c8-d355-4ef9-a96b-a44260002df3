<?php

declare(strict_types=1);

namespace DOM5MemberDirectory;

/**
 * Frontend handler
 * 
 * Manages frontend functionality including shortcodes, rewrite rules, and templates
 */
class Frontend
{
    public function __construct()
    {
        add_action('init', [$this, 'init']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('template_redirect', [$this, 'template_redirect']);
    }

    /**
     * Initialize frontend functionality
     */
    public function init(): void
    {
        $this->add_rewrite_rules();
        $this->register_shortcodes();
    }

    /**
     * Add custom rewrite rules for member pages
     */
    private function add_rewrite_rules(): void
    {
        // Member single page: /member/first-name_last-name
        add_rewrite_rule(
            '^member/([a-zA-Z0-9_-]+)/?$',
            'index.php?dom5md_member_slug=$matches[1]',
            'top'
        );

        // Teams listing page
        add_rewrite_rule(
            '^teams/?$',
            'index.php?dom5md_teams_page=1',
            'top'
        );

        // Members listing page
        add_rewrite_rule(
            '^members/?$',
            'index.php?dom5md_members_page=1',
            'top'
        );

        // Add query vars
        add_filter('query_vars', [$this, 'add_query_vars']);

        // Flush rewrite rules if they haven't been flushed yet
        if (!get_option('dom5md_rewrite_rules_flushed')) {
            flush_rewrite_rules();
            update_option('dom5md_rewrite_rules_flushed', true);
        }

        // Debug: Add a simple test endpoint
        add_rewrite_rule(
            '^dom5md-test/?$',
            'index.php?dom5md_test=1',
            'top'
        );
    }

    /**
     * Add custom query variables
     */
    public function add_query_vars(array $vars): array
    {
        $vars[] = 'dom5md_member_slug';
        $vars[] = 'dom5md_teams_page';
        $vars[] = 'dom5md_members_page';
        $vars[] = 'dom5md_test';
        return $vars;
    }

    /**
     * Handle template redirects for custom pages
     */
    public function template_redirect(): void
    {
        // Test endpoint
        if (get_query_var('dom5md_test')) {
            echo 'DOM5 Member Directory rewrite rules are working!';
            exit;
        }

        // Member single page
        $member_slug = get_query_var('dom5md_member_slug');

        error_log( print_r( $member_slug, true ) );

        if ($member_slug) {
            $this->load_member_template($member_slug);
            return;
        }

        // Teams listing page
        if (get_query_var('dom5md_teams_page')) {
            $this->load_teams_template();
            return;
        }

        // Members listing page
        if (get_query_var('dom5md_members_page')) {
            $this->load_members_template();
            return;
        }
    }

    /**
     * Load member single template
     */
    private function load_member_template(string $slug): void
    {
        $member_model = new Models\Member();
        $member = $member_model->get_by_slug($slug);

        if (!$member || $member->status !== 'active') {
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            get_template_part('404');
            return;
        }

        // Set up global member data
        global $dom5md_member;
        $dom5md_member = $member;

        // Load template
        $this->load_template('single-member.php');
    }

    /**
     * Load teams listing template
     */
    private function load_teams_template(): void
    {
        $this->load_template('teams.php');
    }

    /**
     * Load members listing template
     */
    private function load_members_template(): void
    {
        $this->load_template('members.php');
    }

    /**
     * Load template file
     */
    private function load_template(string $template_name): void
    {
        // Look for template in theme first
        $theme_template = locate_template(['dom5-member-directory/' . $template_name]);
        
        if ($theme_template) {
            include $theme_template;
        } else {
            // Use plugin template
            $plugin_template = DOM5MD_TEMPLATES_DIR_PATH . $template_name;
            if (file_exists($plugin_template)) {
                include $plugin_template;
            }
        }
        exit;
    }

    /**
     * Register shortcodes
     */
    private function register_shortcodes(): void
    {
        add_shortcode('dom5md_members', [$this, 'members_shortcode']);
        add_shortcode('dom5md_teams', [$this, 'teams_shortcode']);
        add_shortcode('dom5md_member_contact', [$this, 'member_contact_shortcode']);
    }

    /**
     * Members listing shortcode
     */
    public function members_shortcode(array $atts): string
    {
        $atts = shortcode_atts([
            'limit' => 12,
            'status' => 'active',
            'team' => '',
            'orderby' => 'first_name',
            'order' => 'ASC',
        ], $atts);

        ob_start();
        ?>
        <div class="dom5md-members-grid" data-atts="<?php echo esc_attr(json_encode($atts)); ?>">
            <div class="dom5md-loading"><?php esc_html_e('Loading members...', 'dom5-member-directory'); ?></div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Teams listing shortcode
     */
    public function teams_shortcode(array $atts): string
    {
        $atts = shortcode_atts([
            'limit' => 12,
            'orderby' => 'name',
            'order' => 'ASC',
        ], $atts);

        ob_start();
        ?>
        <div class="dom5md-teams-grid" data-atts="<?php echo esc_attr(json_encode($atts)); ?>">
            <div class="dom5md-loading"><?php esc_html_e('Loading teams...', 'dom5-member-directory'); ?></div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Member contact form shortcode
     */
    public function member_contact_shortcode(array $atts): string
    {
        $atts = shortcode_atts([
            'member_id' => 0,
        ], $atts);

        if (empty($atts['member_id'])) {
            return '<p>' . esc_html__('Member ID is required.', 'dom5-member-directory') . '</p>';
        }

        ob_start();
        ?>
        <div class="dom5md-contact-form" data-member-id="<?php echo esc_attr($atts['member_id']); ?>">
            <form class="dom5md-contact-form-inner">
                <div class="form-group">
                    <label for="sender_name"><?php esc_html_e('Your Name', 'dom5-member-directory'); ?> *</label>
                    <input type="text" id="sender_name" name="sender_name" required>
                </div>
                
                <div class="form-group">
                    <label for="sender_email"><?php esc_html_e('Your Email', 'dom5-member-directory'); ?> *</label>
                    <input type="email" id="sender_email" name="sender_email" required>
                </div>
                
                <div class="form-group">
                    <label for="message"><?php esc_html_e('Message', 'dom5-member-directory'); ?> *</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="dom5md-submit-btn">
                        <?php esc_html_e('Send Message', 'dom5-member-directory'); ?>
                    </button>
                </div>
                
                <div class="dom5md-form-messages"></div>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts(): void
    {
        // Only enqueue on pages that need it
        if (!$this->should_enqueue_assets()) {
            return;
        }

        $asset_file = DOM5MD_ASSETS_DIR_PATH . 'js/dom5-member-directory.frontend.min.asset.php';
        
        if (file_exists($asset_file)) {
            $dependencies = include_once $asset_file;
        } else {
            $dependencies = [
                'dependencies' => ['wp-element', 'wp-api-fetch'],
                'version' => DOM5MD_VERSION,
            ];
        }

        // Enqueue frontend styles
        wp_enqueue_style(
            'dom5md-frontend',
            DOM5MD_ASSETS_URI . 'css/dom5-member-directory-frontend.css',
            [],
            $dependencies['version']
        );

        // Enqueue frontend script
        wp_enqueue_script(
            'dom5md-frontend',
            DOM5MD_ASSETS_URI . 'js/dom5-member-directory.frontend.min.js',
            $dependencies['dependencies'],
            $dependencies['version'],
            true
        );

        // Localize script
        wp_localize_script(
            'dom5md-frontend',
            'dom5MemberDirectoryFrontend',
            [
                'restUrl' => rest_url('dom5md/v1/'),
                'nonce' => wp_create_nonce('wp_rest'),
                'strings' => [
                    'loading' => __('Loading...', 'dom5-member-directory'),
                    'noResults' => __('No results found.', 'dom5-member-directory'),
                    'contactSuccess' => __('Message sent successfully!', 'dom5-member-directory'),
                    'contactError' => __('Error sending message. Please try again.', 'dom5-member-directory'),
                    'loadMore' => __('Load More', 'dom5-member-directory'),
                ],
            ]
        );
    }

    /**
     * Check if assets should be enqueued
     */
    private function should_enqueue_assets(): bool
    {
        global $post;

        // Check for shortcodes
        if ($post && (
            has_shortcode($post->post_content, 'dom5md_members') ||
            has_shortcode($post->post_content, 'dom5md_teams') ||
            has_shortcode($post->post_content, 'dom5md_member_contact')
        )) {
            return true;
        }

        // Check for custom query vars
        if (get_query_var('dom5md_member_slug') || 
            get_query_var('dom5md_teams_page') || 
            get_query_var('dom5md_members_page')) {
            return true;
        }

        return false;
    }
}
