<?php

declare(strict_types=1);

namespace DOM5MemberDirectory;

/**
 * Database handler for DOM5 Member Directory
 * 
 * Manages custom database tables for members, teams, relationships, and contact submissions
 */
class Database
{
    /**
     * Database version for schema updates
     */
    private const DB_VERSION = '1.0.0';

    /**
     * WordPress database object
     */
    private $wpdb;

    /**
     * Table names
     */
    private string $members_table;
    private string $teams_table;
    private string $member_teams_table;
    private string $contact_submissions_table;

    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;

        // Define table names with WordPress prefix
        $this->members_table = $this->wpdb->prefix . 'dom5md_members';
        $this->teams_table = $this->wpdb->prefix . 'dom5md_teams';
        $this->member_teams_table = $this->wpdb->prefix . 'dom5md_member_teams';
        $this->contact_submissions_table = $this->wpdb->prefix . 'dom5md_contact_submissions';

        // Hook into WordPress init to check for database updates
        add_action('init', [$this, 'check_database_version']);
    }

    /**
     * Check if database needs to be updated
     */
    public function check_database_version(): void
    {
        $installed_version = get_option('dom5md_db_version', '0.0.0');
        
        if (version_compare($installed_version, self::DB_VERSION, '<')) {
            $this->create_tables();
            update_option('dom5md_db_version', self::DB_VERSION);
        }
    }

    /**
     * Create all plugin database tables
     */
    public function create_tables(): void
    {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $this->create_members_table();
        $this->create_teams_table();
        $this->create_member_teams_table();
        $this->create_contact_submissions_table();
    }

    /**
     * Create members table
     */
    private function create_members_table(): void
    {
        $sql = "CREATE TABLE {$this->members_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            first_name varchar(100) NOT NULL,
            last_name varchar(100) NOT NULL,
            email varchar(255) NOT NULL,
            profile_image_id bigint(20) unsigned DEFAULT NULL,
            cover_image_id bigint(20) unsigned DEFAULT NULL,
            address text DEFAULT NULL,
            favorite_color varchar(7) DEFAULT NULL,
            status enum('active', 'draft') NOT NULL DEFAULT 'draft',
            slug varchar(255) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY email (email),
            UNIQUE KEY slug (slug),
            KEY status (status),
            KEY profile_image_id (profile_image_id),
            KEY cover_image_id (cover_image_id)
        ) {$this->get_charset_collate()};";

        dbDelta($sql);
    }

    /**
     * Create teams table
     */
    private function create_teams_table(): void
    {
        $sql = "CREATE TABLE {$this->teams_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            short_description text DEFAULT NULL,
            slug varchar(255) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name),
            UNIQUE KEY slug (slug)
        ) {$this->get_charset_collate()};";

        dbDelta($sql);
    }

    /**
     * Create member-teams relationship table
     */
    private function create_member_teams_table(): void
    {
        $sql = "CREATE TABLE {$this->member_teams_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            member_id bigint(20) unsigned NOT NULL,
            team_id bigint(20) unsigned NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY member_team (member_id, team_id),
            KEY member_id (member_id),
            KEY team_id (team_id),
            CONSTRAINT fk_member_teams_member_id 
                FOREIGN KEY (member_id) REFERENCES {$this->members_table}(id) 
                ON DELETE CASCADE,
            CONSTRAINT fk_member_teams_team_id 
                FOREIGN KEY (team_id) REFERENCES {$this->teams_table}(id) 
                ON DELETE CASCADE
        ) {$this->get_charset_collate()};";

        dbDelta($sql);
    }

    /**
     * Create contact submissions table
     */
    private function create_contact_submissions_table(): void
    {
        $sql = "CREATE TABLE {$this->contact_submissions_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            member_id bigint(20) unsigned NOT NULL,
            sender_name varchar(255) NOT NULL,
            sender_email varchar(255) NOT NULL,
            message text NOT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            status enum('unread', 'read', 'replied') NOT NULL DEFAULT 'unread',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY member_id (member_id),
            KEY sender_email (sender_email),
            KEY status (status),
            KEY created_at (created_at),
            CONSTRAINT fk_contact_submissions_member_id 
                FOREIGN KEY (member_id) REFERENCES {$this->members_table}(id) 
                ON DELETE CASCADE
        ) {$this->get_charset_collate()};";

        dbDelta($sql);
    }

    /**
     * Get WordPress charset and collation
     */
    private function get_charset_collate(): string
    {
        return $this->wpdb->get_charset_collate();
    }

    /**
     * Get table names for use in other classes
     */
    public function get_members_table(): string
    {
        return $this->members_table;
    }

    public function get_teams_table(): string
    {
        return $this->teams_table;
    }

    public function get_member_teams_table(): string
    {
        return $this->member_teams_table;
    }

    public function get_contact_submissions_table(): string
    {
        return $this->contact_submissions_table;
    }

    /**
     * Drop all plugin tables (for uninstall)
     */
    public function drop_tables(): void
    {
        $tables = [
            $this->contact_submissions_table,
            $this->member_teams_table,
            $this->teams_table,
            $this->members_table
        ];

        foreach ($tables as $table) {
            $this->wpdb->query("DROP TABLE IF EXISTS {$table}");
        }
    }
}
