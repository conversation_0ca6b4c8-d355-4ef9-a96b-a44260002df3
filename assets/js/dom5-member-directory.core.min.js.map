{"version": 3, "file": "dom5-member-directory.core.min.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA,MAAMA,GAAG,GAAGA,CAAA,KAAM;EACjB,OAAOC,oDAAA,aAAI,aAAe,CAAC;AAC5B,CAAC;AAED,iEAAeD,GAAG;;;;;;;;;;ACJlB;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;ACNuC;AACX;AACmB;AAC/CI,MAAM,CAACC,2BAA2B,GAAGF,6DAAW,CAAC,CAAC;AAElDG,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACxD,MAAMC,IAAI,GAAGF,QAAQ,CAACG,cAAc,CAAC,4BAA4B,CAAC;EAClE,MAAMC,IAAI,GAAGR,qDAAU,CAACM,IAAI,CAAC;EAE7BE,IAAI,CAACC,MAAM,CAACV,oDAAA,CAACD,gDAAG,MAAE,CAAC,CAAC;AACtB,CAAC,CAAC,C", "sources": ["webpack://dom5-member-directory/./react_app/App.jsx", "webpack://dom5-member-directory/external window \"React\"", "webpack://dom5-member-directory/external window \"ReactDOM\"", "webpack://dom5-member-directory/external window [\"wp\",\"hooks\"]", "webpack://dom5-member-directory/webpack/bootstrap", "webpack://dom5-member-directory/webpack/runtime/compat get default export", "webpack://dom5-member-directory/webpack/runtime/define property getters", "webpack://dom5-member-directory/webpack/runtime/hasOwnProperty shorthand", "webpack://dom5-member-directory/webpack/runtime/make namespace object", "webpack://dom5-member-directory/./react_app/index.js"], "sourcesContent": ["const App = () => {\n\treturn <h1>Hello World</h1>;\n}\n\nexport default App;", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"wp\"][\"hooks\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { createRoot } from \"react-dom\";\nimport App from \"./App.jsx\";\nimport { createHooks } from \"@wordpress/hooks\";\nwindow.dom5_member_directory_hooks = createHooks();\n\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n  const body = document.getElementById(\"dom5-member-directory-body\");\n  const root = createRoot(body);\n\n  root.render(<App />);\n});\n"], "names": ["App", "createElement", "createRoot", "createHooks", "window", "dom5_member_directory_hooks", "document", "addEventListener", "body", "getElementById", "root", "render"], "sourceRoot": ""}