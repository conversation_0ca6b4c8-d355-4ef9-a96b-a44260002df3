{"version": 3, "file": "dom5-member-directory.core.min.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAA2C;AACG;AAEvC,SAASE,QAAQA,CAAA,EAAG;EAC1B,OAAO,IAAIC,eAAe,CAACH,yDAAW,CAAC,CAAC,CAACI,MAAM,CAAC;AACjD;AAEA,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAA,IAAAC,UAAA;EACjB,MAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;EACxB,MAAMM,IAAI,IAAAF,UAAA,GAAGC,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC,cAAAH,UAAA,cAAAA,UAAA,GAAI,iBAAiB;EAEnD,OACCI,oDAAA,CAAAC,2CAAA,QACCD,oDAAA,CAACT,yDAAY;IAACO,IAAI,EAAEA;EAAK,CAAE,CAC1B,CAAC;AAEL,CAAC;AAED,iEAAeH,GAAG;;;;;;;;;;;;;;;;;AClBlB,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;EAC5B,OACIF,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,oBAAsB,CAC5B,CAAC;AAEX,CAAC;AAED,iEAAeE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRqB;AACJ;AACjB;AACJ;AACyB;AACnB;AACmC;AAClC;AACsB;AAElD,MAAMW,KAAK,GAAG;EACjB,uBAAuB,EAAE;IACrBC,KAAK,EAAEH,mDAAE,CAAC,mBAAmB,EAAE,uBAAuB,CAAC;IACvDI,GAAG,EAAE,mBAAmB;IACxBC,SAAS,EAAEhB,oDAAA,CAACM,yDAAe,MAAE,CAAC;IAC9BW,IAAI,EAAE;EACV,CAAC;EACD,+BAA+B,EAAE;IAC7BH,KAAK,EAAEH,mDAAE,CAAC,SAAS,EAAE,uBAAuB,CAAC;IAC7CI,GAAG,EAAE,SAAS;IACdC,SAAS,EAAEhB,oDAAA,CAACO,gDAAO,MAAE,CAAC;IACtBU,IAAI,EAAE;EACV,CAAC;EACD,6BAA6B,EAAE;IAC3BH,KAAK,EAAEH,mDAAE,CAAC,OAAO,EAAE,uBAAuB,CAAC;IAC3CI,GAAG,EAAE,OAAO;IACZC,SAAS,EAAEhB,oDAAA,CAACQ,8CAAK,MAAE,CAAC;IACpBS,IAAI,EAAE;EACV,CAAC;EACD,mCAAmC,EAAE;IACjCH,KAAK,EAAEH,mDAAE,CAAC,qBAAqB,EAAE,uBAAuB,CAAC;IACzDI,GAAG,EAAE,aAAa;IAClBC,SAAS,EAAEhB,oDAAA,CAACE,2DAAiB,MAAE,CAAC;IAChCe,IAAI,EAAE;EACV,CAAC;EACD,gCAAgC,EAAE;IAC9BH,KAAK,EAAEH,mDAAE,CAAC,UAAU,EAAE,uBAAuB,CAAC;IAC9CI,GAAG,EAAE,UAAU;IACfC,SAAS,EAAEhB,oDAAA,CAACS,iDAAQ,MAAE,CAAC;IACvBQ,IAAI,EAAE;EACV;AACJ,CAAC;AAED,MAAM1B,YAAY,GAAGA,CAAC;EAAEO;AAAK,CAAC,KAAK;EACjC,OACEE,oDAAA,CAAAC,2CAAA,QACED,oDAAA,CAACG,gDAAM,QACLH,oDAAA,CAACI,+CAAK;IAACc,IAAI,EAAEN,8DAAU,GAAG,WAAY;IAACO,OAAO,EAAEnB,oDAAA,CAACoB,MAAM;MAACtB,IAAI,EAAEA;IAAK,CAAE;EAAE,GACrEE,oDAAA,CAACI,+CAAK;IAACiB,KAAK;IAACF,OAAO,EAAEN,KAAK,CAACf,IAAI,CAAC,EAAEkB;EAAU,CAAE,CAC1C,CACD,CACR,CAAC;AAEP,CAAC;AAED,iEAAezB,YAAY,EAAC;AACrB,MAAM6B,MAAM,GAAGA,CAAC;EAAEtB;AAAK,CAAC,KAAK;EAChC,OACIE,oDAAA,CAAAC,2CAAA,QACID,oDAAA,CAACU,6EAAW,MAAE,CAAC,EACfV,oDAAA,cACIA,oDAAA,CAACK,gDAAM,MAAE,CACR,CACP,CAAC;AAEX,CAAC;;;;;;;;;;;;;;;;;ACjED,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,OACIN,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,kBAAoB,CAC1B,CAAC;AAEX,CAAC;AAED,iEAAeM,eAAe;;;;;;;;;;;;;;;;;ACR9B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAClB,OACIP,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,SAAW,CACjB,CAAC;AAEX,CAAC;AAED,iEAAeO,OAAO;;;;;;;;;;;;;;;;;ACRtB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EACnB,OACIT,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,UAAY,CAClB,CAAC;AAEX,CAAC;AAED,iEAAeS,QAAQ;;;;;;;;;;;;;;;;;ACRvB,MAAMD,KAAK,GAAGA,CAAA,KAAM;EAChB,OACIR,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,OAAS,CACf,CAAC;AAEX,CAAC;AAED,iEAAeQ,KAAK;;;;;;;;;;;;;;ACRb,MAAM;EAAEI;AAAW,CAAC,GAAGU,MAAM,CAACC,mBAAmB;;;;;;;;;;;;;;;;;;;;;ACAhB;AACU;AACd;AACC;AAErC,MAAMb,WAAW,GAAGA,CAAA,KAAM;EAAA,IAAAd,UAAA;EACtB,MAAMC,KAAK,GAAGL,8CAAQ,CAAC,CAAC;EACxB,MAAMM,IAAI,IAAAF,UAAA,GAAGC,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC,cAAAH,UAAA,cAAAA,UAAA,GAAI,iBAAiB;EACnD,OACII,oDAAA,CAAAC,2CAAA,QACID,oDAAA,aAAI,8BAAgC,CAAC,EACrCA,oDAAA,aACKyB,MAAM,CAACC,OAAO,CAACb,oDAAK,CAAC,CAACc,GAAG,CAAC,CAAC,CAACZ,GAAG,EAAEa,KAAK,CAAC,KACpC5B,oDAAA;IAAI6B,SAAS,EAAEd,GAAG,KAAKjB,IAAI,GAAG,QAAQ,GAAG,EAAG;IAACiB,GAAG,EAAEA;EAAI,GACnDf,oDAAA,CAACwB,8CAAI;IAACM,EAAE,EAAElB,6CAAU,GAAG,kBAAkBG,GAAG;EAAG,GAC7Ca,KAAK,CAACd,KACD,CACN,CACP,CACD,CACN,CAAC;AAEX,CAAC;AAED,iEAAeJ,WAAW;;;;;;;;;;ACxBb;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,yBAAyB;AACzB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,KAAK,kCAAkC,KAAK;AAChG;AACA;AACA;AACA,kDAAkD;AAClD;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,uCAAuC;AACvC;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,gBAAgB;AAC7C,kBAAkB;AAClB;AACA;AACA;AACA;AACA,yDAAyD,KAAK;AAC9D;AACA;AACA;AACA,wDAAwD,IAAI;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,eAAe;AAC5E;AACA,kBAAkB;AAClB;AACA;AACA;AACA,6DAA6D,eAAe;AAC5E;AACA,kBAAkB;AAClB;AACA;AACA;AACA,2DAA2D,aAAa;AACxE;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,8DAA8D,gBAAgB;AAC9E;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB;AAClB;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,mEAAmE,iBAAiB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,mEAAmE,iBAAiB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AC9Oa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,qCAAqC;;AAErC;AACA;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB;;AAEA;AACA,sEAAsE;AACtE,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC,IAAI;AACJ;AACA;;AAEA,WAAW;AACX;;AAEA;AACA;AACA,sBAAsB;AACtB;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,oMAAoM,cAAc;AAClN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kCAAkC;AAClC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,uEAAuE;AACvE;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,+CAA+C;AAC/C;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB;AACpB,0BAA0B;AAC1B,iCAAiC;;;;;;;;;;;AC/NjC;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,cAAc;AACf;AACA,yCAAyC;AACzC,QAAQ,yDAAyD;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,0CAA0C;AAC7D;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,mBAAmB,0CAA0C;AAC7D;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,uCAAuC;AAC1D;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA,UAAU,yBAAyB;AACnC;AACA;AACA,QAAQ,wBAAwB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,QAAQ,wBAAwB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE;AACnE;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF;AACpF,QAAQ,2DAA2D;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,oCAAoC;AACrE;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,2CAA2C;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,8CAA8C;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,8CAA8C;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8FAA8F;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,GAAG;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wCAAwC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAA0B;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,kBAAkB,uBAAuB,WAAW;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,KAAK;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,aAAa;AACrB;AACA;AACA;AACA,kBAAkB,uBAAuB;AACzC;AACA;AACA;AACA;AACA,QAAQ,iEAAiE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,KAAK,mCAAmC,0BAA0B,wIAAwI,0BAA0B;AACzP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,IAAI;AACpE;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,KAAK,mCAAmC,0BAA0B,wIAAwI,0BAA0B;AACvP;AACA;AACA,+FAA+F;AAC/F;AACA;AACA,oBAAoB,2CAA2C;AAC/D;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,uBAAuB,MAAM,gHAAgH,MAAM;AACnJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,8BAA8B,KAAK,2CAA2C,MAAM,YAAY;AAChG;AACA,IAAI,yCAAyC,KAAK;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,IAAI;AACJ;AACA;AACA;AACA;AACA,8BAA8B,0BAA0B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,UAAU,iBAAiB;AAC3B;AACA;AACA,sBAAsB;AACtB,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,wCAAwC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,aAAa;AACb;AACA;AACA;AACA,4BAA4B,UAAU;AACtC;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,oDAAoD,YAAY,IAAI;AACpE;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0BAA0B;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,UAAU;AAClC;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,yBAAyB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,SAAS,IAAI,WAAW;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,SAAS;AACT,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,4HAA4H;AAC5H;AACA;AACA,kBAAkB,YAAY,IAAI,oCAAoC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,cAAc,gCAAgC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,aAAa;AACtD,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,QAAQ;AACR;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,QAAQ;AACR,cAAc,gCAAgC;AAC9C;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU,kCAAkC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,mFAAmF,yDAAyD;AAC5I;AACA,kCAAkC,oCAAoC;AACtE,SAAS;AACT,UAAU;AACV;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,WAAW;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,gCAAgC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,eAAe;AACf;AACA,UAAU,qBAAqB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,oCAAoC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,0BAA0B;AAChE,UAAU;AACV;AACA;AACA;AACA,UAAU,0BAA0B;AACpC;AACA;AACA;AACA;AACA;AACA,6CAA6C,WAAW;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,+CAA+C,WAAW;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,8DAA8D,WAAW;AACzE;AACA,QAAQ;AACR;AACA;AACA;AACA,wCAAwC,gBAAgB;AACxD,YAAY;AACZ;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,kCAAkC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,kBAAkB,mCAAmC;AACrD;AACA;AACA;AACA;AACA;AACA,UAAU,gCAAgC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU,qBAAqB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,8DAA8D,WAAW;AACzE;AACA,QAAQ;AACR;AACA;AACA;AACA,wCAAwC,gBAAgB;AACxD,YAAY;AACZ;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,sCAAsC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qDAAqD;AACrD;AACA;AACA,QAAQ,mCAAmC;AAC3C,QAAQ;AACR;AACA;AACA,yDAAyD;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,kBAAkB,mCAAmC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,IAAI;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,IAAI;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,eAAe,KAAK,iBAAiB;AAChF;AACA;AACA;AACA,kBAAkB,UAAU;AAC5B;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,8CAA8C;AACxE;AACA;AACA;AACA;AACA,8CAA8C,UAAU;AACxD;AACA,UAAU,iBAAiB;AAC3B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,0BAA0B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR,iBAAiB;AACjB,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI;AACR;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,QAAQ;AACxD,YAAY,0CAA0C;AACtD;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,SAAS;AACT;AACA,yBAAyB;AACzB;AACA;AACA;AACA,MAAM;AACN,gDAAgD,6BAA6B;AAC7E,YAAY,kCAAkC;AAC9C;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,SAAS;AACT;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iCAAiC;AACjC;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI;AACR;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,QAAQ;AAClD,MAAM;AACN,0CAA0C,6BAA6B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN,0CAA0C,6BAA6B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,sBAAsB,qCAAqC;AAC3D;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,yCAAyC;AAC3E,WAAW;AACX;AACA,wBAAwB;AACxB;AACA;AACA,WAAW;AACX;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,WAAW;AACX,4CAA4C,yCAAyC,IAAI;AACzF;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,yCAAyC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,+BAA+B,gCAAgC,IAAI;AACnE,wCAAwC,yCAAyC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,OAAO,qDAAqD,gBAAgB,EAAE,YAAY;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,6BAA6B,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,2CAA2C,yBAAyB;AACpE;AACA;AACA;AACA;AACA,yCAAyC,sBAAsB;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,IAAI,EAAE,KAAK,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,0BAA0B,aAAa;AACvC,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,QAAQ;AAClB;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,QAAQ;AAClE;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,kBAAkB,iBAAiB,2BAA2B,IAAI;AAClE;AACA,MAAM;AACN;AACA;AACA,uCAAuC,cAAc;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,sBAAsB,iBAAiB,2BAA2B,kBAAkB,wGAAwG,kBAAkB;AAC9M;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,aAAa,gCAAgC;AACxE;AACA;AACA;AACA,QAAQ,oCAAoC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAA0B;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,UAAU;AAClB;AACA;AACA;AACA;AACA;AACA,4DAA4D,gBAAgB,EAAE,YAAY;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA,0CAA0C;AAC1C,MAAM;AACN,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,+BAA+B,yCAAyC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,8BAA8B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,qBAAqB;AACrB,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,KAAK,cAAc,eAAe;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,QAAQ;AACR,iBAAiB;AACjB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV,mBAAmB;AACnB;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA,IAAI;AACJ,aAAa;AACb,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,eAAe;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,UAAU,0BAA0B;AACpC;AACA;AACA,mCAAmC,6BAA6B;AAChE;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,qBAAqB;AAC7B;AACA;AACA;AACA;AACA;AACA,UAAU,yBAAyB;AACnC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;AACH,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,GAAG,IAAI;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN;AACA;AACA;AACA;AACA;AACA,mCAAmC,QAAQ,cAAc,SAAS,gDAAgD,QAAQ;AAC1H,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA,6BAA6B,QAAQ,wBAAwB,SAAS;AACtE,IAAI;AACJ;AACA,4CAA4C,SAAS;AACrD,IAAI;AACJ;AACA;AACA,mCAAmC,sBAAsB,cAAc,SAAS,iDAAiD,QAAQ;AACzI,MAAM;AACN,gDAAgD,qBAAqB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,QAAQ;AAC3C;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,sBAAsB,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,4DAA4D;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,sEAAsE,MAAM;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACgC;;AAEhC;AAC+B;AAC/B,wBAAwB,gDAAmB;AAC3C;AACA,6BAA6B,gDAAmB;AAChD;AACA,4BAA4B,gDAAmB;AAC/C;AACA,CAAC;AACD;AACA,sBAAsB,gDAAmB;AACzC;AACA;AACA;AACA,mBAAmB,gDAAmB;AACtC;AACA,wBAAwB,gDAAmB;AAC3C;AACA;AACA;AACA,sBAAsB,gDAAmB;AACzC;AACA;AACA;AACA,mBAAmB,gDAAmB;AACtC;AACA;AACA;AACA,CAAC;AACD;AACA,wBAAwB,gDAAmB;AAC3C;AACA;;AAEA;AACgC;AAChC,uBAAuB,WAAW,IAAI;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,sBAAsB,EAAE,6CAAiB;AACjD,QAAQ,yBAAyB,wBAAwB,UAAU;AACnE;AACA;AACA;AACA;AACA,gCAAgC,wCAAwC;AACxE;AACA;AACA,SAAS,6CAAiB;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,6CAAiB;AAC1B;AACA;AACA,SAAS,6CAAiB;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,WAAW;AACnB,SAAS,0CAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,6CAAiB;AAClC;AACA,IAAI,kDAAsB;AAC1B;AACA;AACA;AACA,QAAQ,cAAc,EAAE,6CAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,6CAAiB;AAC3C,QAAQ,sBAAsB,EAAE,6CAAiB;AACjD,QAAQ,UAAU,EAAE,6CAAiB;AACrC,QAAQ,6BAA6B;AACrC;AACA,kBAAkB,yCAAa;AAC/B;AACA;AACA,GAAG;AACH,iBAAiB,8CAAkB;AACnC,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gDAAoB;AACxC;AACA,SAAS,6CAAiB;AAC1B;AACA;AACA,eAAe,6CAAiB;AAChC;AACA,2BAA2B,gDAAoB,2BAA2B,gBAAgB;AAC1F;AACA;AACA;AACA;AACA,QAAQ,UAAU,EAAE,6CAAiB;AACrC;AACA;AACA;AACA,+BAA+B,WAAW,IAAI;AAC9C,QAAQ,UAAU,EAAE,6CAAiB;AACrC,QAAQ,6BAA6B;AACrC;AACA,SAAS,0CAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,YAAY,EAAE,6CAAiB;AACvC,QAAQ,yBAAyB,EAAE,6CAAiB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,eAAe,wBAAwB,WAAW;;AAE5H,wCAAwC,WAAW,qBAAqB,8BAA8B,WAAW,IAAI;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iPAAiP,mBAAmB,kBAAkB,2BAA2B;AACjT;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,6BAA6B;AACnE;AACA;AACA;AACA,qCAAqC,kBAAkB,EAAE,gBAAgB,EAAE,cAAc;AACzF;AACA;AACA;AACA,yCAAyC,kBAAkB,EAAE,gBAAgB,EAAE,cAAc;AAC7F;AACA;AACA;AACA;AACA,iCAAiC;AACjC,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,gDAAoB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,cAAc,EAAE,iBAAiB;AAClF;AACA;AACA,oBAAoB;AACpB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,gDAAoB,CAAC,2CAAe,wBAAwB,gDAAoB,gBAAgB,OAAO,iBAAiB,MAAM,oBAAoB,gDAAoB,4HAA4H,gDAAoB,WAAW,mBAAmB,gDAAgD,gDAAoB,WAAW,mBAAmB;AACpd;AACA,yBAAyB,gDAAoB,CAAC,2CAAe,wBAAwB,gDAAoB,+DAA+D,gDAAoB,SAAS,SAAS,uBAAuB,oCAAoC,gDAAoB,UAAU,kBAAkB;AACzT;AACA,0CAA0C,gDAAoB;AAC9D,wCAAwC,4CAAgB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,gDAAoB,0BAA0B,gCAAgC,kBAAkB,gDAAoB;AAC7K;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,+BAA+B;AACxD,0BAA0B,6CAAiB;AAC3C;AACA;AACA;AACA,yBAAyB,gDAAoB,0BAA0B,qBAAqB;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,4BAA4B;AAChD;AACA;AACA;AACA;AACA;AACA,cAAc,8BAA8B;AAC5C;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR,mCAAmC,gDAAoB;AACvD,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA,6BAA6B,gDAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,uHAAuH,gDAAoB;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,GAAG;AACH;AACA;AACA,YAAY,UAAU;AACtB;AACA;AACA,YAAY,6CAAiB;AAC7B;AACA;AACA;AACA;AACA,cAAc,6CAAiB;AAC/B;AACA;AACA;AACA;AACA,cAAc,6CAAiB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,UAAU;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,8CAAkB;AACrC;AACA,GAAG;AACH,SAAS,0CAAc;AACvB,aAAa,uCAAuC;AACpD;AACA;AACA;AACA;AACA,QAAQ,sBAAsB;AAC9B;AACA;AACA,SAAS,0CAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,6CAAiB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,6CAAiB;AAC/B;AACA;AACA;AACA,cAAc,6CAAiB;AAC/B;AACA;AACA;AACA;AACA,QAAQ,mBAAmB;AAC3B;AACA,oCAAoC,2CAAe;AACnD,wBAAwB,8CAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,+CAA+C;AAC3D;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,EAAE,4CAAgB;AAClB;AACA;AACA;AACA,GAAG;AACH,EAAE,4CAAgB;AAClB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,QAAQ,SAAS;AACjB;AACA,kBAAkB,yCAAa;AAC/B;AACA;AACA,GAAG;AACH,iBAAiB,8CAAkB;AACnC,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,QAAQ;AACR,oCAAoC,6BAA6B;AACjE;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gDAAoB;AACnC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,gDAAoB;AAClD;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gDAAoB;AACxC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,2CAAe;AAC7C,wCAAwC,2CAAe;AACvD,kCAAkC,2CAAe;AACjD;AACA,GAAG;AACH,kCAAkC,2CAAe;AACjD,oCAAoC,2CAAe;AACnD,wCAAwC,2CAAe;AACvD,oBAAoB,yCAAa;AACjC,iBAAiB,8CAAkB;AACnC,iBAAiB,gDAAgD;AACjE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,qNAAqN,iBAAiB;AACtO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,UAAU,kDAAsB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,2BAA2B,wBAAwB;AACnD,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA,EAAE,kDAAsB;AACxB,EAAE,4CAAgB;AAClB;AACA;AACA;AACA,GAAG;AACH,EAAE,4CAAgB;AAClB;AACA;AACA;AACA;AACA,QAAQ,kDAAsB;AAC9B;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,uBAAuB,wBAAwB;AAC/C,OAAO;AACP;AACA;AACA,GAAG;AACH,EAAE,4CAAgB;AAClB;AACA;AACA;AACA,GAAG;AACH,EAAE,4CAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH,kBAAkB,0CAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA,0BAA0B,0CAAc;AACxC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,yBAAyB,gDAAoB,CAAC,2CAAe,wBAAwB,gDAAoB,+BAA+B,0BAA0B,kBAAkB,gDAAoB,oCAAoC,cAAc,kBAAkB,gDAAoB,6BAA6B,4BAA4B,kBAAkB,gDAAoB,mCAAmC,kBAAkB,kBAAkB,gDAAoB;AAC1d;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB,gDAAoB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uCAAW;AACpC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB,yCAAa;AAChC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,8BAA8B,2CAAe;AAC7C;AACA;AACA,GAAG;AACH,iBAAiB,8CAAkB;AACnC;AACA,MAAM,kDAAsB;AAC5B,KAAK;AACL;AACA;AACA,EAAE,kDAAsB;AACxB,yBAAyB,gDAAoB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAmB,EAAE,6CAAiB;AAC9C;AACA;AACA;AACA;AACA,QAAQ,UAAU,EAAE,6CAAiB;AACrC,QAAQ,6BAA6B;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,4CAAgB;AAClB,qCAAqC,oCAAoC;AACzE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,0BAA0B,0CAAc;AACxC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,wBAAwB,0CAAc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;AACA;AACA,yBAAyB,SAAS,mCAAmC,SAAS,EAAE,OAAO,EAAE,KAAK;AAC9F;AACA;AACA;AACA;AACA,yBAAyB,gDAAoB,+BAA+B,0BAA0B,kBAAkB,gDAAoB,6BAA6B,kCAAkC;AAC3M;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB,gDAAoB,uBAAuB,uBAAuB,kBAAkB,gDAAoB;AACjI;AACA,uCAAuC,4CAAgB;AACvD;AACA;AACA,mBAAmB;AACnB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,kCAAkC;AAC5C;AACA;AACA;AACA;AACA;AACA,mDAAmD,iBAAiB;AACpE,gDAAgD,oBAAoB;AACpE,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP,mDAAmD,iBAAiB;AACpE,iDAAiD,wBAAwB;AACzE,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,mDAAmD,iBAAiB;AACpE;AACA,6DAA6D,kBAAkB;AAC/E,8DAA8D,kBAAkB;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,gDAAoB,0BAA0B,wCAAwC;AACnH;AACA;AACA,6BAA6B,gDAAoB,0BAA0B,0BAA0B;AACrG;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,yBAAyB,gDAAoB,CAAC,2CAAe;AAC7D;AACA;AACA;AACA,EAAE,2CAAe;AACjB,SAAS,iDAAqB;AAC9B;AACA;AACA;AACA,yBAAyB,2CAAe;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,oEAAoE;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,gDAAoB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,gDAAoB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,gDAAoB;AAC/B;AACA;;AAEA;AACiC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,QAAQ,sFAAsF,eAAe;AACvH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,oBAAoB;AAChC;AACA,+BAA+B,KAAK;AACpC,2BAA2B,OAAO;AAClC,2BAA2B,OAAO;AAClC,QAAQ;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACgC;;AAEhC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,sCAAsC,aAAa;AACnD;AACA;AACA;AACA,IAAI,2CAAe,EAAE,EAEhB;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,gCAAgC;AACvF;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,qCAAqC,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,gBAAgB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,8CAA8C,wCAAwC,IAAI;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,iDAAiD,yBAAyB,IAAI;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACgC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,cAAc;AACxD;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA,UAAU,UAAU;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,iCAAiC;AAC3C;AACA;AACA;AACA;AACA,kEAAkE,sBAAsB,IAAI,2BAA2B;AACvH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,YAAY,KAAK,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,6BAA6B,YAAY,IAAI,uBAAuB;AACpE;AACA;AACA;AACA;AACA;AACA,2BAA2B,KAAK;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,iCAAiC;AACjE;AACA,mCAAmC,iDAAiD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,4BAA4B,mBAAmB;AAC/C;AACA,6BAA6B,OAAO;AACpC,YAAY;AACZ,+BAA+B,UAAU,IAAI,iBAAiB;AAC9D,YAAY;AACZ,+BAA+B,SAAS,IAAI,4BAA4B;AACxE,YAAY;AACZ,+BAA+B,YAAY,IAAI;AAC/C;AACA,cAAc,GAAG,6BAA6B;AAC9C,YAAY;AACZ;AACA,iCAAiC,SAAS,IAAI,4DAA4D;AAC1G,cAAc;AACd,iCAAiC,SAAS;AAC1C;AACA,YAAY;AACZ;AACA,iCAAiC,SAAS,IAAI;AAC9C;AACA;AACA,2BAA2B;AAC3B,cAAc;AACd,iCAAiC,SAAS;AAC1C;AACA,YAAY;AACZ,+BAA+B,aAAa,IAAI,OAAO;AACvD;AACA,YAAY;AACZ,+BAA+B,WAAW,IAAI,+BAA+B;AAC7E;AACA,iCAAiC,4BAA4B;AAC7D;AACA;AACA,YAAY;AACZ,+BAA+B,iBAAiB,GAAG,EAAE,qBAAqB;AAC1E,YAAY;AACZ,4BAA4B,EAAE,qBAAqB;AACnD,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,iCAAiC;AACjE;AACA,mCAAmC,iDAAiD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,iCAAiC;AAC/D;AACA,iCAAiC,iDAAiD;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU,mBAAmB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,sCAAsC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,OAAO;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,OAAO;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,iBAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,UAAU;AACpB;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,YAAY;AACrD;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,YAAY;AACrD;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,+BAA+B;AACzC;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA,QAAQ;AACR;AACA,iCAAiC,+BAA+B;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,eAAe;AACf;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,aAAa,EAAE,WAAW,MAAM,uBAAuB,IAAI,OAAO;AAC7F;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,2BAA2B,aAAa,EAAE,WAAW,GAAG;AACxD;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,2BAA2B,aAAa,EAAE,WAAW,IAAI,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,WAAW,EAAE,WAAW,MAAM,uBAAuB,IAAI,OAAO;AAC3F;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA,4CAA4C,WAAW,EAAE,WAAW,GAAG;AACvE;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,2BAA2B,WAAW,EAAE,WAAW,IAAI,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,MAAM;AACN,uBAAuB;AACvB;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,cAAc;AACtB;AACA;AACA;AACA;AACA;AACA,kDAAkD,cAAc;AAChE;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,cAAc;AACtB,0CAA0C,gDAAoB;AAC9D;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA,UAAU,EAAE;AACZ;AACA;AACA;AACA;AACA,2BAA2B,gDAAoB,CAAC,2CAAe,mCAAmC,gDAAoB;AACtH;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA,IAAI;AACJ,2BAA2B,gDAAoB,CAAC,2CAAe,mCAAmC,gDAAoB,CAAC,2CAAe,wBAAwB,gDAAoB;AAClL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,+BAA+B;AACzC;AACA;AACA;AACA;AACA;AACA,YAAY,6BAA6B;AACzC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,sBAAsB;AAClC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC;AACA;AACA,kBAAkB,cAAc;AAChC;AACA,WAAW;AACX,kCAAkC;AAClC,UAAU;AACV,kCAAkC;AAClC;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,kDAAkD;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA,aAAa;AACb,iCAAiC;AACjC,YAAY;AACZ,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,+BAA+B;AAC/B,UAAU;AACV,+BAA+B;AAC/B;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,6BAA6B,YAAY;AACzC,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc;AAC1B;AACA,KAAK;AACL;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,sBAAsB,4BAA4B;AAClD,IAAI;AACJ,sBAAsB,gCAAgC;AACtD;AACA;AACA;AACA;AACA,QAAQ,UAAU;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,QAAQ;AACR,kBAAkB;AAClB;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,kBAAkB;AAClB,QAAQ;AACR,kBAAkB,UAAU;AAC5B;AACA;AACA,aAAa;AACb,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,mBAAmB;AACnB;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,0BAA0B,8BAA8B;AACxD,sBAAsB,mCAAmC;AACzD,wBAAwB,2BAA2B;AACnD;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA,IAAI;AACJ,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACgC;;AAEhC;AACgC;;AAEhC;AACgC;AAChC,uCAAuC,4CAAgB;AACvD;AACA;AACA,mBAAmB;AACnB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,eAAe;AACf;AACA,aAAa;AACb;AACA;AACA;AACA,6BAA6B,gDAAoB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,qCAAqC,gDAAoB;AACzD;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO,iBAAiB,MAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,gDAAoB,kBAAkB,qCAAqC,kBAAkB,gDAAoB,SAAS,SAAS,oBAAoB;AAClL;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,yBAAyB,gDAAoB;AAC7C;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB,gDAAoB,SAAS,SAAS,oBAAoB;AAC9E,oBAAoB,gDAAoB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,QAAQ,eAAe;AACvB;AACA;AACA;AACA,yBAAyB,gDAAoB,WAAW,YAAY,kBAAkB,gDAAoB,+BAA+B,gDAAoB,WAAW,kBAAkB,mBAAmB,gDAAoB;AACjO;AACA;AACA;AACA;AACA;AACA,qBAAqB,gDAAoB,yCAAyC,gDAAoB,+BAA+B,gDAAoB,WAAW,SAAS,wDAAwD,4CAA4C,gDAAoB;AACrS;;AAEA;AACgC;AAChC;AACA,yBAAyB,gDAAoB,kBAAkB,0CAA0C,wCAAwC,gDAAoB;AACrK;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO,iBAAiB,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,0HAA0H,gDAAoB,kCAAkC,wBAAwB;AACxM;AACA;AACA;AACA,iCAAiC,gDAAoB,2CAA2C,gDAAoB;AACpH,QAAQ,IAAI,uBAAuB;AACnC;AACA,sCAAsC,gDAAoB,2CAA2C,gDAAoB;AACzH,QAAQ,IAAI,eAAe;AAC3B;AACA,gDAAgD,gDAAoB,2CAA2C,gDAAoB;AACnI,QAAQ,IAAI;AACZ;AACA;AACA,WAAW;AACX;AACA,iLAAiL,uBAAuB;AACxM;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,IAAI,yCAAyC,MAAM,aAAa,SAAS;AACjH;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,QAAQ,kBAAkB,SAAS,EAAE,KAAK,gEAAgE,SAAS,IAAI,KAAK;AAClJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,kCAAkC,0BAA0B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,0BAA0B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA,MAAM;AACN;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA,gBAAgB,4BAA4B;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,0BAA0B,wCAAwC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,kBAAkB;AAChD;AACA;AACA;AACA;AACA;AACA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,sCAAsC,2BAA2B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,iCAAiC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,4CAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,SAAS,EAAE,aAAa;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,QAAQ;AACzC;AACA,yBAAyB,YAAY,EAAE,eAAe;AACtD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,IAAI;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,6CAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,6CAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gDAAoB;AAC3C;AACA;AACA,gBAAgB,6CAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,6CAAiB;AAC1C,0CAA0C,2CAAe;AACzD,4CAA4C,2CAAe;AAC3D,QAAQ,4DAA4D;AACpE,YAAY,yCAAa;AACzB,EAAE,4CAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,0DAA0D,gBAAgB;AAC1E;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,EAAE,4CAAgB;AAClB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,iDAAiD;AACzD,QAAQ,iCAAiC;AACzC;AACA,mBAAmB,0CAAc;AACjC;AACA;AACA;AACA,yBAAyB,gDAAoB,CAAC,2CAAe,0DAA0D,gDAAoB,YAAY,2BAA2B,uBAAuB,4DAA4D,gDAAoB,WAAW,2CAA2C;AAC/U,OAAO,WAAW,kDAAkD,gDAAoB,sBAAsB,cAAc,oBAAoB,gDAAoB,WAAW,cAAc;AAC7L;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,QAAQ,SAAS;AACjB,gBAAgB,0CAAc;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gDAAoB,0BAA0B,iCAAiC;AACxG;AACA;AACA,QAAQ,yBAAyB;AACjC,oDAAoD,2CAAe;AACnE,EAAE,4CAAgB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,QAAQ,yBAAyB;AACjC,QAAQ,WAAW;AACnB,QAAQ,sBAAsB;AAC9B,0BAA0B,0CAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,0CAAc;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,0CAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,0CAAc;AAClC;AACA;AACA;AACA;AACA,yBAAyB,gDAAoB,CAAC,2CAAe,iDAAiD,gDAAoB,WAAW,qEAAqE,+CAA+C,gDAAoB,WAAW,6DAA6D,8BAA8B,WAAW;AACtY;AACA;AACA,oBAAoB,gDAAoB,WAAW,cAAc;AACjE;AACA;AACA;AACA,QAAQ,0BAA0B;AAClC;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gDAAoB,CAAC,2CAAe;AAC7D;AACA;AACA;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;AACA,oDAAoD,QAAQ;AAC5D;AACA;AACA;AACA;AACA,6BAA6B,gDAAoB,SAAS,oCAAoC;AAC9F;AACA;AACA,6BAA6B,gDAAoB,YAAY,cAAc;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,gDAAoB,WAAW,4CAA4C;AAChJ;AACA;AACA;AACA;AACA,+BAA+B,gDAAoB;AACnD;AACA;AACA,mCAAmC,KAAK;AACxC;AACA,uCAAuC;AACvC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,2BAA2B,gDAAoB,WAAW,8CAA8C;AACxG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,QAAQ,0CAA0C;AAClD,QAAQ,yBAAyB;AACjC;AACA;AACA;AACA;AACA;AACA,EAAE,4CAAgB;AAClB;AACA,GAAG;AACH,uBAAuB,0CAAc;AACrC,iFAAiF,kBAAkB,4DAA4D,uCAAuC;AACtM,yEAAyE,qBAAqB,EAAE,aAAa;AAC7G,kDAAkD,kCAAkC,sCAAsC,OAAO,EAAE,4BAA4B,6BAA6B;AAC5L,EAAE;AACF,iCAAiC,WAAW;AAC5C;AACA,yCAAyC,gBAAgB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC;AACA;AACA,UAAU,oBAAoB,aAAa;AAC3C;AACA;AACA,8BAA8B,cAAc,OAAO,wBAAwB;AAC3E;AACA,qEAAqE,eAAe,QAAQ,aAAa,EAAE;AAC3G,yCAAyC,cAAc,GAAG,EAAE,4BAA4B,cAAc,eAAe;AACrH;AACA,KAAK;AACL,IAAI;AACJ;AACA,wCAAwC;AACxC;AACA;AACA;AACA,SAAS;AACT;AACA,sCAAsC,EAAE,iCAAiC,+BAA+B,QAAQ,MAAM;;AAEtH,SAAS,sCAAsC,EAAE;AACjD,2BAA2B,gDAAoB,CAAC,2CAAe,wBAAwB,gDAAoB;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gDAAoB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,6CAA6C,gDAAoB,CAAC,2CAAe,2DAA2D,gDAAoB;AAChK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,8CAA8C,gDAAoB;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gDAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,gDAAoB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,sBAAsB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,sBAAsB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB,yCAAc;AACjC;AACA,gDAAgD,iCAAiC;AACjF;AACA;AACA,8BAA8B,2CAAgB;AAC9C;AACA;AACA,GAAG;AACH,iBAAiB,8CAAmB;AACpC;AACA,MAAM,kDAAuB;AAC7B,KAAK;AACL;AACA;AACA,EAAE,kDAAuB;AACzB,yBAAyB,gDAAqB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,qCAAqC;AAC3D,mBAAmB,yCAAc;AACjC;AACA,6CAA6C,iCAAiC;AAC9E;AACA;AACA,8BAA8B,2CAAgB;AAC9C;AACA;AACA,GAAG;AACH,iBAAiB,8CAAmB;AACpC;AACA,MAAM,kDAAuB;AAC7B,KAAK;AACL;AACA;AACA,EAAE,kDAAuB;AACzB,yBAAyB,gDAAqB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,2CAAgB;AAC9C;AACA;AACA,GAAG;AACH,iBAAiB,8CAAmB;AACpC;AACA,MAAM,kDAAuB;AAC7B,KAAK;AACL;AACA;AACA,EAAE,kDAAuB;AACzB,yBAAyB,gDAAqB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,6CAAkB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,UAAU,WAAW,EAAE,6CAAkB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,UAAU;AACV;AACA;AACA,yBAAyB,GAAG;AAC5B;AACA;AACA;AACA;AACA,8BAA8B,UAAU;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,gDAAqB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,gDAAqB,CAAC,2CAAgB,8BAA8B,gDAAqB,sBAAsB,aAAa;AACvL;AACA;AACA;AACA,cAAc,6CAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,qCAAqC,yBAAyB;AAC9D;AACA,sBAAsB,6CAAkB;AACxC,UAAU,sBAAsB,EAAE,6CAAkB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,gDAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,WAAW,6CAAkB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,6CAA6C,UAAU;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,2BAA2B,gDAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB,6CAAkB;AACvC,QAAQ,WAAW,EAAE,6CAAkB;AACvC;AACA;AACA,yBAAyB,oBAAoB;AAC7C,eAAe,0CAAe;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,KAAK;AACzC;AACA;AACA,2EAA2E;AAC3E;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;AACH,yBAAyB,gDAAqB;AAC9C;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc,IAAI;AACtC;AACA,UAAU,IAAI,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,UAAU;AACtB;AACA;AACA,YAAY,6CAAkB;AAC9B;AACA;AACA;AACA;AACA,cAAc,6CAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN;AACA;AACA,mCAAmC,UAAU;AAC7C,SAAS,8CAAmB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,yCAAc;AAC7C,8BAA8B,yCAAc;AAC5C;AACA,qBAAqB,0CAAe;AACpC;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,8CAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,oCAAoC,oBAAoB;AACxD;AACA,QAAQ,SAAS;AACjB,QAAQ,WAAW,EAAE,6CAAkB;AACvC;AACA,SAAS,8CAAmB;AAC5B,+BAA+B;AAC/B,YAAY,0CAA0C;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA,iCAAiC,WAAW,IAAI;AAChD,QAAQ,WAAW,EAAE,6CAAkB;AACvC,qBAAqB,6CAAkB;AACvC;AACA;AACA,eAAe,4CAA4C,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN,QAAQ,SAAS;AACjB;AACA,oBAAoB,6CAAkB;AACtC,cAAc,6CAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,wCAAa;AAChC,oCAAoC,2CAAgB;AACpD;AACA;AACA;AACA,EAAE,4CAAiB;AACnB;AACA;AACA,GAAG;AACH,aAAa,8CAAmB;AAChC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,eAAe,8CAAmB;AAClC;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,oBAAoB,0CAAe;AACnC,uBAAuB,6CAAkB;AACzC;AACA,+BAA+B,gDAAqB,SAAS,4CAA4C;AACzG;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,8BAA8B,0CAAe;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN,QAAQ,SAAS;AACjB,QAAQ,4CAA4C;AACpD;AACA;AACA,QAAQ,WAAW,EAAE,6CAAkB;AACvC;AACA;AACA;AACA,EAAE,4CAAiB;AACnB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,IAAI,8CAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,8GAA8G,MAAM;AACpH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,IAAI,kDAAuB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,KAAK;AACL,IAAI,kDAAuB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,kDAAuB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,QAAQ,UAAU;AAClB,EAAE,4CAAiB;AACnB,mCAAmC,UAAU;AAC7C;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,QAAQ,UAAU;AAClB,EAAE,4CAAiB;AACnB,mCAAmC,UAAU;AAC7C;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,EAAE,4CAAiB;AACnB;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;AACH,EAAE,4CAAiB;AACnB;AACA;AACA;AACA,GAAG;AACH;AACA,6CAA6C;AAC7C,kBAAkB,6CAAkB;AACpC;AACA;AACA;AACA;AACA,QAAQ,WAAW;AACnB;AACA;AACA,mCAAmC,yBAAyB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACiC;AACjC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gDAAqB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,KAAK,EAAE;AAC9E;AACA,QAAQ,QAAQ;AAChB,yBAAyB,gDAAqB,CAAC,2CAAgB,wBAAwB,gDAAqB,+BAA+B,0BAA0B,kBAAkB,gDAAqB,oCAAoC,cAAc,kBAAkB,gDAAqB,6BAA6B,wBAAwB,kBAAkB,gDAAqB,mCAAmC,SAAS,0BAA0B,kBAAkB,gDAAqB;AAC9e;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB,gDAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,gDAAqB;AAC9D;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4JAA4J,mBAAmB;AAC/K;AACA,KAAK;AACL;AACA;AACA,+JAA+J,mBAAmB,IAAI,eAAe;AACrM;AACA,KAAK;AACL;AACA;AACA,0JAA0J,MAAM;AAChK;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,iDAAiD,OAAO;AACxD;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACiC;AACjC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,QAAQ,2DAA2D;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gDAAqB,CAAC,2CAAgB,wBAAwB,gDAAqB;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB,gDAAqB,uBAAuB,iCAAiC,kBAAkB,gDAAqB;AACxI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,gDAAqB,CAAC,2CAAgB,wBAAwB,gDAAqB;AACtI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,oBAAoB,yCAAc;AAClC,0BAA0B,yCAAc;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,oBAAoB;AACpB,mBAAmB,yBAAyB;AAC5C;AACA;AACA,SAAS;AACT,wBAAwB;AACxB;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,2BAA2B,gDAAqB,8BAA8B,gCAAgC,kBAAkB,gDAAqB,mBAAmB,2BAA2B;AACnM;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AAC0C;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,+BAA+B;AACnC;AACA;AACA;AACA;AACA;AACA,kBAAkB,uBAAuB;AACzC;AACA;AACA;AACA;;AAEA;AACA,4CAA4C;AAC5C,QAAQ,2BAA2B;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,oBAAoB,6CAAK,iBAAiB,6BAA6B;AACvE;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA,aAAa,iDAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,EAAE;AACxB;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,sBAAsB,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,KAAK,yOAAyO,SAAS,wEAAwE,SAAS;AACpV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,IAAI;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,kBAAkB;AACnB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,6CAA6C;AAC7E,GAAG,IAAI;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,cAAc;AACd;AACA,cAAc;AACd;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACuD;AACvD;AACA;AACA;AACA,wCAAwC,WAAW;AACnD;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,uDAAuD;AACjE;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,UAAU,KAAK;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,kBAAkB,qEAAkB;AACpC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,QAAQ;AACR;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,iBAAiB;AAC3C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,IAAI;AACJ;AACA;AACA,gBAAgB,OAAO;AACvB;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA,IAAI;AACJ;AACA;AACA,gBAAgB,QAAQ,SAAS;AACjC;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,gCAAgC,gCAAgC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uBAAuB;AACvC;AACA;AACA;AACA,gBAAgB,kCAAkC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,kEAAkE,SAAS;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA,SAAS,sCAAsC;AAC/C;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,4CAA4C,eAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,sDAAsD,aAAa;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,YAAY,WAAW;AACvB;AACA;AACA,6DAA6D,UAAU;AACvE,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,KAAK;AACxB;AACA;AACA;AACA;AACA,sBAAsB,YAAY;AAClC,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,2CAA2C,aAAa;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,gCAAgC,aAAa;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,qCAAqC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,EAAE,cAAc;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,oBAAoB,KAAK;AACzB;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC,KAAK;AACL;AACA,YAAY,kBAAkB;AAC9B;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,YAAY;AACxB;AACA;;AAEA;AACA,sCAAsC,oBAAoB,IAAI;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA,sCAAsC,SAAS,IAAI;AACnD;AACA;AACA;AACA;AACA;AACA,oBAAoB,sBAAsB;AAC1C;AACA,KAAK;AACL;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,oBAAoB,sBAAsB;AAC1C,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,KAAK,oBAAoB,MAAM;AAChD;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AA0HE;;;;;;;UCrzWF;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;ACNuC;AACX;AACmB;AACF;AAC7CY,MAAM,CAACY,2BAA2B,GAAGF,6DAAW,CAAC,CAAC;AAClDG,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACzD,MAAMC,IAAI,GAAGF,QAAQ,CAACG,cAAc,CAAC,4BAA4B,CAAC;EAClE,MAAMC,IAAI,GAAGR,qDAAU,CAACM,IAAI,CAAC;EAE7BE,IAAI,CAACC,MAAM,CACVxC,oDAAA,CAACiC,uDAAa,QACbjC,oDAAA,CAACL,gDAAG,MAAE,CACQ,CAChB,CAAC;AACF,CAAC,CAAC,C", "sources": ["webpack://dom5-member-directory/./react_app/App.jsx", "webpack://dom5-member-directory/./react_app/components/pages/contact-submission/index.jsx", "webpack://dom5-member-directory/./react_app/components/pages/index.jsx", "webpack://dom5-member-directory/./react_app/components/pages/member-directory/index.jsx", "webpack://dom5-member-directory/./react_app/components/pages/members/index.jsx", "webpack://dom5-member-directory/./react_app/components/pages/settings/index.jsx", "webpack://dom5-member-directory/./react_app/components/pages/teams/index.jsx", "webpack://dom5-member-directory/./react_app/components/utils/data.js", "webpack://dom5-member-directory/./react_app/components/utils/navbar/primary-menu.jsx", "webpack://dom5-member-directory/./node_modules/cookie/dist/index.js", "webpack://dom5-member-directory/./node_modules/set-cookie-parser/lib/set-cookie.js", "webpack://dom5-member-directory/external window \"React\"", "webpack://dom5-member-directory/external window \"ReactDOM\"", "webpack://dom5-member-directory/external window [\"wp\",\"hooks\"]", "webpack://dom5-member-directory/external window [\"wp\",\"i18n\"]", "webpack://dom5-member-directory/./node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs", "webpack://dom5-member-directory/webpack/bootstrap", "webpack://dom5-member-directory/webpack/runtime/compat get default export", "webpack://dom5-member-directory/webpack/runtime/define property getters", "webpack://dom5-member-directory/webpack/runtime/hasOwnProperty shorthand", "webpack://dom5-member-directory/webpack/runtime/make namespace object", "webpack://dom5-member-directory/./react_app/index.js"], "sourcesContent": ["import { useLocation } from \"react-router\";\nimport DefineRoutes from \"./components/pages\";\n\nexport function useQuery() {\n\treturn new URLSearchParams(useLocation().search);\n}\n\nconst App = () => {\n\tconst query = useQuery();\n\tconst page = query.get(\"page\") ?? \"dnxte-essential\";\n\n\treturn (\n\t\t<>\n\t\t\t<DefineRoutes page={page} />\n\t\t</>\n\t);\n};\n\nexport default App;\n", "const ContactSubmission = () => {\n    return ( \n        <>\n            <h1>Contact Submission</h1>\n        </>\n     );\n}\n \nexport default ContactSubmission;", "import { Routes, Route, Outlet } from \"react-router\";\nimport MemberDirectory from \"./member-directory\";\nimport Members from \"./members\";\nimport Teams from \"./teams\";\nimport ContactSubmission from \"./contact-submission\";\nimport Settings from \"./settings\";\nimport PrimaryMenu from \"../../components/utils/navbar/primary-menu\";\nimport {__} from '@wordpress/i18n';\nimport { route_path } from \"../../components/utils/data\";\n\nexport const pages = {\n    'dom5-member-directory': {\n        label: __(\"Members Directory\", 'dom5-member-directory'),\n        key: \"members-directory\",\n        component: <MemberDirectory />,\n        icon: '',\n    },\n    'dom5-member-directory-members': {\n        label: __(\"Members\", 'dom5-member-directory'),\n        key: \"members\",\n        component: <Members />,\n        icon: '',\n    },\n    'dom5-member-directory-teams': {\n        label: __(\"Teams\", 'dom5-member-directory'),\n        key: \"teams\",\n        component: <Teams />,\n        icon: '',\n    },\n    'dom5-member-directory-submissions': {\n        label: __(\"Contact Submissions\", 'dom5-member-directory'),\n        key: \"submissions\",\n        component: <ContactSubmission />,\n        icon: '',\n    },\n    'dom5-member-directory-settings': {\n        label: __(\"Settings\", 'dom5-member-directory'),\n        key: \"settings\",\n        component: <Settings />,\n        icon: '',\n    },\n}\n\nconst DefineRoutes = ({ page }) => {\n  return (\n    <>\n      <Routes>\n        <Route path={route_path + \"admin.php\"} element={<Layout page={page} />}>\n          <Route index element={pages[page]?.component} />\n        </Route>\n      </Routes>\n    </>\n  );\n};\n\nexport default DefineRoutes;\nexport const Layout = ({ page }) => {\n    return (\n        <>\n            <PrimaryMenu />\n            <div>\n                <Outlet />\n            </div>\n        </>\n    )\n}", "const MemberDirectory = () => {\n    return ( \n        <>\n            <h1>Member Directory</h1>\n        </>\n     );\n}\n \nexport default MemberDirectory;", "const Members = () => {\n    return ( \n        <>\n            <h1>Members</h1>\n        </>\n     );\n}\n \nexport default Members;", "const Settings = () => {\n    return ( \n        <>\n            <h1>Settings</h1>\n        </>\n     );\n}\n \nexport default Settings;", "const Teams = () => {\n    return ( \n        <>\n            <h1>Teams</h1>\n        </>\n     );\n}\n \nexport default Teams;", "export const { route_path } = window.dom5MemberDirectory;", "import { useQuery } from \"../../../App\";\nimport { pages } from \"../../../components/pages\";\nimport { Link } from \"react-router\";\nimport { route_path } from \"../data\";\n\nconst PrimaryMenu = () => {\n    const query = useQuery();\n    const page = query.get(\"page\") ?? \"dnxte-essential\";\n    return ( \n        <>\n            <h1>DOM5 Member Directory Plugin</h1>\n            <ul>\n                {Object.entries(pages).map(([key, value]) => (\n                    <li className={key === page ? \"active\" : \"\"} key={key}> \n                       <Link to={route_path + `admin.php?page=${key}`}>\n                        {value.label}\n                        </Link>\n                    </li>\n                ))}\n            </ul>\n        </>\n     );\n}\n \nexport default PrimaryMenu;", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parse = parse;\nexports.serialize = serialize;\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nfunction parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nfunction serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else if (key === \"partitioned\") {\n      cookie.partitioned = true;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"wp\"][\"hooks\"];", "module.exports = window[\"wp\"][\"i18n\"];", "/**\n * react-router v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n\n// lib/router/history.ts\nvar Action = /* @__PURE__ */ ((Action2) => {\n  Action2[\"Pop\"] = \"POP\";\n  Action2[\"Push\"] = \"PUSH\";\n  Action2[\"Replace\"] = \"REPLACE\";\n  return Action2;\n})(Action || {});\nvar PopStateEventType = \"popstate\";\nfunction createMemoryHistory(options = {}) {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries;\n  entries = initialEntries.map(\n    (entry, index2) => createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index2 === 0 ? \"default\" : void 0\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = \"POP\" /* Pop */;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state = null, key) {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n  function createHref2(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref: createHref2,\n    createURL(to) {\n      return new URL(createHref2(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = \"PUSH\" /* Push */;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = \"REPLACE\" /* Replace */;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = \"POP\" /* Pop */;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\nfunction createBrowserHistory(options = {}) {\n  function createBrowserLocation(window2, globalHistory) {\n    let { pathname, search, hash } = window2.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      globalHistory.state && globalHistory.state.usr || null,\n      globalHistory.state && globalHistory.state.key || \"default\"\n    );\n  }\n  function createBrowserHref(window2, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\nfunction createHashHistory(options = {}) {\n  function createHashLocation(window2, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window2.location.hash.substring(1));\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      globalHistory.state && globalHistory.state.usr || null,\n      globalHistory.state && globalHistory.state.key || \"default\"\n    );\n  }\n  function createHashHref(window2, to) {\n    let base = window2.document.querySelector(\"base\");\n    let href2 = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window2.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href2 = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href2 + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning(cond, message) {\n  if (!cond) {\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      throw new Error(message);\n    } catch (e) {\n    }\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substring(2, 10);\n}\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\nfunction createLocation(current, to, state = null, key) {\n  let location = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...typeof to === \"string\" ? parsePath(to) : to,\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  };\n  return location;\n}\nfunction createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\"\n}) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substring(hashIndex);\n      path = path.substring(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substring(searchIndex);\n      path = path.substring(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref2, validateLocation, options = {}) {\n  let { window: window2 = document.defaultView, v5Compat = false } = options;\n  let globalHistory = window2.history;\n  let action = \"POP\" /* Pop */;\n  let listener = null;\n  let index = getIndex();\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n  function handlePop() {\n    action = \"POP\" /* Pop */;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n  function push(to, state) {\n    action = \"PUSH\" /* Push */;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      window2.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n  function replace2(to, state) {\n    action = \"REPLACE\" /* Replace */;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n  function createURL(to) {\n    return createBrowserURLImpl(to);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window2, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window2.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window2.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref2(window2, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace: replace2,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n}\nfunction createBrowserURLImpl(to, isAbsolute = false) {\n  let base = \"http://localhost\";\n  if (typeof window !== \"undefined\") {\n    base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n  }\n  invariant(base, \"No window.location.(origin|href) available to create URL\");\n  let href2 = typeof to === \"string\" ? to : createPath(to);\n  href2 = href2.replace(/ $/, \"%20\");\n  if (!isAbsolute && href2.startsWith(\"//\")) {\n    href2 = base + href2;\n  }\n  return new URL(href2, base);\n}\n\n// lib/router/utils.ts\nfunction unstable_createContext(defaultValue) {\n  return { defaultValue };\n}\nvar _map;\nvar unstable_RouterContextProvider = class {\n  constructor(init) {\n    __privateAdd(this, _map, /* @__PURE__ */ new Map());\n    if (init) {\n      for (let [context, value] of init) {\n        this.set(context, value);\n      }\n    }\n  }\n  get(context) {\n    if (__privateGet(this, _map).has(context)) {\n      return __privateGet(this, _map).get(context);\n    }\n    if (context.defaultValue !== void 0) {\n      return context.defaultValue;\n    }\n    throw new Error(\"No value found for context\");\n  }\n  set(context, value) {\n    __privateGet(this, _map).set(context, value);\n  }\n};\n_map = new WeakMap();\nvar unsupportedLazyRouteObjectKeys = /* @__PURE__ */ new Set([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\"\n]);\nfunction isUnsupportedLazyRouteObjectKey(key) {\n  return unsupportedLazyRouteObjectKeys.has(\n    key\n  );\n}\nvar unsupportedLazyRouteFunctionKeys = /* @__PURE__ */ new Set([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"unstable_middleware\",\n  \"children\"\n]);\nfunction isUnsupportedLazyRouteFunctionKey(key) {\n  return unsupportedLazyRouteFunctionKeys.has(\n    key\n  );\n}\nfunction isIndexRoute(route) {\n  return route.index === true;\n}\nfunction convertRoutesToDataRoutes(routes, mapRouteProperties2, parentPath = [], manifest = {}) {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route id's must be globally unique within Data Router usages`\n    );\n    if (isIndexRoute(route)) {\n      let indexRoute = {\n        ...route,\n        ...mapRouteProperties2(route),\n        id\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = {\n        ...route,\n        ...mapRouteProperties2(route),\n        id,\n        children: void 0\n      };\n      manifest[id] = pathOrLayoutRoute;\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties2,\n          treePath,\n          manifest\n        );\n      }\n      return pathOrLayoutRoute;\n    }\n  });\n}\nfunction matchRoutes(routes, locationArg, basename = \"/\") {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\nfunction matchRoutesImpl(routes, locationArg, basename, allowPartial) {\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n  return matches;\n}\nfunction convertRouteMatchToUiMatch(match, loaderData) {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction flattenRoutes(routes, branches = [], parentsMeta = [], parentPath = \"\") {\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === void 0 ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path \"${parentPath}\" is not valid. An absolute child route path must start with the combined path of all its parent routes.`\n      );\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments;\n  let isOptional = first.endsWith(\"?\");\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = [];\n  result.push(\n    ...restExploded.map(\n      (subpath) => subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n  return result.map(\n    (exploded) => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\nfunction rankRouteBranches(branches) {\n  branches.sort(\n    (a, b) => a.score !== b.score ? b.score - a.score : compareIndexes(\n      a.routesMeta.map((meta) => meta.childrenIndex),\n      b.routesMeta.map((meta) => meta.childrenIndex)\n    )\n  );\n}\nvar paramRe = /^:[\\w-]+$/;\nvar dynamicSegmentValue = 3;\nvar indexRouteValue = 2;\nvar emptySegmentValue = 1;\nvar staticSegmentValue = 10;\nvar splatPenalty = -2;\nvar isSplat = (s) => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter((s) => !isSplat(s)).reduce(\n    (score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue),\n    initialScore\n  );\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ? (\n    // If two routes are siblings, we should try to match the earlier sibling\n    // first. This allows people to have fine-grained control over the matching\n    // behavior by simply putting routes with identical paths in the order they\n    // want them tried.\n    a[a.length - 1] - b[b.length - 1]\n  ) : (\n    // Otherwise, it doesn't really make sense to rank non-siblings by index,\n    // so they sort equally.\n    0\n  );\n}\nfunction matchRouteBranch(branch, pathname, allowPartial = false) {\n  let { routesMeta } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n    let route = meta.route;\n    if (!match && end && allowPartial && !routesMeta[routesMeta.length - 1].route.index) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false\n        },\n        remainingPathname\n      );\n    }\n    if (!match) {\n      return null;\n    }\n    Object.assign(matchedParams, match.params);\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\nfunction generatePath(originalPath, params = {}) {\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were \"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must always follow a \\`/\\` in the pattern. To get rid of this warning, please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n  const stringify2 = (p) => p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n  const segments = path.split(/\\/+/).map((segment, index, array) => {\n    const isLastSegment = index === array.length - 1;\n    if (isLastSegment && segment === \"*\") {\n      const star = \"*\";\n      return stringify2(params[star]);\n    }\n    const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n    if (keyMatch) {\n      const [, key, optional] = keyMatch;\n      let param = params[key];\n      invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n      return stringify2(param);\n    }\n    return segment.replace(/\\?$/g, \"\");\n  }).filter((segment) => !!segment);\n  return prefix + segments.join(\"/\");\n}\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = compiledParams.reduce(\n    (memo2, { paramName, isOptional }, index) => {\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n      }\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo2[paramName] = void 0;\n      } else {\n        memo2[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo2;\n    },\n    {}\n  );\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive = false, end = true) {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were \"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must always follow a \\`/\\` in the pattern. To get rid of this warning, please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n  let params = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\").replace(/^\\/*/, \"/\").replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\").replace(\n    /\\/:([\\w-]+)(\\?)?/g,\n    (_, paramName, isOptional) => {\n      params.push({ paramName, isOptional: isOptional != null });\n      return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n    }\n  );\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" : \"(?:\\\\/(.+)|\\\\/*)$\";\n  } else if (end) {\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n  }\n  let matcher = new RegExp(regexpSource, caseSensitive ? void 0 : \"i\");\n  return [matcher, params];\n}\nfunction decodePath(value) {\n  try {\n    return value.split(\"/\").map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\")).join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${error}).`\n    );\n    return value;\n  }\n}\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\nfunction resolvePath(to, fromPathname = \"/\") {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return `Cannot include a '${char}' character in a manually specified \\`to.${field}\\` field [${JSON.stringify(\n    path\n  )}].  Please separate it out to the \\`to.${dest}\\` field. Alternatively you may provide the full path as a string in <Link to=\"...\"> and the router will parse it for you.`;\n}\nfunction getPathContributingMatches(matches) {\n  return matches.filter(\n    (match, index) => index === 0 || match.route.path && match.route.path.length > 0\n  );\n}\nfunction getResolveToMatches(matches) {\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches.map(\n    (match, idx) => idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n  );\n}\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative = false) {\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from;\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    }\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from);\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\nvar joinPaths = (paths) => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\nvar normalizePathname = (pathname) => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\nvar normalizeSearch = (search) => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\nvar normalizeHash = (hash) => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\nvar DataWithResponseInit = class {\n  constructor(data2, init) {\n    this.type = \"DataWithResponseInit\";\n    this.data = data2;\n    this.init = init || null;\n  }\n};\nfunction data(data2, init) {\n  return new DataWithResponseInit(\n    data2,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\nvar redirect = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, { ...responseInit, headers });\n};\nvar redirectDocument = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\nvar replace = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\nvar ErrorResponseImpl = class {\n  constructor(status, statusText, data2, internal = false) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data2 instanceof Error) {\n      this.data = data2.toString();\n      this.error = data2;\n    } else {\n      this.data = data2;\n    }\n  }\n};\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\n\n// lib/router/router.ts\nvar validMutationMethodsArr = [\n  \"POST\",\n  \"PUT\",\n  \"PATCH\",\n  \"DELETE\"\n];\nvar validMutationMethods = new Set(\n  validMutationMethodsArr\n);\nvar validRequestMethodsArr = [\n  \"GET\",\n  ...validMutationMethodsArr\n];\nvar validRequestMethods = new Set(validRequestMethodsArr);\nvar redirectStatusCodes = /* @__PURE__ */ new Set([301, 302, 303, 307, 308]);\nvar redirectPreserveMethodStatusCodes = /* @__PURE__ */ new Set([307, 308]);\nvar IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: void 0,\n  formMethod: void 0,\n  formAction: void 0,\n  formEncType: void 0,\n  formData: void 0,\n  json: void 0,\n  text: void 0\n};\nvar IDLE_FETCHER = {\n  state: \"idle\",\n  data: void 0,\n  formMethod: void 0,\n  formAction: void 0,\n  formEncType: void 0,\n  formData: void 0,\n  json: void 0,\n  text: void 0\n};\nvar IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: void 0,\n  reset: void 0,\n  location: void 0\n};\nvar ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nvar defaultMapRouteProperties = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary)\n});\nvar TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\nvar ResetLoaderDataSymbol = Symbol(\"ResetLoaderData\");\nfunction createRouter(init) {\n  const routerWindow = init.window ? init.window : typeof window !== \"undefined\" ? window : void 0;\n  const isBrowser2 = typeof routerWindow !== \"undefined\" && typeof routerWindow.document !== \"undefined\" && typeof routerWindow.document.createElement !== \"undefined\";\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n  let hydrationRouteProperties2 = init.hydrationRouteProperties || [];\n  let mapRouteProperties2 = init.mapRouteProperties || defaultMapRouteProperties;\n  let manifest = {};\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties2,\n    void 0,\n    manifest\n  );\n  let inFlightDataRoutes;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategyWithMiddleware;\n  let future = {\n    unstable_middleware: false,\n    ...init.future\n  };\n  let unlistenHistory = null;\n  let subscribers = /* @__PURE__ */ new Set();\n  let savedScrollPositions2 = null;\n  let getScrollRestorationKey2 = null;\n  let getScrollPosition = null;\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors = null;\n  let initialized;\n  if (initialMatches == null && !init.patchRoutesOnNavigation) {\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialized = true;\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  } else {\n    if (initialMatches && !init.hydrationData) {\n      let fogOfWar = checkFogOfWar(\n        initialMatches,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active) {\n        initialMatches = null;\n      }\n    }\n    if (!initialMatches) {\n      initialized = false;\n      initialMatches = [];\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    } else if (initialMatches.some((m) => m.route.lazy)) {\n      initialized = false;\n    } else if (!initialMatches.some((m) => m.route.loader)) {\n      initialized = true;\n    } else {\n      let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n      let errors = init.hydrationData ? init.hydrationData.errors : null;\n      if (errors) {\n        let idx = initialMatches.findIndex(\n          (m) => errors[m.route.id] !== void 0\n        );\n        initialized = initialMatches.slice(0, idx + 1).every(\n          (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n        );\n      } else {\n        initialized = initialMatches.every(\n          (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n        );\n      }\n    }\n  }\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: /* @__PURE__ */ new Map(),\n    blockers: /* @__PURE__ */ new Map()\n  };\n  let pendingAction = \"POP\" /* Pop */;\n  let pendingPreventScrollReset = false;\n  let pendingNavigationController;\n  let pendingViewTransitionEnabled = false;\n  let appliedViewTransitions = /* @__PURE__ */ new Map();\n  let removePageHideEventListener = null;\n  let isUninterruptedRevalidation = false;\n  let isRevalidationRequired = false;\n  let cancelledFetcherLoads = /* @__PURE__ */ new Set();\n  let fetchControllers = /* @__PURE__ */ new Map();\n  let incrementingLoadId = 0;\n  let pendingNavigationLoadId = -1;\n  let fetchReloadIds = /* @__PURE__ */ new Map();\n  let fetchRedirectIds = /* @__PURE__ */ new Set();\n  let fetchLoadMatches = /* @__PURE__ */ new Map();\n  let activeFetchers = /* @__PURE__ */ new Map();\n  let fetchersQueuedForDeletion = /* @__PURE__ */ new Set();\n  let blockerFunctions = /* @__PURE__ */ new Map();\n  let unblockBlockerHistoryUpdate = void 0;\n  let pendingRevalidationDfd = null;\n  function initialize() {\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = void 0;\n          return;\n        }\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.\"\n        );\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction\n        });\n        if (blockerKey && delta != null) {\n          let nextHistoryUpdatePromise = new Promise((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey, {\n                state: \"proceeding\",\n                proceed: void 0,\n                reset: void 0,\n                location\n              });\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey, IDLE_BLOCKER);\n              updateState({ blockers });\n            }\n          });\n          return;\n        }\n        return startNavigation(historyAction, location);\n      }\n    );\n    if (isBrowser2) {\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () => persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () => routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n    if (!state.initialized) {\n      startNavigation(\"POP\" /* Pop */, state.location, {\n        initialHydration: true\n      });\n    }\n    return router;\n  }\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n  function updateState(newState, opts = {}) {\n    state = {\n      ...state,\n      ...newState\n    };\n    let unmountedFetchers = [];\n    let mountedFetchers = [];\n    state.fetchers.forEach((fetcher, key) => {\n      if (fetcher.state === \"idle\") {\n        if (fetchersQueuedForDeletion.has(key)) {\n          unmountedFetchers.push(key);\n        } else {\n          mountedFetchers.push(key);\n        }\n      }\n    });\n    fetchersQueuedForDeletion.forEach((key) => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        unmountedFetchers.push(key);\n      }\n    });\n    [...subscribers].forEach(\n      (subscriber) => subscriber(state, {\n        deletedFetchers: unmountedFetchers,\n        viewTransitionOpts: opts.viewTransitionOpts,\n        flushSync: opts.flushSync === true\n      })\n    );\n    unmountedFetchers.forEach((key) => deleteFetcher(key));\n    mountedFetchers.forEach((key) => state.fetchers.delete(key));\n  }\n  function completeNavigation(location, newState, { flushSync } = {}) {\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && location.state?._isRedirect !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      actionData = state.actionData;\n    } else {\n      actionData = null;\n    }\n    let loaderData = newState.loaderData ? mergeLoaderData(\n      state.loaderData,\n      newState.loaderData,\n      newState.matches || [],\n      newState.errors\n    ) : state.loaderData;\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && location.state?._isRedirect !== true;\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = void 0;\n    }\n    if (isUninterruptedRevalidation) {\n    } else if (pendingAction === \"POP\" /* Pop */) {\n    } else if (pendingAction === \"PUSH\" /* Push */) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === \"REPLACE\" /* Replace */) {\n      init.history.replace(location, location.state);\n    }\n    let viewTransitionOpts;\n    if (pendingAction === \"POP\" /* Pop */) {\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = /* @__PURE__ */ new Set([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location\n      };\n    }\n    updateState(\n      {\n        ...newState,\n        // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true\n      }\n    );\n    pendingAction = \"POP\" /* Pop */;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    pendingRevalidationDfd?.resolve();\n    pendingRevalidationDfd = null;\n  }\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      to,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      false,\n      normalizedPath,\n      opts\n    );\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation)\n    };\n    let userReplace = opts && opts.replace != null ? opts.replace : void 0;\n    let historyAction = \"PUSH\" /* Push */;\n    if (userReplace === true) {\n      historyAction = \"REPLACE\" /* Replace */;\n    } else if (userReplace === false) {\n    } else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      historyAction = \"REPLACE\" /* Replace */;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : void 0;\n    let flushSync = (opts && opts.flushSync) === true;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: void 0,\n            reset: void 0,\n            location: nextLocation\n          });\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey, IDLE_BLOCKER);\n          updateState({ blockers });\n        }\n      });\n      return;\n    }\n    await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync\n    });\n  }\n  function revalidate() {\n    if (!pendingRevalidationDfd) {\n      pendingRevalidationDfd = createDeferred();\n    }\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n    let promise = pendingRevalidationDfd.promise;\n    if (state.navigation.state === \"submitting\") {\n      return promise;\n    }\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return promise;\n    }\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true\n      }\n    );\n    return promise;\n  }\n  async function startNavigation(historyAction, location, opts) {\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true;\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = opts?.initialHydration && state.matches && state.matches.length > 0 && !initialMatchesIsFOW ? (\n      // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n      state.matches\n    ) : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n    if (matches && state.initialized && !isRevalidationRequired && isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error\n          }\n        },\n        { flushSync }\n      );\n      return;\n    }\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let scopedContext = new unstable_RouterContextProvider(\n      init.unstable_getContext ? await init.unstable_getContext() : void 0\n    );\n    let pendingActionResult;\n    if (opts && opts.pendingError) {\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: \"error\" /* error */, error: opts.pendingError }\n      ];\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        scopedContext,\n        fogOfWar.active,\n        opts && opts.initialHydration === true,\n        { replace: opts.replace, flushSync }\n      );\n      if (actionResult.shortCircuited) {\n        return;\n      }\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (isErrorResult(result) && isRouteErrorResponse(result.error) && result.error.status === 404) {\n          pendingNavigationController = null;\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error\n            }\n          });\n          return;\n        }\n      }\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      fogOfWar.active = false;\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      scopedContext,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n    if (shortCircuited) {\n      return;\n    }\n    pendingNavigationController = null;\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors\n    });\n  }\n  async function handleAction(request, location, submission, matches, scopedContext, isFogOfWar, initialHydration, opts = {}) {\n    interruptActiveLoads();\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: \"error\" /* error */,\n              error: discoverResult.error\n            }\n          ]\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: \"error\" /* error */,\n              error\n            }\n          ]\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: \"error\" /* error */,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      let dsMatches = getTargetedDataStrategyMatches(\n        mapRouteProperties2,\n        manifest,\n        request,\n        matches,\n        actionMatch,\n        initialHydration ? [] : hydrationRouteProperties2,\n        scopedContext\n      );\n      let results = await callDataStrategy(\n        request,\n        dsMatches,\n        scopedContext,\n        null\n      );\n      result = results[actionMatch.route.id];\n      if (!result) {\n        for (let match of matches) {\n          if (results[match.route.id]) {\n            result = results[match.route.id];\n            break;\n          }\n        }\n      }\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace2;\n      if (opts && opts.replace != null) {\n        replace2 = opts.replace;\n      } else {\n        let location2 = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\"),\n          new URL(request.url),\n          basename\n        );\n        replace2 = location2 === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace: replace2\n      });\n      return { shortCircuited: true };\n    }\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      if ((opts && opts.replace) !== true) {\n        pendingAction = \"PUSH\" /* Push */;\n      }\n      return {\n        matches,\n        pendingActionResult: [\n          boundaryMatch.route.id,\n          result,\n          actionMatch.route.id\n        ]\n      };\n    }\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result]\n    };\n  }\n  async function handleLoaders(request, location, matches, scopedContext, isFogOfWar, overrideNavigation, submission, fetcherSubmission, replace2, initialHydration, flushSync, pendingActionResult) {\n    let loadingNavigation = overrideNavigation || getLoadingNavigation(location, submission);\n    let activeSubmission = submission || fetcherSubmission || getSubmissionFromNavigation(loadingNavigation);\n    let shouldUpdateNavigationState = !isUninterruptedRevalidation && !initialHydration;\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...actionData !== void 0 ? { actionData } : {}\n          },\n          {\n            flushSync\n          }\n        );\n      }\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error\n          }\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error\n          }\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { dsMatches, revalidatingFetchers } = getMatchesToLoad(\n      request,\n      scopedContext,\n      mapRouteProperties2,\n      manifest,\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      initialHydration ? [] : hydrationRouteProperties2,\n      initialHydration === true,\n      isRevalidationRequired,\n      cancelledFetcherLoads,\n      fetchersQueuedForDeletion,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      init.patchRoutesOnNavigation != null,\n      pendingActionResult\n    );\n    pendingNavigationLoadId = ++incrementingLoadId;\n    if (!init.dataStrategy && !dsMatches.some((m) => m.shouldLoad) && revalidatingFetchers.length === 0) {\n      let updatedFetchers2 = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? { [pendingActionResult[0]]: pendingActionResult[1].error } : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...updatedFetchers2 ? { fetchers: new Map(state.fetchers) } : {}\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n    if (shouldUpdateNavigationState) {\n      let updates = {};\n      if (!isFogOfWar) {\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== void 0) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n    revalidatingFetchers.forEach((rf) => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    let { loaderResults, fetcherResults } = await callLoadersAndMaybeResolveData(\n      dsMatches,\n      revalidatingFetchers,\n      request,\n      scopedContext\n    );\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n    let redirect2 = findRedirect(loaderResults);\n    if (redirect2) {\n      await startRedirectNavigation(request, redirect2.result, true, {\n        replace: replace2\n      });\n      return { shortCircuited: true };\n    }\n    redirect2 = findRedirect(fetcherResults);\n    if (redirect2) {\n      fetchRedirectIds.add(redirect2.key);\n      await startRedirectNavigation(request, redirect2.result, true, {\n        replace: replace2\n      });\n      return { shortCircuited: true };\n    }\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults\n    );\n    if (initialHydration && state.errors) {\n      errors = { ...state.errors, ...errors };\n    }\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers = updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}\n    };\n  }\n  function getUpdatedActionData(pendingActionResult) {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n  function getUpdatedRevalidatingFetchers(revalidatingFetchers) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        void 0,\n        fetcher ? fetcher.data : void 0\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n  async function fetch2(key, routeId, href2, opts) {\n    abortFetcher(key);\n    let flushSync = (opts && opts.flushSync) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      href2,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n    let { path, submission, error } = normalizeNavigateOptions(\n      true,\n      normalizedPath,\n      opts\n    );\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n    let match = getTargetMatch(matches, path);\n    let scopedContext = new unstable_RouterContextProvider(\n      init.unstable_getContext ? await init.unstable_getContext() : void 0\n    );\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      await handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        scopedContext,\n        fogOfWar.active,\n        flushSync,\n        preventScrollReset,\n        submission\n      );\n      return;\n    }\n    fetchLoadMatches.set(key, { routeId, path });\n    await handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      scopedContext,\n      fogOfWar.active,\n      flushSync,\n      preventScrollReset,\n      submission\n    );\n  }\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, scopedContext, isFogOfWar, flushSync, preventScrollReset, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    function detectAndHandle405Error(m) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        path,\n        fetchRequest.signal,\n        key\n      );\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let fetchMatches = getTargetedDataStrategyMatches(\n      mapRouteProperties2,\n      manifest,\n      fetchRequest,\n      requestMatches,\n      match,\n      hydrationRouteProperties2,\n      scopedContext\n    );\n    let actionResults = await callDataStrategy(\n      fetchRequest,\n      fetchMatches,\n      scopedContext,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n    if (fetchRequest.signal.aborted) {\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    if (fetchersQueuedForDeletion.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(void 0));\n        return;\n      }\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          updateFetcherState(key, getDoneFetcher(void 0));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset\n          });\n        }\n      }\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(routesToUse, state.navigation.location, basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n    let { dsMatches, revalidatingFetchers } = getMatchesToLoad(\n      revalidationRequest,\n      scopedContext,\n      mapRouteProperties2,\n      manifest,\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      hydrationRouteProperties2,\n      false,\n      isRevalidationRequired,\n      cancelledFetcherLoads,\n      fetchersQueuedForDeletion,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      init.patchRoutesOnNavigation != null,\n      [match.route.id, actionResult]\n    );\n    revalidatingFetchers.filter((rf) => rf.key !== key).forEach((rf) => {\n      let staleKey = rf.key;\n      let existingFetcher2 = state.fetchers.get(staleKey);\n      let revalidatingFetcher = getLoadingFetcher(\n        void 0,\n        existingFetcher2 ? existingFetcher2.data : void 0\n      );\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      abortFetcher(staleKey);\n      if (rf.controller) {\n        fetchControllers.set(staleKey, rf.controller);\n      }\n    });\n    updateState({ fetchers: new Map(state.fetchers) });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n    let { loaderResults, fetcherResults } = await callLoadersAndMaybeResolveData(\n      dsMatches,\n      revalidatingFetchers,\n      revalidationRequest,\n      scopedContext\n    );\n    if (abortController.signal.aborted) {\n      return;\n    }\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n    let redirect2 = findRedirect(loaderResults);\n    if (redirect2) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect2.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n    redirect2 = findRedirect(fetcherResults);\n    if (redirect2) {\n      fetchRedirectIds.add(redirect2.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect2.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      void 0,\n      revalidatingFetchers,\n      fetcherResults\n    );\n    abortStaleFetchLoads(loadId);\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers)\n      });\n      isRevalidationRequired = false;\n    }\n  }\n  async function handleFetcherLoader(key, routeId, path, match, matches, scopedContext, isFogOfWar, flushSync, preventScrollReset, submission) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : void 0\n      ),\n      { flushSync }\n    );\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        path,\n        fetchRequest.signal,\n        key\n      );\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let dsMatches = getTargetedDataStrategyMatches(\n      mapRouteProperties2,\n      manifest,\n      fetchRequest,\n      matches,\n      match,\n      hydrationRouteProperties2,\n      scopedContext\n    );\n    let results = await callDataStrategy(\n      fetchRequest,\n      dsMatches,\n      scopedContext,\n      key\n    );\n    let result = results[match.route.id];\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n    if (fetchersQueuedForDeletion.has(key)) {\n      updateFetcherState(key, getDoneFetcher(void 0));\n      return;\n    }\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        updateFetcherState(key, getDoneFetcher(void 0));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset\n        });\n        return;\n      }\n    }\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n  async function startRedirectNavigation(request, redirect2, isNavigation, {\n    submission,\n    fetcherSubmission,\n    preventScrollReset,\n    replace: replace2\n  } = {}) {\n    if (redirect2.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n    let location = redirect2.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true\n    });\n    if (isBrowser2) {\n      let isDocumentReload = false;\n      if (redirect2.response.headers.has(\"X-Remix-Reload-Document\")) {\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = createBrowserURLImpl(location, true);\n        isDocumentReload = // Hard reload if it's an absolute URL to a new origin\n        url.origin !== routerWindow.location.origin || // Hard reload if it's an absolute URL that does not match our basename\n        stripBasename(url.pathname, basename) == null;\n      }\n      if (isDocumentReload) {\n        if (replace2) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n    pendingNavigationController = null;\n    let redirectNavigationType = replace2 === true || redirect2.response.headers.has(\"X-Remix-Replace\") ? \"REPLACE\" /* Replace */ : \"PUSH\" /* Push */;\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (!submission && !fetcherSubmission && formMethod && formAction && formEncType) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n    let activeSubmission = submission || fetcherSubmission;\n    if (redirectPreserveMethodStatusCodes.has(redirect2.response.status) && activeSubmission && isMutationMethod(activeSubmission.formMethod)) {\n      await startNavigation(redirectNavigationType, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : void 0\n      });\n    } else {\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectNavigationType, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : void 0\n      });\n    }\n  }\n  async function callDataStrategy(request, matches, scopedContext, fetcherKey) {\n    let results;\n    let dataResults = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        request,\n        matches,\n        fetcherKey,\n        scopedContext,\n        false\n      );\n    } catch (e) {\n      matches.filter((m) => m.shouldLoad).forEach((m) => {\n        dataResults[m.route.id] = {\n          type: \"error\" /* error */,\n          error: e\n        };\n      });\n      return dataResults;\n    }\n    if (request.signal.aborted) {\n      return dataResults;\n    }\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResult(result)) {\n        let response = result.result;\n        dataResults[routeId] = {\n          type: \"redirect\" /* redirect */,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename\n          )\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n    return dataResults;\n  }\n  async function callLoadersAndMaybeResolveData(matches, fetchersToLoad, request, scopedContext) {\n    let loaderResultsPromise = callDataStrategy(\n      request,\n      matches,\n      scopedContext,\n      null\n    );\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.request && f.controller) {\n          let results = await callDataStrategy(\n            f.request,\n            f.matches,\n            scopedContext,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: \"error\" /* error */,\n              error: getInternalRouterError(404, {\n                pathname: f.path\n              })\n            }\n          });\n        }\n      })\n    );\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n    return {\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    isRevalidationRequired = true;\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n  function updateFetcherState(key, fetcher, opts = {}) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n  function setFetcherError(key, routeId, error, opts = {}) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error\n        },\n        fetchers: new Map(state.fetchers)\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n  function getFetcher(key) {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    if (fetchersQueuedForDeletion.has(key)) {\n      fetchersQueuedForDeletion.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n  function deleteFetcher(key) {\n    let fetcher = state.fetchers.get(key);\n    if (fetchControllers.has(key) && !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    fetchersQueuedForDeletion.delete(key);\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n  function queueFetcherForDeletion(key) {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      fetchersQueuedForDeletion.add(key);\n    } else {\n      activeFetchers.set(key, count);\n    }\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    invariant(\n      blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\",\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction\n  }) {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n    if (blocker && blocker.state === \"proceeding\") {\n      return;\n    }\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n  function handleNavigational404(pathname) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n    return { notFoundMatches: matches, route, error };\n  }\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions2 = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey2 = getKey || null;\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n    return () => {\n      savedScrollPositions2 = null;\n      getScrollPosition = null;\n      getScrollRestorationKey2 = null;\n    };\n  }\n  function getScrollKey(location, matches) {\n    if (getScrollRestorationKey2) {\n      let key = getScrollRestorationKey2(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions2 && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions2[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions2) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions2[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  function checkFogOfWar(matches, routesToUse, pathname) {\n    if (init.patchRoutesOnNavigation) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          let partialMatches = matchRoutesImpl(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n    return { active: false, matches: null };\n  }\n  async function discoverRoutes(matches, pathname, signal, fetcherKey) {\n    if (!init.patchRoutesOnNavigation) {\n      return { type: \"success\", matches };\n    }\n    let partialMatches = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await init.patchRoutesOnNavigation({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          fetcherKey,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(\n              routeId,\n              children,\n              routesToUse,\n              localManifest,\n              mapRouteProperties2\n            );\n          }\n        });\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return { type: \"success\", matches: newMatches };\n      }\n      let newPartialMatches = matchRoutesImpl(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n      if (!newPartialMatches || partialMatches.length === newPartialMatches.length && partialMatches.every(\n        (m, i) => m.route.id === newPartialMatches[i].route.id\n      )) {\n        return { type: \"success\", matches: null };\n      }\n      partialMatches = newPartialMatches;\n    }\n  }\n  function _internalSetRoutes(newRoutes) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties2,\n      void 0,\n      manifest\n    );\n  }\n  function patchRoutes(routeId, children) {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties2\n    );\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch: fetch2,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to) => init.history.createHref(to),\n    encodeLocation: (to) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: queueFetcherForDeletion,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes\n  };\n  return router;\n}\nfunction createStaticHandler(routes, opts) {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n  let manifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties2 = opts?.mapRouteProperties || defaultMapRouteProperties;\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties2,\n    void 0,\n    manifest\n  );\n  async function query(request, {\n    requestContext,\n    filterMatchesToLoad,\n    skipLoaderErrorBubbling,\n    skipRevalidation,\n    dataStrategy,\n    unstable_respond: respond\n  } = {}) {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    requestContext = requestContext != null ? requestContext : new unstable_RouterContextProvider();\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } = getShortCircuitMatches(dataRoutes);\n      let staticContext = {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n      return respond ? respond(staticContext) : staticContext;\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } = getShortCircuitMatches(dataRoutes);\n      let staticContext = {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n      return respond ? respond(staticContext) : staticContext;\n    }\n    if (respond && matches.some(\n      (m) => m.route.unstable_middleware || typeof m.route.lazy === \"object\" && m.route.lazy.unstable_middleware\n    )) {\n      invariant(\n        requestContext instanceof unstable_RouterContextProvider,\n        \"When using middleware in `staticHandler.query()`, any provided `requestContext` must be an instance of `unstable_RouterContextProvider`\"\n      );\n      try {\n        await loadLazyMiddlewareForMatches(\n          matches,\n          manifest,\n          mapRouteProperties2\n        );\n        let renderedStaticContext;\n        let response = await runMiddlewarePipeline(\n          {\n            request,\n            matches,\n            params: matches[0].params,\n            // If we're calling middleware then it must be enabled so we can cast\n            // this to the proper type knowing it's not an `AppLoadContext`\n            context: requestContext\n          },\n          true,\n          async () => {\n            let result2 = await queryImpl(\n              request,\n              location,\n              matches,\n              requestContext,\n              dataStrategy || null,\n              skipLoaderErrorBubbling === true,\n              null,\n              filterMatchesToLoad || null,\n              skipRevalidation === true\n            );\n            if (isResponse(result2)) {\n              return result2;\n            }\n            renderedStaticContext = { location, basename, ...result2 };\n            let res = await respond(renderedStaticContext);\n            return res;\n          },\n          async (error, routeId) => {\n            if (isResponse(error)) {\n              return error;\n            }\n            if (renderedStaticContext) {\n              if (routeId in renderedStaticContext.loaderData) {\n                renderedStaticContext.loaderData[routeId] = void 0;\n              }\n              return respond(\n                getStaticContextFromError(\n                  dataRoutes,\n                  renderedStaticContext,\n                  error,\n                  skipLoaderErrorBubbling ? routeId : findNearestBoundary(matches, routeId).route.id\n                )\n              );\n            } else {\n              let boundaryRouteId = skipLoaderErrorBubbling ? routeId : findNearestBoundary(\n                matches,\n                matches.find(\n                  (m) => m.route.id === routeId || m.route.loader\n                )?.route.id || routeId\n              ).route.id;\n              return respond({\n                matches,\n                location,\n                basename,\n                loaderData: {},\n                actionData: null,\n                errors: {\n                  [boundaryRouteId]: error\n                },\n                statusCode: isRouteErrorResponse(error) ? error.status : 500,\n                actionHeaders: {},\n                loaderHeaders: {}\n              });\n            }\n          }\n        );\n        invariant(isResponse(response), \"Expected a response in query()\");\n        return response;\n      } catch (e) {\n        if (isResponse(e)) {\n          return e;\n        }\n        throw e;\n      }\n    }\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null,\n      filterMatchesToLoad || null,\n      skipRevalidation === true\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n    return { location, basename, ...result };\n  }\n  async function queryRoute(request, {\n    routeId,\n    requestContext,\n    dataStrategy,\n    unstable_respond: respond\n  } = {}) {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    requestContext = requestContext != null ? requestContext : new unstable_RouterContextProvider();\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n    let match = routeId ? matches.find((m) => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n    if (respond && matches.some(\n      (m) => m.route.unstable_middleware || typeof m.route.lazy === \"object\" && m.route.lazy.unstable_middleware\n    )) {\n      invariant(\n        requestContext instanceof unstable_RouterContextProvider,\n        \"When using middleware in `staticHandler.queryRoute()`, any provided `requestContext` must be an instance of `unstable_RouterContextProvider`\"\n      );\n      await loadLazyMiddlewareForMatches(matches, manifest, mapRouteProperties2);\n      let response = await runMiddlewarePipeline(\n        {\n          request,\n          matches,\n          params: matches[0].params,\n          // If we're calling middleware then it must be enabled so we can cast\n          // this to the proper type knowing it's not an `AppLoadContext`\n          context: requestContext\n        },\n        true,\n        async () => {\n          let result2 = await queryImpl(\n            request,\n            location,\n            matches,\n            requestContext,\n            dataStrategy || null,\n            false,\n            match,\n            null,\n            false\n          );\n          if (isResponse(result2)) {\n            return respond(result2);\n          }\n          let error2 = result2.errors ? Object.values(result2.errors)[0] : void 0;\n          if (error2 !== void 0) {\n            throw error2;\n          }\n          let value = result2.actionData ? Object.values(result2.actionData)[0] : Object.values(result2.loaderData)[0];\n          return typeof value === \"string\" ? new Response(value) : Response.json(value);\n        },\n        (error2) => {\n          if (isResponse(error2)) {\n            return respond(error2);\n          }\n          return new Response(String(error2), {\n            status: 500,\n            statusText: \"Unexpected Server Error\"\n          });\n        }\n      );\n      return response;\n    }\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      false,\n      match,\n      null,\n      false\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : void 0;\n    if (error !== void 0) {\n      throw error;\n    }\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      return Object.values(result.loaderData)[0];\n    }\n    return void 0;\n  }\n  async function queryImpl(request, location, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch, filterMatchesToLoad, skipRevalidation) {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n    try {\n      if (isMutationMethod(request.method)) {\n        let result2 = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null,\n          filterMatchesToLoad,\n          skipRevalidation\n        );\n        return result2;\n      }\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch,\n        filterMatchesToLoad\n      );\n      return isResponse(result) ? result : {\n        ...result,\n        actionData: null,\n        actionHeaders: {}\n      };\n    } catch (e) {\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === \"error\" /* error */) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, dataStrategy, skipLoaderErrorBubbling, isRouteRequest, filterMatchesToLoad, skipRevalidation) {\n    let result;\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: \"error\" /* error */,\n        error\n      };\n    } else {\n      let dsMatches = getTargetedDataStrategyMatches(\n        mapRouteProperties2,\n        manifest,\n        request,\n        matches,\n        actionMatch,\n        [],\n        requestContext\n      );\n      let results = await callDataStrategy(\n        request,\n        dsMatches,\n        isRouteRequest,\n        requestContext,\n        dataStrategy\n      );\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest);\n      }\n    }\n    if (isRedirectResult(result)) {\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")\n        }\n      });\n    }\n    if (isRouteRequest) {\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    }\n    if (skipRevalidation) {\n      if (isErrorResult(result)) {\n        let boundaryMatch = skipLoaderErrorBubbling ? actionMatch : findNearestBoundary(matches, actionMatch.route.id);\n        return {\n          statusCode: isRouteErrorResponse(result.error) ? result.error.status : result.statusCode != null ? result.statusCode : 500,\n          actionData: null,\n          actionHeaders: {\n            ...result.headers ? { [actionMatch.route.id]: result.headers } : {}\n          },\n          matches,\n          loaderData: {},\n          errors: {\n            [boundaryMatch.route.id]: result.error\n          },\n          loaderHeaders: {}\n        };\n      } else {\n        return {\n          actionData: {\n            [actionMatch.route.id]: result.data\n          },\n          actionHeaders: result.headers ? { [actionMatch.route.id]: result.headers } : {},\n          matches,\n          loaderData: {},\n          errors: null,\n          statusCode: result.statusCode || 200,\n          loaderHeaders: {}\n        };\n      }\n    }\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    if (isErrorResult(result)) {\n      let boundaryMatch = skipLoaderErrorBubbling ? actionMatch : findNearestBoundary(matches, actionMatch.route.id);\n      let handlerContext2 = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        filterMatchesToLoad,\n        [boundaryMatch.route.id, result]\n      );\n      return {\n        ...handlerContext2,\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : result.statusCode != null ? result.statusCode : 500,\n        actionData: null,\n        actionHeaders: {\n          ...result.headers ? { [actionMatch.route.id]: result.headers } : {}\n        }\n      };\n    }\n    let handlerContext = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      dataStrategy,\n      skipLoaderErrorBubbling,\n      null,\n      filterMatchesToLoad\n    );\n    return {\n      ...handlerContext,\n      actionData: {\n        [actionMatch.route.id]: result.data\n      },\n      // action status codes take precedence over loader status codes\n      ...result.statusCode ? { statusCode: result.statusCode } : {},\n      actionHeaders: result.headers ? { [actionMatch.route.id]: result.headers } : {}\n    };\n  }\n  async function loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch, filterMatchesToLoad, pendingActionResult) {\n    let isRouteRequest = routeMatch != null;\n    if (isRouteRequest && !routeMatch?.route.loader && !routeMatch?.route.lazy) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id\n      });\n    }\n    let dsMatches;\n    if (routeMatch) {\n      dsMatches = getTargetedDataStrategyMatches(\n        mapRouteProperties2,\n        manifest,\n        request,\n        matches,\n        routeMatch,\n        [],\n        requestContext\n      );\n    } else {\n      let maxIdx = pendingActionResult && isErrorResult(pendingActionResult[1]) ? (\n        // Up to but not including the boundary\n        matches.findIndex((m) => m.route.id === pendingActionResult[0]) - 1\n      ) : void 0;\n      dsMatches = matches.map((match, index) => {\n        if (maxIdx != null && index > maxIdx) {\n          return getDataStrategyMatch(\n            mapRouteProperties2,\n            manifest,\n            request,\n            match,\n            [],\n            requestContext,\n            false\n          );\n        }\n        return getDataStrategyMatch(\n          mapRouteProperties2,\n          manifest,\n          request,\n          match,\n          [],\n          requestContext,\n          (match.route.loader || match.route.lazy) != null && (!filterMatchesToLoad || filterMatchesToLoad(match))\n        );\n      });\n    }\n    if (!dataStrategy && !dsMatches.some((m) => m.shouldLoad)) {\n      return {\n        matches,\n        loaderData: {},\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null,\n        statusCode: 200,\n        loaderHeaders: {}\n      };\n    }\n    let results = await callDataStrategy(\n      request,\n      dsMatches,\n      isRouteRequest,\n      requestContext,\n      dataStrategy\n    );\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest);\n    }\n    let handlerContext = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      true,\n      skipLoaderErrorBubbling\n    );\n    return {\n      ...handlerContext,\n      matches\n    };\n  }\n  async function callDataStrategy(request, matches, isRouteRequest, requestContext, dataStrategy) {\n    let results = await callDataStrategyImpl(\n      dataStrategy || defaultDataStrategy,\n      request,\n      matches,\n      null,\n      requestContext,\n      true\n    );\n    let dataResults = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResult(result)) {\n          let response = result.result;\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          throw result;\n        }\n        dataResults[match.route.id] = await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n}\nfunction getStaticContextFromError(routes, handlerContext, error, boundaryId) {\n  let errorBoundaryId = boundaryId || handlerContext._deepestRenderedBoundaryId || routes[0].id;\n  return {\n    ...handlerContext,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [errorBoundaryId]: error\n    }\n  };\n}\nfunction throwStaticHandlerAbortedError(request, isRouteRequest) {\n  if (request.signal.reason !== void 0) {\n    throw request.signal.reason;\n  }\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(\n    `${method}() call aborted without an \\`AbortSignal.reason\\`: ${request.method} ${request.url}`\n  );\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && (\"formData\" in opts && opts.formData != null || \"body\" in opts && opts.body !== void 0);\n}\nfunction normalizeTo(location, matches, basename, to, fromRouteId, relative) {\n  let contextualMatches;\n  let activeRouteMatch;\n  if (fromRouteId) {\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\nfunction normalizeNavigateOptions(isFetcher, path, opts) {\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod })\n    };\n  }\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" })\n  });\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = rawFormMethod.toUpperCase();\n  let formAction = stripHashFromPath(path);\n  if (opts.body !== void 0) {\n    if (opts.formEncType === \"text/plain\") {\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      let text = typeof opts.body === \"string\" ? opts.body : opts.body instanceof FormData || opts.body instanceof URLSearchParams ? (\n        // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n        Array.from(opts.body.entries()).reduce(\n          (acc, [name, value]) => `${acc}${name}=${value}\n`,\n          \"\"\n        )\n      ) : String(opts.body);\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: void 0,\n          json: void 0,\n          text\n        }\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      try {\n        let json = typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: void 0,\n            json,\n            text: void 0\n          }\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n  let searchParams;\n  let formData;\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n  let submission = {\n    formMethod,\n    formAction,\n    formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n    formData,\n    json: void 0,\n    text: void 0\n  };\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n  let parsedPath = parsePath(path);\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n  return { path: createPath(parsedPath), submission };\n}\nfunction getMatchesToLoad(request, scopedContext, mapRouteProperties2, manifest, history, state, matches, submission, location, lazyRoutePropertiesToSkip, initialHydration, isRevalidationRequired, cancelledFetcherLoads, fetchersQueuedForDeletion, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, hasPatchRoutesOnNavigation, pendingActionResult) {\n  let actionResult = pendingActionResult ? isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : pendingActionResult[1].data : void 0;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  let maxIdx;\n  if (initialHydration && state.errors) {\n    let boundaryId = Object.keys(state.errors)[0];\n    maxIdx = matches.findIndex((m) => m.route.id === boundaryId);\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    let boundaryId = pendingActionResult[0];\n    maxIdx = matches.findIndex((m) => m.route.id === boundaryId) - 1;\n  }\n  let actionStatus = pendingActionResult ? pendingActionResult[1].statusCode : void 0;\n  let shouldSkipRevalidation = actionStatus && actionStatus >= 400;\n  let baseShouldRevalidateArgs = {\n    currentUrl,\n    currentParams: state.matches[0]?.params || {},\n    nextUrl,\n    nextParams: matches[0].params,\n    ...submission,\n    actionResult,\n    actionStatus\n  };\n  let dsMatches = matches.map((match, index) => {\n    let { route } = match;\n    let forceShouldLoad = null;\n    if (maxIdx != null && index > maxIdx) {\n      forceShouldLoad = false;\n    } else if (route.lazy) {\n      forceShouldLoad = true;\n    } else if (route.loader == null) {\n      forceShouldLoad = false;\n    } else if (initialHydration) {\n      forceShouldLoad = shouldLoadRouteOnHydration(\n        route,\n        state.loaderData,\n        state.errors\n      );\n    } else if (isNewLoader(state.loaderData, state.matches[index], match)) {\n      forceShouldLoad = true;\n    }\n    if (forceShouldLoad !== null) {\n      return getDataStrategyMatch(\n        mapRouteProperties2,\n        manifest,\n        request,\n        match,\n        lazyRoutePropertiesToSkip,\n        scopedContext,\n        forceShouldLoad\n      );\n    }\n    let defaultShouldRevalidate = shouldSkipRevalidation ? false : (\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      isRevalidationRequired || currentUrl.pathname + currentUrl.search === nextUrl.pathname + nextUrl.search || // Search params affect all loaders\n      currentUrl.search !== nextUrl.search || isNewRouteInstance(state.matches[index], match)\n    );\n    let shouldRevalidateArgs = {\n      ...baseShouldRevalidateArgs,\n      defaultShouldRevalidate\n    };\n    let shouldLoad = shouldRevalidateLoader(match, shouldRevalidateArgs);\n    return getDataStrategyMatch(\n      mapRouteProperties2,\n      manifest,\n      request,\n      match,\n      lazyRoutePropertiesToSkip,\n      scopedContext,\n      shouldLoad,\n      shouldRevalidateArgs\n    );\n  });\n  let revalidatingFetchers = [];\n  fetchLoadMatches.forEach((f, key) => {\n    if (initialHydration || !matches.some((m) => m.route.id === f.routeId) || fetchersQueuedForDeletion.has(key)) {\n      return;\n    }\n    let fetcher = state.fetchers.get(key);\n    let isMidInitialLoad = fetcher && fetcher.state !== \"idle\" && fetcher.data === void 0;\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n    if (!fetcherMatches) {\n      if (hasPatchRoutesOnNavigation && isMidInitialLoad) {\n        return;\n      }\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        request: null,\n        controller: null\n      });\n      return;\n    }\n    if (fetchRedirectIds.has(key)) {\n      return;\n    }\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n    let fetchController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      history,\n      f.path,\n      fetchController.signal\n    );\n    let fetcherDsMatches = null;\n    if (cancelledFetcherLoads.has(key)) {\n      cancelledFetcherLoads.delete(key);\n      fetcherDsMatches = getTargetedDataStrategyMatches(\n        mapRouteProperties2,\n        manifest,\n        fetchRequest,\n        fetcherMatches,\n        fetcherMatch,\n        lazyRoutePropertiesToSkip,\n        scopedContext\n      );\n    } else if (isMidInitialLoad) {\n      if (isRevalidationRequired) {\n        fetcherDsMatches = getTargetedDataStrategyMatches(\n          mapRouteProperties2,\n          manifest,\n          fetchRequest,\n          fetcherMatches,\n          fetcherMatch,\n          lazyRoutePropertiesToSkip,\n          scopedContext\n        );\n      }\n    } else {\n      let shouldRevalidateArgs = {\n        ...baseShouldRevalidateArgs,\n        defaultShouldRevalidate: shouldSkipRevalidation ? false : isRevalidationRequired\n      };\n      if (shouldRevalidateLoader(fetcherMatch, shouldRevalidateArgs)) {\n        fetcherDsMatches = getTargetedDataStrategyMatches(\n          mapRouteProperties2,\n          manifest,\n          fetchRequest,\n          fetcherMatches,\n          fetcherMatch,\n          lazyRoutePropertiesToSkip,\n          scopedContext,\n          shouldRevalidateArgs\n        );\n      }\n    }\n    if (fetcherDsMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherDsMatches,\n        match: fetcherMatch,\n        request: fetchRequest,\n        controller: fetchController\n      });\n    }\n  });\n  return { dsMatches, revalidatingFetchers };\n}\nfunction shouldLoadRouteOnHydration(route, loaderData, errors) {\n  if (route.lazy) {\n    return true;\n  }\n  if (!route.loader) {\n    return false;\n  }\n  let hasData = loaderData != null && route.id in loaderData;\n  let hasError = errors != null && errors[route.id] !== void 0;\n  if (!hasData && hasError) {\n    return false;\n  }\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n  return !hasData && !hasError;\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew = (\n    // [a] -> [a, b]\n    !currentMatch || // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id\n  );\n  let isMissingData = !currentLoaderData.hasOwnProperty(match.route.id);\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname || // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\nfunction patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties2) {\n  let childrenToPatch;\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n  let uniqueChildren = children.filter(\n    (newRoute) => !childrenToPatch.some(\n      (existingRoute) => isSameRoute(newRoute, existingRoute)\n    )\n  );\n  let newRoutes = convertRoutesToDataRoutes(\n    uniqueChildren,\n    mapRouteProperties2,\n    [routeId || \"_\", \"patch\", String(childrenToPatch?.length || \"0\")],\n    manifest\n  );\n  childrenToPatch.push(...newRoutes);\n}\nfunction isSameRoute(newRoute, existingRoute) {\n  if (\"id\" in newRoute && \"id\" in existingRoute && newRoute.id === existingRoute.id) {\n    return true;\n  }\n  if (!(newRoute.index === existingRoute.index && newRoute.path === existingRoute.path && newRoute.caseSensitive === existingRoute.caseSensitive)) {\n    return false;\n  }\n  if ((!newRoute.children || newRoute.children.length === 0) && (!existingRoute.children || existingRoute.children.length === 0)) {\n    return true;\n  }\n  return newRoute.children.every(\n    (aChild, i) => existingRoute.children?.some((bChild) => isSameRoute(aChild, bChild))\n  );\n}\nvar lazyRoutePropertyCache = /* @__PURE__ */ new WeakMap();\nvar loadLazyRouteProperty = ({\n  key,\n  route,\n  manifest,\n  mapRouteProperties: mapRouteProperties2\n}) => {\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  if (!routeToUpdate.lazy || typeof routeToUpdate.lazy !== \"object\") {\n    return;\n  }\n  let lazyFn = routeToUpdate.lazy[key];\n  if (!lazyFn) {\n    return;\n  }\n  let cache = lazyRoutePropertyCache.get(routeToUpdate);\n  if (!cache) {\n    cache = {};\n    lazyRoutePropertyCache.set(routeToUpdate, cache);\n  }\n  let cachedPromise = cache[key];\n  if (cachedPromise) {\n    return cachedPromise;\n  }\n  let propertyPromise = (async () => {\n    let isUnsupported = isUnsupportedLazyRouteObjectKey(key);\n    let staticRouteValue = routeToUpdate[key];\n    let isStaticallyDefined = staticRouteValue !== void 0 && key !== \"hasErrorBoundary\";\n    if (isUnsupported) {\n      warning(\n        !isUnsupported,\n        \"Route property \" + key + \" is not a supported lazy route property. This property will be ignored.\"\n      );\n      cache[key] = Promise.resolve();\n    } else if (isStaticallyDefined) {\n      warning(\n        false,\n        `Route \"${routeToUpdate.id}\" has a static property \"${key}\" defined. The lazy property will be ignored.`\n      );\n    } else {\n      let value = await lazyFn();\n      if (value != null) {\n        Object.assign(routeToUpdate, { [key]: value });\n        Object.assign(routeToUpdate, mapRouteProperties2(routeToUpdate));\n      }\n    }\n    if (typeof routeToUpdate.lazy === \"object\") {\n      routeToUpdate.lazy[key] = void 0;\n      if (Object.values(routeToUpdate.lazy).every((value) => value === void 0)) {\n        routeToUpdate.lazy = void 0;\n      }\n    }\n  })();\n  cache[key] = propertyPromise;\n  return propertyPromise;\n};\nvar lazyRouteFunctionCache = /* @__PURE__ */ new WeakMap();\nfunction loadLazyRoute(route, type, manifest, mapRouteProperties2, lazyRoutePropertiesToSkip) {\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  if (!route.lazy) {\n    return {\n      lazyRoutePromise: void 0,\n      lazyHandlerPromise: void 0\n    };\n  }\n  if (typeof route.lazy === \"function\") {\n    let cachedPromise = lazyRouteFunctionCache.get(routeToUpdate);\n    if (cachedPromise) {\n      return {\n        lazyRoutePromise: cachedPromise,\n        lazyHandlerPromise: cachedPromise\n      };\n    }\n    let lazyRoutePromise2 = (async () => {\n      invariant(\n        typeof route.lazy === \"function\",\n        \"No lazy route function found\"\n      );\n      let lazyRoute = await route.lazy();\n      let routeUpdates = {};\n      for (let lazyRouteProperty in lazyRoute) {\n        let lazyValue = lazyRoute[lazyRouteProperty];\n        if (lazyValue === void 0) {\n          continue;\n        }\n        let isUnsupported = isUnsupportedLazyRouteFunctionKey(lazyRouteProperty);\n        let staticRouteValue = routeToUpdate[lazyRouteProperty];\n        let isStaticallyDefined = staticRouteValue !== void 0 && // This property isn't static since it should always be updated based\n        // on the route updates\n        lazyRouteProperty !== \"hasErrorBoundary\";\n        if (isUnsupported) {\n          warning(\n            !isUnsupported,\n            \"Route property \" + lazyRouteProperty + \" is not a supported property to be returned from a lazy route function. This property will be ignored.\"\n          );\n        } else if (isStaticallyDefined) {\n          warning(\n            !isStaticallyDefined,\n            `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" defined but its lazy function is also returning a value for this property. The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n          );\n        } else {\n          routeUpdates[lazyRouteProperty] = lazyValue;\n        }\n      }\n      Object.assign(routeToUpdate, routeUpdates);\n      Object.assign(routeToUpdate, {\n        // To keep things framework agnostic, we use the provided `mapRouteProperties`\n        // function to set the framework-aware properties (`element`/`hasErrorBoundary`)\n        // since the logic will differ between frameworks.\n        ...mapRouteProperties2(routeToUpdate),\n        lazy: void 0\n      });\n    })();\n    lazyRouteFunctionCache.set(routeToUpdate, lazyRoutePromise2);\n    lazyRoutePromise2.catch(() => {\n    });\n    return {\n      lazyRoutePromise: lazyRoutePromise2,\n      lazyHandlerPromise: lazyRoutePromise2\n    };\n  }\n  let lazyKeys = Object.keys(route.lazy);\n  let lazyPropertyPromises = [];\n  let lazyHandlerPromise = void 0;\n  for (let key of lazyKeys) {\n    if (lazyRoutePropertiesToSkip && lazyRoutePropertiesToSkip.includes(key)) {\n      continue;\n    }\n    let promise = loadLazyRouteProperty({\n      key,\n      route,\n      manifest,\n      mapRouteProperties: mapRouteProperties2\n    });\n    if (promise) {\n      lazyPropertyPromises.push(promise);\n      if (key === type) {\n        lazyHandlerPromise = promise;\n      }\n    }\n  }\n  let lazyRoutePromise = lazyPropertyPromises.length > 0 ? Promise.all(lazyPropertyPromises).then(() => {\n  }) : void 0;\n  lazyRoutePromise?.catch(() => {\n  });\n  lazyHandlerPromise?.catch(() => {\n  });\n  return {\n    lazyRoutePromise,\n    lazyHandlerPromise\n  };\n}\nfunction isNonNullable(value) {\n  return value !== void 0;\n}\nfunction loadLazyMiddlewareForMatches(matches, manifest, mapRouteProperties2) {\n  let promises = matches.map(({ route }) => {\n    if (typeof route.lazy !== \"object\" || !route.lazy.unstable_middleware) {\n      return void 0;\n    }\n    return loadLazyRouteProperty({\n      key: \"unstable_middleware\",\n      route,\n      manifest,\n      mapRouteProperties: mapRouteProperties2\n    });\n  }).filter(isNonNullable);\n  return promises.length > 0 ? Promise.all(promises) : void 0;\n}\nasync function defaultDataStrategy(args) {\n  let matchesToLoad = args.matches.filter((m) => m.shouldLoad);\n  let keyedResults = {};\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  results.forEach((result, i) => {\n    keyedResults[matchesToLoad[i].route.id] = result;\n  });\n  return keyedResults;\n}\nasync function defaultDataStrategyWithMiddleware(args) {\n  if (!args.matches.some((m) => m.route.unstable_middleware)) {\n    return defaultDataStrategy(args);\n  }\n  return runMiddlewarePipeline(\n    args,\n    false,\n    () => defaultDataStrategy(args),\n    (error, routeId) => ({ [routeId]: { type: \"error\", result: error } })\n  );\n}\nasync function runMiddlewarePipeline(args, propagateResult, handler, errorHandler) {\n  let { matches, request, params, context } = args;\n  let middlewareState = {\n    handlerResult: void 0\n  };\n  try {\n    let tuples = matches.flatMap(\n      (m) => m.route.unstable_middleware ? m.route.unstable_middleware.map((fn) => [m.route.id, fn]) : []\n    );\n    let result = await callRouteMiddleware(\n      { request, params, context },\n      tuples,\n      propagateResult,\n      middlewareState,\n      handler\n    );\n    return propagateResult ? result : middlewareState.handlerResult;\n  } catch (e) {\n    if (!middlewareState.middlewareError) {\n      throw e;\n    }\n    let result = await errorHandler(\n      middlewareState.middlewareError.error,\n      middlewareState.middlewareError.routeId\n    );\n    if (propagateResult || !middlewareState.handlerResult) {\n      return result;\n    }\n    return Object.assign(middlewareState.handlerResult, result);\n  }\n}\nasync function callRouteMiddleware(args, middlewares, propagateResult, middlewareState, handler, idx = 0) {\n  let { request } = args;\n  if (request.signal.aborted) {\n    if (request.signal.reason) {\n      throw request.signal.reason;\n    }\n    throw new Error(\n      `Request aborted without an \\`AbortSignal.reason\\`: ${request.method} ${request.url}`\n    );\n  }\n  let tuple = middlewares[idx];\n  if (!tuple) {\n    middlewareState.handlerResult = await handler();\n    return middlewareState.handlerResult;\n  }\n  let [routeId, middleware] = tuple;\n  let nextCalled = false;\n  let nextResult = void 0;\n  let next = async () => {\n    if (nextCalled) {\n      throw new Error(\"You may only call `next()` once per middleware\");\n    }\n    nextCalled = true;\n    let result = await callRouteMiddleware(\n      args,\n      middlewares,\n      propagateResult,\n      middlewareState,\n      handler,\n      idx + 1\n    );\n    if (propagateResult) {\n      nextResult = result;\n      return nextResult;\n    }\n  };\n  try {\n    let result = await middleware(\n      {\n        request: args.request,\n        params: args.params,\n        context: args.context\n      },\n      next\n    );\n    if (nextCalled) {\n      if (result === void 0) {\n        return nextResult;\n      } else {\n        return result;\n      }\n    } else {\n      return next();\n    }\n  } catch (error) {\n    if (!middlewareState.middlewareError) {\n      middlewareState.middlewareError = { routeId, error };\n    } else if (middlewareState.middlewareError.error !== error) {\n      middlewareState.middlewareError = { routeId, error };\n    }\n    throw error;\n  }\n}\nfunction getDataStrategyMatchLazyPromises(mapRouteProperties2, manifest, request, match, lazyRoutePropertiesToSkip) {\n  let lazyMiddlewarePromise = loadLazyRouteProperty({\n    key: \"unstable_middleware\",\n    route: match.route,\n    manifest,\n    mapRouteProperties: mapRouteProperties2\n  });\n  let lazyRoutePromises = loadLazyRoute(\n    match.route,\n    isMutationMethod(request.method) ? \"action\" : \"loader\",\n    manifest,\n    mapRouteProperties2,\n    lazyRoutePropertiesToSkip\n  );\n  return {\n    middleware: lazyMiddlewarePromise,\n    route: lazyRoutePromises.lazyRoutePromise,\n    handler: lazyRoutePromises.lazyHandlerPromise\n  };\n}\nfunction getDataStrategyMatch(mapRouteProperties2, manifest, request, match, lazyRoutePropertiesToSkip, scopedContext, shouldLoad, unstable_shouldRevalidateArgs = null) {\n  let isUsingNewApi = false;\n  let _lazyPromises = getDataStrategyMatchLazyPromises(\n    mapRouteProperties2,\n    manifest,\n    request,\n    match,\n    lazyRoutePropertiesToSkip\n  );\n  return {\n    ...match,\n    _lazyPromises,\n    shouldLoad,\n    unstable_shouldRevalidateArgs,\n    unstable_shouldCallHandler(defaultShouldRevalidate) {\n      isUsingNewApi = true;\n      if (!unstable_shouldRevalidateArgs) {\n        return shouldLoad;\n      }\n      if (typeof defaultShouldRevalidate === \"boolean\") {\n        return shouldRevalidateLoader(match, {\n          ...unstable_shouldRevalidateArgs,\n          defaultShouldRevalidate\n        });\n      }\n      return shouldRevalidateLoader(match, unstable_shouldRevalidateArgs);\n    },\n    resolve(handlerOverride) {\n      if (isUsingNewApi || shouldLoad || handlerOverride && request.method === \"GET\" && (match.route.lazy || match.route.loader)) {\n        return callLoaderOrAction({\n          request,\n          match,\n          lazyHandlerPromise: _lazyPromises?.handler,\n          lazyRoutePromise: _lazyPromises?.route,\n          handlerOverride,\n          scopedContext\n        });\n      }\n      return Promise.resolve({ type: \"data\" /* data */, result: void 0 });\n    }\n  };\n}\nfunction getTargetedDataStrategyMatches(mapRouteProperties2, manifest, request, matches, targetMatch, lazyRoutePropertiesToSkip, scopedContext, shouldRevalidateArgs = null) {\n  return matches.map((match) => {\n    if (match.route.id !== targetMatch.route.id) {\n      return {\n        ...match,\n        shouldLoad: false,\n        unstable_shouldRevalidateArgs: shouldRevalidateArgs,\n        unstable_shouldCallHandler: () => false,\n        _lazyPromises: getDataStrategyMatchLazyPromises(\n          mapRouteProperties2,\n          manifest,\n          request,\n          match,\n          lazyRoutePropertiesToSkip\n        ),\n        resolve: () => Promise.resolve({ type: \"data\", result: void 0 })\n      };\n    }\n    return getDataStrategyMatch(\n      mapRouteProperties2,\n      manifest,\n      request,\n      match,\n      lazyRoutePropertiesToSkip,\n      scopedContext,\n      true,\n      shouldRevalidateArgs\n    );\n  });\n}\nasync function callDataStrategyImpl(dataStrategyImpl, request, matches, fetcherKey, scopedContext, isStaticHandler) {\n  if (matches.some((m) => m._lazyPromises?.middleware)) {\n    await Promise.all(matches.map((m) => m._lazyPromises?.middleware));\n  }\n  let dataStrategyArgs = {\n    request,\n    params: matches[0].params,\n    context: scopedContext,\n    matches\n  };\n  let unstable_runClientMiddleware = isStaticHandler ? () => {\n    throw new Error(\n      \"You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`\"\n    );\n  } : (cb) => {\n    let typedDataStrategyArgs = dataStrategyArgs;\n    return runMiddlewarePipeline(\n      typedDataStrategyArgs,\n      false,\n      () => cb({\n        ...typedDataStrategyArgs,\n        fetcherKey,\n        unstable_runClientMiddleware: () => {\n          throw new Error(\n            \"Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler\"\n          );\n        }\n      }),\n      (error, routeId) => ({\n        [routeId]: { type: \"error\", result: error }\n      })\n    );\n  };\n  let results = await dataStrategyImpl({\n    ...dataStrategyArgs,\n    fetcherKey,\n    unstable_runClientMiddleware\n  });\n  try {\n    await Promise.all(\n      matches.flatMap((m) => [m._lazyPromises?.handler, m._lazyPromises?.route])\n    );\n  } catch (e) {\n  }\n  return results;\n}\nasync function callLoaderOrAction({\n  request,\n  match,\n  lazyHandlerPromise,\n  lazyRoutePromise,\n  handlerOverride,\n  scopedContext\n}) {\n  let result;\n  let onReject;\n  let isAction = isMutationMethod(request.method);\n  let type = isAction ? \"action\" : \"loader\";\n  let runHandler = (handler) => {\n    let reject;\n    let abortPromise = new Promise((_, r) => reject = r);\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    let actualHandler = (ctx) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean \"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: scopedContext\n        },\n        ...ctx !== void 0 ? [ctx] : []\n      );\n    };\n    let handlerPromise = (async () => {\n      try {\n        let val = await (handlerOverride ? handlerOverride((ctx) => actualHandler(ctx)) : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n  try {\n    let handler = isAction ? match.route.action : match.route.loader;\n    if (lazyHandlerPromise || lazyRoutePromise) {\n      if (handler) {\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          // Ensure all lazy route promises are resolved before continuing\n          lazyHandlerPromise,\n          lazyRoutePromise\n        ]);\n        if (handlerError !== void 0) {\n          throw handlerError;\n        }\n        result = value;\n      } else {\n        await lazyHandlerPromise;\n        let handler2 = isAction ? match.route.action : match.route.loader;\n        if (handler2) {\n          [result] = await Promise.all([runHandler(handler2), lazyRoutePromise]);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id\n          });\n        } else {\n          return { type: \"data\" /* data */, result: void 0 };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n  } catch (e) {\n    return { type: \"error\" /* error */, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n  return result;\n}\nasync function convertDataStrategyResultToDataResult(dataStrategyResult) {\n  let { result, type } = dataStrategyResult;\n  if (isResponse(result)) {\n    let data2;\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data2 = null;\n        } else {\n          data2 = await result.json();\n        }\n      } else {\n        data2 = await result.text();\n      }\n    } catch (e) {\n      return { type: \"error\" /* error */, error: e };\n    }\n    if (type === \"error\" /* error */) {\n      return {\n        type: \"error\" /* error */,\n        error: new ErrorResponseImpl(result.status, result.statusText, data2),\n        statusCode: result.status,\n        headers: result.headers\n      };\n    }\n    return {\n      type: \"data\" /* data */,\n      data: data2,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (type === \"error\" /* error */) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: \"error\" /* error */,\n          error: result.data,\n          statusCode: result.init?.status,\n          headers: result.init?.headers ? new Headers(result.init.headers) : void 0\n        };\n      }\n      return {\n        type: \"error\" /* error */,\n        error: new ErrorResponseImpl(\n          result.init?.status || 500,\n          void 0,\n          result.data\n        ),\n        statusCode: isRouteErrorResponse(result) ? result.status : void 0,\n        headers: result.init?.headers ? new Headers(result.init.headers) : void 0\n      };\n    }\n    return {\n      type: \"error\" /* error */,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : void 0\n    };\n  }\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: \"data\" /* data */,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers ? new Headers(result.init.headers) : void 0\n    };\n  }\n  return { type: \"data\" /* data */, data: result };\n}\nfunction normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      location\n    );\n    response.headers.set(\"Location\", location);\n  }\n  return response;\n}\nfunction normalizeRedirectLocation(location, currentUrl, basename) {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\") ? new URL(currentUrl.protocol + normalizedLocation) : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = { signal };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    init.method = formMethod.toUpperCase();\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      init.body = submission.text;\n    } else if (formEncType === \"application/x-www-form-urlencoded\" && submission.formData) {\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      init.body = submission.formData;\n    }\n  }\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n  return searchParams;\n}\nfunction convertSearchParamsToFormData(searchParams) {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\nfunction processRouteLoaderData(matches, results, pendingActionResult, isStaticHandler = false, skipLoaderErrorBubbling = false) {\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {};\n  let pendingError = pendingActionResult && isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : void 0;\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      if (pendingError !== void 0) {\n        error = pendingError;\n        pendingError = void 0;\n      }\n      errors = errors || {};\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n      if (!isStaticHandler) {\n        loaderData[id] = ResetLoaderDataSymbol;\n      }\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      loaderData[id] = result.data;\n      if (result.statusCode && result.statusCode !== 200 && !foundError) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n  if (pendingError !== void 0 && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    if (pendingActionResult[2]) {\n      loaderData[pendingActionResult[2]] = void 0;\n    }\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, results, pendingActionResult, revalidatingFetchers, fetcherResults) {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult\n  );\n  revalidatingFetchers.filter((f) => !f.matches || f.matches.some((m) => m.shouldLoad)).forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n    if (controller && controller.signal.aborted) {\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n  return { loaderData, errors };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = Object.entries(newLoaderData).filter(([, v]) => v !== ResetLoaderDataSymbol).reduce((merged, [k, v]) => {\n    merged[k] = v;\n    return merged;\n  }, {});\n  for (let match of matches) {\n    let id = match.route.id;\n    if (!newLoaderData.hasOwnProperty(id) && loaderData.hasOwnProperty(id) && match.route.loader) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\nfunction getActionDataForCommit(pendingActionResult) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1]) ? {\n    // Clear out prior actionData on errors\n    actionData: {}\n  } : {\n    actionData: {\n      [pendingActionResult[0]]: pendingActionResult[1].data\n    }\n  };\n}\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  let route = routes.length === 1 ? routes[0] : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n    id: `__shim-error-route__`\n  };\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route\n      }\n    ],\n    route\n  };\n}\nfunction getInternalRouterError(status, {\n  pathname,\n  routeId,\n  method,\n  type,\n  message\n} = {}) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = `You made a ${method} request to \"${pathname}\" but did not provide a \\`loader\\` for route \"${routeId}\", so there is no way to handle the request.`;\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = `You made a ${method.toUpperCase()} request to \"${pathname}\" but did not provide an \\`action\\` for route \"${routeId}\", so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\nfunction findRedirect(results) {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\nfunction isHashChangeOnly(a, b) {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n  if (a.hash === \"\") {\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    return true;\n  } else if (b.hash !== \"\") {\n    return true;\n  }\n  return false;\n}\nfunction isDataStrategyResult(result) {\n  return result != null && typeof result === \"object\" && \"type\" in result && \"result\" in result && (result.type === \"data\" /* data */ || result.type === \"error\" /* error */);\n}\nfunction isRedirectDataStrategyResult(result) {\n  return isResponse(result.result) && redirectStatusCodes.has(result.result.status);\n}\nfunction isErrorResult(result) {\n  return result.type === \"error\" /* error */;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === \"redirect\" /* redirect */;\n}\nfunction isDataWithResponseInit(value) {\n  return typeof value === \"object\" && value != null && \"type\" in value && \"data\" in value && \"init\" in value && value.type === \"DataWithResponseInit\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectStatusCode(statusCode) {\n  return redirectStatusCodes.has(statusCode);\n}\nfunction isRedirectResponse(result) {\n  return isResponse(result) && isRedirectStatusCode(result.status) && result.headers.has(\"Location\");\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method.toUpperCase());\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method.toUpperCase());\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    return matches[matches.length - 1];\n  }\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\nfunction getSubmissionFromNavigation(navigation) {\n  let { formMethod, formAction, formEncType, text, formData, json } = navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: void 0,\n      json: void 0,\n      text\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: void 0,\n      text: void 0\n    };\n  } else if (json !== void 0) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: void 0,\n      json,\n      text: void 0\n    };\n  }\n}\nfunction getLoadingNavigation(location, submission) {\n  if (submission) {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text\n    };\n    return navigation;\n  } else {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: void 0,\n      formAction: void 0,\n      formEncType: void 0,\n      formData: void 0,\n      json: void 0,\n      text: void 0\n    };\n    return navigation;\n  }\n}\nfunction getSubmittingNavigation(location, submission) {\n  let navigation = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text\n  };\n  return navigation;\n}\nfunction getLoadingFetcher(submission, data2) {\n  if (submission) {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data: data2\n    };\n    return fetcher;\n  } else {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: void 0,\n      formAction: void 0,\n      formEncType: void 0,\n      formData: void 0,\n      json: void 0,\n      text: void 0,\n      data: data2\n    };\n    return fetcher;\n  }\n}\nfunction getSubmittingFetcher(submission, existingFetcher) {\n  let fetcher = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : void 0\n  };\n  return fetcher;\n}\nfunction getDoneFetcher(data2) {\n  let fetcher = {\n    state: \"idle\",\n    formMethod: void 0,\n    formAction: void 0,\n    formEncType: void 0,\n    formData: void 0,\n    json: void 0,\n    text: void 0,\n    data: data2\n  };\n  return fetcher;\n}\nfunction restoreAppliedTransitions(_window, transitions) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n  }\n}\nfunction persistAppliedTransitions(_window, transitions) {\n  if (transitions.size > 0) {\n    let json = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\nfunction createDeferred() {\n  let resolve;\n  let reject;\n  let promise = new Promise((res, rej) => {\n    resolve = async (val) => {\n      res(val);\n      try {\n        await promise;\n      } catch (e) {\n      }\n    };\n    reject = async (error) => {\n      rej(error);\n      try {\n        await promise;\n      } catch (e) {\n      }\n    };\n  });\n  return {\n    promise,\n    //@ts-ignore\n    resolve,\n    //@ts-ignore\n    reject\n  };\n}\n\n// lib/components.tsx\nimport * as React3 from \"react\";\n\n// lib/context.ts\nimport * as React from \"react\";\nvar DataRouterContext = React.createContext(null);\nDataRouterContext.displayName = \"DataRouter\";\nvar DataRouterStateContext = React.createContext(null);\nDataRouterStateContext.displayName = \"DataRouterState\";\nvar ViewTransitionContext = React.createContext({\n  isTransitioning: false\n});\nViewTransitionContext.displayName = \"ViewTransition\";\nvar FetchersContext = React.createContext(\n  /* @__PURE__ */ new Map()\n);\nFetchersContext.displayName = \"Fetchers\";\nvar AwaitContext = React.createContext(null);\nAwaitContext.displayName = \"Await\";\nvar NavigationContext = React.createContext(\n  null\n);\nNavigationContext.displayName = \"Navigation\";\nvar LocationContext = React.createContext(\n  null\n);\nLocationContext.displayName = \"Location\";\nvar RouteContext = React.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nRouteContext.displayName = \"Route\";\nvar RouteErrorContext = React.createContext(null);\nRouteErrorContext.displayName = \"RouteError\";\nvar ENABLE_DEV_WARNINGS = true;\n\n// lib/hooks.tsx\nimport * as React2 from \"react\";\nfunction useHref(to, { relative } = {}) {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n  let { basename, navigator } = React2.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n  let joinedPathname = pathname;\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\nfunction useInRouterContext() {\n  return React2.useContext(LocationContext) != null;\n}\nfunction useLocation() {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n  return React2.useContext(LocationContext).location;\n}\nfunction useNavigationType() {\n  return React2.useContext(LocationContext).navigationType;\n}\nfunction useMatch(pattern) {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n  let { pathname } = useLocation();\n  return React2.useMemo(\n    () => matchPath(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\nvar navigateEffectWarning = `You should call navigate() in a React.useEffect(), not when your component is first rendered.`;\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = React2.useContext(NavigationContext).static;\n  if (!isStatic) {\n    React2.useLayoutEffect(cb);\n  }\n}\nfunction useNavigate() {\n  let { isDataRoute } = React2.useContext(RouteContext);\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n  let dataRouterContext = React2.useContext(DataRouterContext);\n  let { basename, navigator } = React2.useContext(NavigationContext);\n  let { matches } = React2.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(getResolveToMatches(matches));\n  let activeRef = React2.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React2.useCallback(\n    (to, options = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n      if (!activeRef.current) return;\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n      }\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext\n    ]\n  );\n  return navigate;\n}\nvar OutletContext = React2.createContext(null);\nfunction useOutletContext() {\n  return React2.useContext(OutletContext);\n}\nfunction useOutlet(context) {\n  let outlet = React2.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /* @__PURE__ */ React2.createElement(OutletContext.Provider, { value: context }, outlet);\n  }\n  return outlet;\n}\nfunction useParams() {\n  let { matches } = React2.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\nfunction useResolvedPath(to, { relative } = {}) {\n  let { matches } = React2.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(getResolveToMatches(matches));\n  return React2.useMemo(\n    () => resolveTo(\n      to,\n      JSON.parse(routePathnamesJson),\n      locationPathname,\n      relative === \"path\"\n    ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n  let { navigator } = React2.useContext(NavigationContext);\n  let { matches: parentMatches } = React2.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (ENABLE_DEV_WARNINGS) {\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\") || parentPath.endsWith(\"*?\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at \"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the parent route path has no trailing \"*\". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path=\"${parentPath}\"> to <Route path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    invariant(\n      parentPathnameBase === \"/\" || parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n  if (ENABLE_DEV_WARNINGS) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n    warning(\n      matches == null || matches[matches.length - 1].route.element !== void 0 || matches[matches.length - 1].route.Component !== void 0 || matches[matches.length - 1].route.lazy !== void 0,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n  let renderedMatches = _renderMatches(\n    matches && matches.map(\n      (match) => Object.assign({}, match, {\n        params: Object.assign({}, parentParams, match.params),\n        pathname: joinPaths([\n          parentPathnameBase,\n          // Re-encode pathnames that were decoded inside matchRoutes\n          navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname\n        ]),\n        pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([\n          parentPathnameBase,\n          // Re-encode pathnames that were decoded inside matchRoutes\n          navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase\n        ])\n      })\n    ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n  if (locationArg && renderedMatches) {\n    return /* @__PURE__ */ React2.createElement(\n      LocationContext.Provider,\n      {\n        value: {\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location\n          },\n          navigationType: \"POP\" /* Pop */\n        }\n      },\n      renderedMatches\n    );\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? `${error.status} ${error.statusText}` : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n  let devInfo = null;\n  if (ENABLE_DEV_WARNINGS) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n    devInfo = /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\"p\", null, \"\\u{1F4BF} Hey developer \\u{1F44B}\"), /* @__PURE__ */ React2.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /* @__PURE__ */ React2.createElement(\"code\", { style: codeStyles }, \"ErrorBoundary\"), \" or\", \" \", /* @__PURE__ */ React2.createElement(\"code\", { style: codeStyles }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\"h2\", null, \"Unexpected Application Error!\"), /* @__PURE__ */ React2.createElement(\"h3\", { style: { fontStyle: \"italic\" } }, message), stack ? /* @__PURE__ */ React2.createElement(\"pre\", { style: preStyles }, stack) : null, devInfo);\n}\nvar defaultErrorElement = /* @__PURE__ */ React2.createElement(DefaultErrorComponent, null);\nvar RenderErrorBoundary = class extends React2.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n    return {\n      error: props.error !== void 0 ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n  render() {\n    return this.state.error !== void 0 ? /* @__PURE__ */ React2.createElement(RouteContext.Provider, { value: this.props.routeContext }, /* @__PURE__ */ React2.createElement(\n      RouteErrorContext.Provider,\n      {\n        value: this.state.error,\n        children: this.props.component\n      }\n    )) : this.props.children;\n  }\n};\nfunction RenderedRoute({ routeContext, match, children }) {\n  let dataRouterContext = React2.useContext(DataRouterContext);\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /* @__PURE__ */ React2.createElement(RouteContext.Provider, { value: routeContext }, children);\n}\nfunction _renderMatches(matches, parentMatches = [], dataRouterState = null, future = null) {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n    if (dataRouterState.errors) {\n      matches = dataRouterState.matches;\n    } else if (parentMatches.length === 0 && !dataRouterState.initialized && dataRouterState.matches.length > 0) {\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== void 0\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let { loaderData, errors: errors2 } = dataRouterState;\n        let needsToRunLoader = match.route.loader && !loaderData.hasOwnProperty(match.route.id) && (!errors2 || errors2[match.route.id] === void 0);\n        if (match.route.lazy || needsToRunLoader) {\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : void 0;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches2 = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        children = /* @__PURE__ */ React2.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /* @__PURE__ */ React2.createElement(\n        RenderedRoute,\n        {\n          match,\n          routeContext: {\n            outlet,\n            matches: matches2,\n            isDataRoute: dataRouterState != null\n          },\n          children\n        }\n      );\n    };\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /* @__PURE__ */ React2.createElement(\n      RenderErrorBoundary,\n      {\n        location: dataRouterState.location,\n        revalidation: dataRouterState.revalidation,\n        component: errorElement,\n        error,\n        children: getChildren(),\n        routeContext: { outlet: null, matches: matches2, isDataRoute: true }\n      }\n    ) : getChildren();\n  }, null);\n}\nfunction getDataRouterConsoleError(hookName) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`;\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React2.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React2.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React2.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\nfunction useRouteId() {\n  return useCurrentRouteId(\"useRouteId\" /* UseRouteId */);\n}\nfunction useNavigation() {\n  let state = useDataRouterState(\"useNavigation\" /* UseNavigation */);\n  return state.navigation;\n}\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(\"useRevalidator\" /* UseRevalidator */);\n  let state = useDataRouterState(\"useRevalidator\" /* UseRevalidator */);\n  let revalidate = React2.useCallback(async () => {\n    await dataRouterContext.router.revalidate();\n  }, [dataRouterContext.router]);\n  return React2.useMemo(\n    () => ({ revalidate, state: state.revalidation }),\n    [revalidate, state.revalidation]\n  );\n}\nfunction useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    \"useMatches\" /* UseMatches */\n  );\n  return React2.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\nfunction useLoaderData() {\n  let state = useDataRouterState(\"useLoaderData\" /* UseLoaderData */);\n  let routeId = useCurrentRouteId(\"useLoaderData\" /* UseLoaderData */);\n  return state.loaderData[routeId];\n}\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(\"useRouteLoaderData\" /* UseRouteLoaderData */);\n  return state.loaderData[routeId];\n}\nfunction useActionData() {\n  let state = useDataRouterState(\"useActionData\" /* UseActionData */);\n  let routeId = useCurrentRouteId(\"useLoaderData\" /* UseLoaderData */);\n  return state.actionData ? state.actionData[routeId] : void 0;\n}\nfunction useRouteError() {\n  let error = React2.useContext(RouteErrorContext);\n  let state = useDataRouterState(\"useRouteError\" /* UseRouteError */);\n  let routeId = useCurrentRouteId(\"useRouteError\" /* UseRouteError */);\n  if (error !== void 0) {\n    return error;\n  }\n  return state.errors?.[routeId];\n}\nfunction useAsyncValue() {\n  let value = React2.useContext(AwaitContext);\n  return value?._data;\n}\nfunction useAsyncError() {\n  let value = React2.useContext(AwaitContext);\n  return value?._error;\n}\nvar blockerId = 0;\nfunction useBlocker(shouldBlock) {\n  let { router, basename } = useDataRouterContext(\"useBlocker\" /* UseBlocker */);\n  let state = useDataRouterState(\"useBlocker\" /* UseBlocker */);\n  let [blockerKey, setBlockerKey] = React2.useState(\"\");\n  let blockerFunction = React2.useCallback(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname: stripBasename(currentLocation.pathname, basename) || currentLocation.pathname\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname: stripBasename(nextLocation.pathname, basename) || nextLocation.pathname\n        },\n        historyAction\n      });\n    },\n    [basename, shouldBlock]\n  );\n  React2.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n  React2.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : IDLE_BLOCKER;\n}\nfunction useNavigateStable() {\n  let { router } = useDataRouterContext(\"useNavigate\" /* UseNavigateStable */);\n  let id = useCurrentRouteId(\"useNavigate\" /* UseNavigateStable */);\n  let activeRef = React2.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React2.useCallback(\n    async (to, options = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n      if (!activeRef.current) return;\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        await router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n  return navigate;\n}\nvar alreadyWarned = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n\n// lib/server-runtime/warnings.ts\nvar alreadyWarned2 = {};\nfunction warnOnce(condition, message) {\n  if (!condition && !alreadyWarned2[message]) {\n    alreadyWarned2[message] = true;\n    console.warn(message);\n  }\n}\n\n// lib/components.tsx\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.hasErrorBoundary || route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (ENABLE_DEV_WARNINGS) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - `Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React3.createElement(route.Component),\n      Component: void 0\n    });\n  }\n  if (route.HydrateFallback) {\n    if (ENABLE_DEV_WARNINGS) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React3.createElement(route.HydrateFallback),\n      HydrateFallback: void 0\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (ENABLE_DEV_WARNINGS) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React3.createElement(route.ErrorBoundary),\n      ErrorBoundary: void 0\n    });\n  }\n  return updates;\n}\nvar hydrationRouteProperties = [\n  \"HydrateFallback\",\n  \"hydrateFallbackElement\"\n];\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts?.basename,\n    unstable_getContext: opts?.unstable_getContext,\n    future: opts?.future,\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    hydrationRouteProperties,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation\n  }).initialize();\n}\nvar Deferred = class {\n  constructor() {\n    this.status = \"pending\";\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n};\nfunction RouterProvider({\n  router,\n  flushSync: reactDomFlushSyncImpl\n}) {\n  let [state, setStateImpl] = React3.useState(router.state);\n  let [pendingState, setPendingState] = React3.useState();\n  let [vtContext, setVtContext] = React3.useState({\n    isTransitioning: false\n  });\n  let [renderDfd, setRenderDfd] = React3.useState();\n  let [transition, setTransition] = React3.useState();\n  let [interruption, setInterruption] = React3.useState();\n  let fetcherData = React3.useRef(/* @__PURE__ */ new Map());\n  let setState = React3.useCallback(\n    (newState, { deletedFetchers, flushSync, viewTransitionOpts }) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== void 0) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n      warnOnce(\n        flushSync === false || reactDomFlushSyncImpl != null,\n        'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from \"react-router/dom\"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.'\n      );\n      let isViewTransitionAvailable = router.window != null && router.window.document != null && typeof router.window.document.startViewTransition === \"function\";\n      warnOnce(\n        viewTransitionOpts == null || isViewTransitionAvailable,\n        \"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available.\"\n      );\n      if (!viewTransitionOpts || !isViewTransitionAvailable) {\n        if (reactDomFlushSyncImpl && flushSync) {\n          reactDomFlushSyncImpl(() => setStateImpl(newState));\n        } else {\n          React3.startTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n      if (reactDomFlushSyncImpl && flushSync) {\n        reactDomFlushSyncImpl(() => {\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation\n          });\n        });\n        let t = router.window.document.startViewTransition(() => {\n          reactDomFlushSyncImpl(() => setStateImpl(newState));\n        });\n        t.finished.finally(() => {\n          reactDomFlushSyncImpl(() => {\n            setRenderDfd(void 0);\n            setTransition(void 0);\n            setPendingState(void 0);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n        reactDomFlushSyncImpl(() => setTransition(t));\n        return;\n      }\n      if (transition) {\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation\n        });\n      } else {\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation\n        });\n      }\n    },\n    [router.window, reactDomFlushSyncImpl, transition, renderDfd]\n  );\n  React3.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  React3.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred());\n    }\n  }, [vtContext]);\n  React3.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition2 = router.window.document.startViewTransition(async () => {\n        React3.startTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition2.finished.finally(() => {\n        setRenderDfd(void 0);\n        setTransition(void 0);\n        setPendingState(void 0);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition2);\n    }\n  }, [pendingState, renderDfd, router.window]);\n  React3.useEffect(() => {\n    if (renderDfd && pendingState && state.location.key === pendingState.location.key) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n  React3.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation\n      });\n      setInterruption(void 0);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n  let navigator = React3.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state2, opts) => router.navigate(to, {\n        state: state2,\n        preventScrollReset: opts?.preventScrollReset\n      }),\n      replace: (to, state2, opts) => router.navigate(to, {\n        replace: true,\n        state: state2,\n        preventScrollReset: opts?.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React3.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename\n    }),\n    [router, navigator, basename]\n  );\n  return /* @__PURE__ */ React3.createElement(React3.Fragment, null, /* @__PURE__ */ React3.createElement(DataRouterContext.Provider, { value: dataRouterContext }, /* @__PURE__ */ React3.createElement(DataRouterStateContext.Provider, { value: state }, /* @__PURE__ */ React3.createElement(FetchersContext.Provider, { value: fetcherData.current }, /* @__PURE__ */ React3.createElement(ViewTransitionContext.Provider, { value: vtContext }, /* @__PURE__ */ React3.createElement(\n    Router,\n    {\n      basename,\n      location: state.location,\n      navigationType: state.historyAction,\n      navigator\n    },\n    /* @__PURE__ */ React3.createElement(\n      MemoizedDataRoutes,\n      {\n        routes: router.routes,\n        future: router.future,\n        state\n      }\n    )\n  ))))), null);\n}\nvar MemoizedDataRoutes = React3.memo(DataRoutes);\nfunction DataRoutes({\n  routes,\n  future,\n  state\n}) {\n  return useRoutesImpl(routes, void 0, state, future);\n}\nfunction MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex\n}) {\n  let historyRef = React3.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React3.useState({\n    action: history.action,\n    location: history.location\n  });\n  let setState = React3.useCallback(\n    (newState) => {\n      React3.startTransition(() => setStateImpl(newState));\n    },\n    [setStateImpl]\n  );\n  React3.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /* @__PURE__ */ React3.createElement(\n    Router,\n    {\n      basename,\n      children,\n      location: state.location,\n      navigationType: state.action,\n      navigator: history\n    }\n  );\n}\nfunction Navigate({\n  to,\n  replace: replace2,\n  state,\n  relative\n}) {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n  let { static: isStatic } = React3.useContext(NavigationContext);\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.`\n  );\n  let { matches } = React3.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n  React3.useEffect(() => {\n    navigate(JSON.parse(jsonPath), { replace: replace2, state, relative });\n  }, [navigate, jsonPath, relative, replace2, state]);\n  return null;\n}\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\nfunction Route(_props) {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\nfunction Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = \"POP\" /* Pop */,\n  navigator,\n  static: staticProp = false\n}) {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>. You should never have more than one in your app.`\n  );\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React3.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {}\n    }),\n    [basename, navigator, staticProp]\n  );\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = React3.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL \"${pathname}${search}${hash}\" because it does not start with the basename, so the <Router> won't render anything.`\n  );\n  if (locationContext == null) {\n    return null;\n  }\n  return /* @__PURE__ */ React3.createElement(NavigationContext.Provider, { value: navigationContext }, /* @__PURE__ */ React3.createElement(LocationContext.Provider, { children, value: locationContext }));\n}\nfunction Routes({\n  children,\n  location\n}) {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\nfunction Await({\n  children,\n  errorElement,\n  resolve\n}) {\n  return /* @__PURE__ */ React3.createElement(AwaitErrorBoundary, { resolve, errorElement }, /* @__PURE__ */ React3.createElement(ResolveAwait, null, children));\n}\nvar AwaitErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n  render() {\n    let { children, errorElement, resolve } = this.props;\n    let promise = null;\n    let status = 0 /* pending */;\n    if (!(resolve instanceof Promise)) {\n      status = 1 /* success */;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      status = 2 /* error */;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {\n      });\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if (resolve._tracked) {\n      promise = resolve;\n      status = \"_error\" in promise ? 2 /* error */ : \"_data\" in promise ? 1 /* success */ : 0 /* pending */;\n    } else {\n      status = 0 /* pending */;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data2) => Object.defineProperty(resolve, \"_data\", { get: () => data2 }),\n        (error) => Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n    if (status === 2 /* error */ && !errorElement) {\n      throw promise._error;\n    }\n    if (status === 2 /* error */) {\n      return /* @__PURE__ */ React3.createElement(AwaitContext.Provider, { value: promise, children: errorElement });\n    }\n    if (status === 1 /* success */) {\n      return /* @__PURE__ */ React3.createElement(AwaitContext.Provider, { value: promise, children });\n    }\n    throw promise;\n  }\n};\nfunction ResolveAwait({\n  children\n}) {\n  let data2 = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data2) : children;\n  return /* @__PURE__ */ React3.createElement(React3.Fragment, null, toRender);\n}\nfunction createRoutesFromChildren(children, parentPath = []) {\n  let routes = [];\n  React3.Children.forEach(children, (element, index) => {\n    if (!React3.isValidElement(element)) {\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === React3.Fragment) {\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n    invariant(\n      element.type === Route,\n      `[${typeof element.type === \"string\" ? element.type : element.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      hydrateFallbackElement: element.props.hydrateFallbackElement,\n      HydrateFallback: element.props.HydrateFallback,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.hasErrorBoundary === true || element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n    routes.push(route);\n  });\n  return routes;\n}\nvar createRoutesFromElements = createRoutesFromChildren;\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\nfunction withComponentProps(Component4) {\n  return function WithComponentProps() {\n    const props = {\n      params: useParams(),\n      loaderData: useLoaderData(),\n      actionData: useActionData(),\n      matches: useMatches()\n    };\n    return React3.createElement(Component4, props);\n  };\n}\nfunction withHydrateFallbackProps(HydrateFallback) {\n  return function WithHydrateFallbackProps() {\n    const props = {\n      params: useParams(),\n      loaderData: useLoaderData(),\n      actionData: useActionData()\n    };\n    return React3.createElement(HydrateFallback, props);\n  };\n}\nfunction withErrorBoundaryProps(ErrorBoundary) {\n  return function WithErrorBoundaryProps() {\n    const props = {\n      params: useParams(),\n      loaderData: useLoaderData(),\n      actionData: useActionData(),\n      error: useRouteError()\n    };\n    return React3.createElement(ErrorBoundary, props);\n  };\n}\n\n// lib/dom/lib.tsx\nimport * as React10 from \"react\";\n\n// lib/dom/dom.ts\nvar defaultMethod = \"get\";\nvar defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && // Ignore everything but left clicks\n  (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event);\n}\nfunction createSearchParams(init = \"\") {\n  return new URLSearchParams(\n    typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo2, key) => {\n      let value = init[key];\n      return memo2.concat(\n        Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n      );\n    }, [])\n  );\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n  return searchParams;\n}\nvar _formDataSupportsSubmitter = null;\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\nvar supportedFormEncTypes = /* @__PURE__ */ new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\"\n]);\nfunction getFormEncType(encType) {\n  if (encType != null && !supportedFormEncTypes.has(encType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` and will default to \"${defaultEncType}\"`\n    );\n    return null;\n  }\n  return encType;\n}\nfunction getFormSubmissionInfo(target, basename) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  let body;\n  if (isFormElement(target)) {\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(target);\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"formenctype\")) || getFormEncType(form.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(form, target);\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or <input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = void 0;\n  }\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n\n// lib/dom/ssr/components.tsx\nimport * as React9 from \"react\";\n\n// lib/dom/ssr/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\n// lib/dom/ssr/routeModules.ts\nasync function loadRouteModule(route, routeModulesCache) {\n  if (route.id in routeModulesCache) {\n    return routeModulesCache[route.id];\n  }\n  try {\n    let routeModule = await import(\n      /* @vite-ignore */\n      /* webpackIgnore: true */\n      route.module\n    );\n    routeModulesCache[route.id] = routeModule;\n    return routeModule;\n  } catch (error) {\n    console.error(\n      `Error loading route module \\`${route.module}\\`, reloading page...`\n    );\n    console.error(error);\n    if (window.__reactRouterContext && window.__reactRouterContext.isSpaMode && // @ts-expect-error\n    import.meta.hot) {\n      throw error;\n    }\n    window.location.reload();\n    return new Promise(() => {\n    });\n  }\n}\n\n// lib/dom/ssr/links.ts\nfunction getKeyedLinksForMatches(matches, routeModules, manifest) {\n  let descriptors = matches.map((match) => {\n    let module = routeModules[match.route.id];\n    let route = manifest.routes[match.route.id];\n    return [\n      route && route.css ? route.css.map((href2) => ({ rel: \"stylesheet\", href: href2 })) : [],\n      module?.links?.() || []\n    ];\n  }).flat(2);\n  let preloads = getModuleLinkHrefs(matches, manifest);\n  return dedupeLinkDescriptors(descriptors, preloads);\n}\nfunction getRouteCssDescriptors(route) {\n  if (!route.css) return [];\n  return route.css.map((href2) => ({ rel: \"stylesheet\", href: href2 }));\n}\nasync function prefetchRouteCss(route) {\n  if (!route.css) return;\n  let descriptors = getRouteCssDescriptors(route);\n  await Promise.all(descriptors.map(prefetchStyleLink));\n}\nasync function prefetchStyleLinks(route, routeModule) {\n  if (!route.css && !routeModule.links || !isPreloadSupported()) return;\n  let descriptors = [];\n  if (route.css) {\n    descriptors.push(...getRouteCssDescriptors(route));\n  }\n  if (routeModule.links) {\n    descriptors.push(...routeModule.links());\n  }\n  if (descriptors.length === 0) return;\n  let styleLinks = [];\n  for (let descriptor of descriptors) {\n    if (!isPageLinkDescriptor(descriptor) && descriptor.rel === \"stylesheet\") {\n      styleLinks.push({\n        ...descriptor,\n        rel: \"preload\",\n        as: \"style\"\n      });\n    }\n  }\n  await Promise.all(styleLinks.map(prefetchStyleLink));\n}\nasync function prefetchStyleLink(descriptor) {\n  return new Promise((resolve) => {\n    if (descriptor.media && !window.matchMedia(descriptor.media).matches || document.querySelector(\n      `link[rel=\"stylesheet\"][href=\"${descriptor.href}\"]`\n    )) {\n      return resolve();\n    }\n    let link = document.createElement(\"link\");\n    Object.assign(link, descriptor);\n    function removeLink() {\n      if (document.head.contains(link)) {\n        document.head.removeChild(link);\n      }\n    }\n    link.onload = () => {\n      removeLink();\n      resolve();\n    };\n    link.onerror = () => {\n      removeLink();\n      resolve();\n    };\n    document.head.appendChild(link);\n  });\n}\nfunction isPageLinkDescriptor(object) {\n  return object != null && typeof object.page === \"string\";\n}\nfunction isHtmlLinkDescriptor(object) {\n  if (object == null) {\n    return false;\n  }\n  if (object.href == null) {\n    return object.rel === \"preload\" && typeof object.imageSrcSet === \"string\" && typeof object.imageSizes === \"string\";\n  }\n  return typeof object.rel === \"string\" && typeof object.href === \"string\";\n}\nasync function getKeyedPrefetchLinks(matches, manifest, routeModules) {\n  let links = await Promise.all(\n    matches.map(async (match) => {\n      let route = manifest.routes[match.route.id];\n      if (route) {\n        let mod = await loadRouteModule(route, routeModules);\n        return mod.links ? mod.links() : [];\n      }\n      return [];\n    })\n  );\n  return dedupeLinkDescriptors(\n    links.flat(1).filter(isHtmlLinkDescriptor).filter((link) => link.rel === \"stylesheet\" || link.rel === \"preload\").map(\n      (link) => link.rel === \"stylesheet\" ? { ...link, rel: \"prefetch\", as: \"style\" } : { ...link, rel: \"prefetch\" }\n    )\n  );\n}\nfunction getNewMatchesForLinks(page, nextMatches, currentMatches, manifest, location, mode) {\n  let isNew = (match, index) => {\n    if (!currentMatches[index]) return true;\n    return match.route.id !== currentMatches[index].route.id;\n  };\n  let matchPathChanged = (match, index) => {\n    return (\n      // param change, /users/123 -> /users/456\n      currentMatches[index].pathname !== match.pathname || // splat param changed, which is not present in match.path\n      // e.g. /files/images/avatar.jpg -> files/finances.xls\n      currentMatches[index].route.path?.endsWith(\"*\") && currentMatches[index].params[\"*\"] !== match.params[\"*\"]\n    );\n  };\n  if (mode === \"assets\") {\n    return nextMatches.filter(\n      (match, index) => isNew(match, index) || matchPathChanged(match, index)\n    );\n  }\n  if (mode === \"data\") {\n    return nextMatches.filter((match, index) => {\n      let manifestRoute = manifest.routes[match.route.id];\n      if (!manifestRoute || !manifestRoute.hasLoader) {\n        return false;\n      }\n      if (isNew(match, index) || matchPathChanged(match, index)) {\n        return true;\n      }\n      if (match.route.shouldRevalidate) {\n        let routeChoice = match.route.shouldRevalidate({\n          currentUrl: new URL(\n            location.pathname + location.search + location.hash,\n            window.origin\n          ),\n          currentParams: currentMatches[0]?.params || {},\n          nextUrl: new URL(page, window.origin),\n          nextParams: match.params,\n          defaultShouldRevalidate: true\n        });\n        if (typeof routeChoice === \"boolean\") {\n          return routeChoice;\n        }\n      }\n      return true;\n    });\n  }\n  return [];\n}\nfunction getModuleLinkHrefs(matches, manifest, { includeHydrateFallback } = {}) {\n  return dedupeHrefs(\n    matches.map((match) => {\n      let route = manifest.routes[match.route.id];\n      if (!route) return [];\n      let hrefs = [route.module];\n      if (route.clientActionModule) {\n        hrefs = hrefs.concat(route.clientActionModule);\n      }\n      if (route.clientLoaderModule) {\n        hrefs = hrefs.concat(route.clientLoaderModule);\n      }\n      if (includeHydrateFallback && route.hydrateFallbackModule) {\n        hrefs = hrefs.concat(route.hydrateFallbackModule);\n      }\n      if (route.imports) {\n        hrefs = hrefs.concat(route.imports);\n      }\n      return hrefs;\n    }).flat(1)\n  );\n}\nfunction dedupeHrefs(hrefs) {\n  return [...new Set(hrefs)];\n}\nfunction sortKeys(obj) {\n  let sorted = {};\n  let keys = Object.keys(obj).sort();\n  for (let key of keys) {\n    sorted[key] = obj[key];\n  }\n  return sorted;\n}\nfunction dedupeLinkDescriptors(descriptors, preloads) {\n  let set = /* @__PURE__ */ new Set();\n  let preloadsSet = new Set(preloads);\n  return descriptors.reduce((deduped, descriptor) => {\n    let alreadyModulePreload = preloads && !isPageLinkDescriptor(descriptor) && descriptor.as === \"script\" && descriptor.href && preloadsSet.has(descriptor.href);\n    if (alreadyModulePreload) {\n      return deduped;\n    }\n    let key = JSON.stringify(sortKeys(descriptor));\n    if (!set.has(key)) {\n      set.add(key);\n      deduped.push({ key, link: descriptor });\n    }\n    return deduped;\n  }, []);\n}\nvar _isPreloadSupported;\nfunction isPreloadSupported() {\n  if (_isPreloadSupported !== void 0) {\n    return _isPreloadSupported;\n  }\n  let el = document.createElement(\"link\");\n  _isPreloadSupported = el.relList.supports(\"preload\");\n  el = null;\n  return _isPreloadSupported;\n}\n\n// lib/dom/ssr/markup.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\nfunction createHtml(html) {\n  return { __html: html };\n}\n\n// lib/dom/ssr/single-fetch.tsx\nimport * as React4 from \"react\";\n\n// vendor/turbo-stream-v2/utils.ts\nvar HOLE = -1;\nvar NAN = -2;\nvar NEGATIVE_INFINITY = -3;\nvar NEGATIVE_ZERO = -4;\nvar NULL = -5;\nvar POSITIVE_INFINITY = -6;\nvar UNDEFINED = -7;\nvar TYPE_BIGINT = \"B\";\nvar TYPE_DATE = \"D\";\nvar TYPE_ERROR = \"E\";\nvar TYPE_MAP = \"M\";\nvar TYPE_NULL_OBJECT = \"N\";\nvar TYPE_PROMISE = \"P\";\nvar TYPE_REGEXP = \"R\";\nvar TYPE_SET = \"S\";\nvar TYPE_SYMBOL = \"Y\";\nvar TYPE_URL = \"U\";\nvar TYPE_PREVIOUS_RESOLVED = \"Z\";\nvar Deferred2 = class {\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n};\nfunction createLineSplittingTransform() {\n  const decoder = new TextDecoder();\n  let leftover = \"\";\n  return new TransformStream({\n    transform(chunk, controller) {\n      const str = decoder.decode(chunk, { stream: true });\n      const parts = (leftover + str).split(\"\\n\");\n      leftover = parts.pop() || \"\";\n      for (const part of parts) {\n        controller.enqueue(part);\n      }\n    },\n    flush(controller) {\n      if (leftover) {\n        controller.enqueue(leftover);\n      }\n    }\n  });\n}\n\n// vendor/turbo-stream-v2/flatten.ts\nfunction flatten(input) {\n  const { indices } = this;\n  const existing = indices.get(input);\n  if (existing) return [existing];\n  if (input === void 0) return UNDEFINED;\n  if (input === null) return NULL;\n  if (Number.isNaN(input)) return NAN;\n  if (input === Number.POSITIVE_INFINITY) return POSITIVE_INFINITY;\n  if (input === Number.NEGATIVE_INFINITY) return NEGATIVE_INFINITY;\n  if (input === 0 && 1 / input < 0) return NEGATIVE_ZERO;\n  const index = this.index++;\n  indices.set(input, index);\n  stringify.call(this, input, index);\n  return index;\n}\nfunction stringify(input, index) {\n  const { deferred, plugins, postPlugins } = this;\n  const str = this.stringified;\n  const stack = [[input, index]];\n  while (stack.length > 0) {\n    const [input2, index2] = stack.pop();\n    const partsForObj = (obj) => Object.keys(obj).map((k) => `\"_${flatten.call(this, k)}\":${flatten.call(this, obj[k])}`).join(\",\");\n    let error = null;\n    switch (typeof input2) {\n      case \"boolean\":\n      case \"number\":\n      case \"string\":\n        str[index2] = JSON.stringify(input2);\n        break;\n      case \"bigint\":\n        str[index2] = `[\"${TYPE_BIGINT}\",\"${input2}\"]`;\n        break;\n      case \"symbol\": {\n        const keyFor = Symbol.keyFor(input2);\n        if (!keyFor) {\n          error = new Error(\n            \"Cannot encode symbol unless created with Symbol.for()\"\n          );\n        } else {\n          str[index2] = `[\"${TYPE_SYMBOL}\",${JSON.stringify(keyFor)}]`;\n        }\n        break;\n      }\n      case \"object\": {\n        if (!input2) {\n          str[index2] = `${NULL}`;\n          break;\n        }\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          let result = isArray ? \"[\" : \"{\";\n          if (isArray) {\n            for (let i = 0; i < input2.length; i++)\n              result += (i ? \",\" : \"\") + (i in input2 ? flatten.call(this, input2[i]) : HOLE);\n            str[index2] = `${result}]`;\n          } else if (input2 instanceof Date) {\n            str[index2] = `[\"${TYPE_DATE}\",${input2.getTime()}]`;\n          } else if (input2 instanceof URL) {\n            str[index2] = `[\"${TYPE_URL}\",${JSON.stringify(input2.href)}]`;\n          } else if (input2 instanceof RegExp) {\n            str[index2] = `[\"${TYPE_REGEXP}\",${JSON.stringify(\n              input2.source\n            )},${JSON.stringify(input2.flags)}]`;\n          } else if (input2 instanceof Set) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_SET}\",${[...input2].map((val) => flatten.call(this, val)).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_SET}\"]`;\n            }\n          } else if (input2 instanceof Map) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_MAP}\",${[...input2].flatMap(([k, v]) => [\n                flatten.call(this, k),\n                flatten.call(this, v)\n              ]).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_MAP}\"]`;\n            }\n          } else if (input2 instanceof Promise) {\n            str[index2] = `[\"${TYPE_PROMISE}\",${index2}]`;\n            deferred[index2] = input2;\n          } else if (input2 instanceof Error) {\n            str[index2] = `[\"${TYPE_ERROR}\",${JSON.stringify(input2.message)}`;\n            if (input2.name !== \"Error\") {\n              str[index2] += `,${JSON.stringify(input2.name)}`;\n            }\n            str[index2] += \"]\";\n          } else if (Object.getPrototypeOf(input2) === null) {\n            str[index2] = `[\"${TYPE_NULL_OBJECT}\",{${partsForObj(input2)}}]`;\n          } else if (isPlainObject(input2)) {\n            str[index2] = `{${partsForObj(input2)}}`;\n          } else {\n            error = new Error(\"Cannot encode object with prototype\");\n          }\n        }\n        break;\n      }\n      default: {\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          error = new Error(\"Cannot encode function or unexpected type\");\n        }\n      }\n    }\n    if (error) {\n      let pluginHandled = false;\n      if (postPlugins) {\n        for (const plugin of postPlugins) {\n          const pluginResult = plugin(input2);\n          if (Array.isArray(pluginResult)) {\n            pluginHandled = true;\n            const [pluginIdentifier, ...rest] = pluginResult;\n            str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n            if (rest.length > 0) {\n              str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n            }\n            str[index2] += \"]\";\n            break;\n          }\n        }\n      }\n      if (!pluginHandled) {\n        throw error;\n      }\n    }\n  }\n}\nvar objectProtoNames = Object.getOwnPropertyNames(Object.prototype).sort().join(\"\\0\");\nfunction isPlainObject(thing) {\n  const proto = Object.getPrototypeOf(thing);\n  return proto === Object.prototype || proto === null || Object.getOwnPropertyNames(proto).sort().join(\"\\0\") === objectProtoNames;\n}\n\n// vendor/turbo-stream-v2/unflatten.ts\nvar globalObj = typeof window !== \"undefined\" ? window : typeof globalThis !== \"undefined\" ? globalThis : void 0;\nfunction unflatten(parsed) {\n  const { hydrated, values } = this;\n  if (typeof parsed === \"number\") return hydrate.call(this, parsed);\n  if (!Array.isArray(parsed) || !parsed.length) throw new SyntaxError();\n  const startIndex = values.length;\n  for (const value of parsed) {\n    values.push(value);\n  }\n  hydrated.length = values.length;\n  return hydrate.call(this, startIndex);\n}\nfunction hydrate(index) {\n  const { hydrated, values, deferred, plugins } = this;\n  let result;\n  const stack = [\n    [\n      index,\n      (v) => {\n        result = v;\n      }\n    ]\n  ];\n  let postRun = [];\n  while (stack.length > 0) {\n    const [index2, set] = stack.pop();\n    switch (index2) {\n      case UNDEFINED:\n        set(void 0);\n        continue;\n      case NULL:\n        set(null);\n        continue;\n      case NAN:\n        set(NaN);\n        continue;\n      case POSITIVE_INFINITY:\n        set(Infinity);\n        continue;\n      case NEGATIVE_INFINITY:\n        set(-Infinity);\n        continue;\n      case NEGATIVE_ZERO:\n        set(-0);\n        continue;\n    }\n    if (hydrated[index2]) {\n      set(hydrated[index2]);\n      continue;\n    }\n    const value = values[index2];\n    if (!value || typeof value !== \"object\") {\n      hydrated[index2] = value;\n      set(value);\n      continue;\n    }\n    if (Array.isArray(value)) {\n      if (typeof value[0] === \"string\") {\n        const [type, b, c] = value;\n        switch (type) {\n          case TYPE_DATE:\n            set(hydrated[index2] = new Date(b));\n            continue;\n          case TYPE_URL:\n            set(hydrated[index2] = new URL(b));\n            continue;\n          case TYPE_BIGINT:\n            set(hydrated[index2] = BigInt(b));\n            continue;\n          case TYPE_REGEXP:\n            set(hydrated[index2] = new RegExp(b, c));\n            continue;\n          case TYPE_SYMBOL:\n            set(hydrated[index2] = Symbol.for(b));\n            continue;\n          case TYPE_SET:\n            const newSet = /* @__PURE__ */ new Set();\n            hydrated[index2] = newSet;\n            for (let i = value.length - 1; i > 0; i--)\n              stack.push([\n                value[i],\n                (v) => {\n                  newSet.add(v);\n                }\n              ]);\n            set(newSet);\n            continue;\n          case TYPE_MAP:\n            const map = /* @__PURE__ */ new Map();\n            hydrated[index2] = map;\n            for (let i = value.length - 2; i > 0; i -= 2) {\n              const r = [];\n              stack.push([\n                value[i + 1],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                value[i],\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                map.set(r[0], r[1]);\n              });\n            }\n            set(map);\n            continue;\n          case TYPE_NULL_OBJECT:\n            const obj = /* @__PURE__ */ Object.create(null);\n            hydrated[index2] = obj;\n            for (const key of Object.keys(b).reverse()) {\n              const r = [];\n              stack.push([\n                b[key],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                Number(key.slice(1)),\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                obj[r[0]] = r[1];\n              });\n            }\n            set(obj);\n            continue;\n          case TYPE_PROMISE:\n            if (hydrated[b]) {\n              set(hydrated[index2] = hydrated[b]);\n            } else {\n              const d = new Deferred2();\n              deferred[b] = d;\n              set(hydrated[index2] = d.promise);\n            }\n            continue;\n          case TYPE_ERROR:\n            const [, message, errorType] = value;\n            let error = errorType && globalObj && globalObj[errorType] ? new globalObj[errorType](message) : new Error(message);\n            hydrated[index2] = error;\n            set(error);\n            continue;\n          case TYPE_PREVIOUS_RESOLVED:\n            set(hydrated[index2] = hydrated[b]);\n            continue;\n          default:\n            if (Array.isArray(plugins)) {\n              const r = [];\n              const vals = value.slice(1);\n              for (let i = 0; i < vals.length; i++) {\n                const v = vals[i];\n                stack.push([\n                  v,\n                  (v2) => {\n                    r[i] = v2;\n                  }\n                ]);\n              }\n              postRun.push(() => {\n                for (const plugin of plugins) {\n                  const result2 = plugin(value[0], ...r);\n                  if (result2) {\n                    set(hydrated[index2] = result2.value);\n                    return;\n                  }\n                }\n                throw new SyntaxError();\n              });\n              continue;\n            }\n            throw new SyntaxError();\n        }\n      } else {\n        const array = [];\n        hydrated[index2] = array;\n        for (let i = 0; i < value.length; i++) {\n          const n = value[i];\n          if (n !== HOLE) {\n            stack.push([\n              n,\n              (v) => {\n                array[i] = v;\n              }\n            ]);\n          }\n        }\n        set(array);\n        continue;\n      }\n    } else {\n      const object = {};\n      hydrated[index2] = object;\n      for (const key of Object.keys(value).reverse()) {\n        const r = [];\n        stack.push([\n          value[key],\n          (v) => {\n            r[1] = v;\n          }\n        ]);\n        stack.push([\n          Number(key.slice(1)),\n          (k) => {\n            r[0] = k;\n          }\n        ]);\n        postRun.push(() => {\n          object[r[0]] = r[1];\n        });\n      }\n      set(object);\n      continue;\n    }\n  }\n  while (postRun.length > 0) {\n    postRun.pop()();\n  }\n  return result;\n}\n\n// vendor/turbo-stream-v2/turbo-stream.ts\nasync function decode(readable, options) {\n  const { plugins } = options ?? {};\n  const done = new Deferred2();\n  const reader = readable.pipeThrough(createLineSplittingTransform()).getReader();\n  const decoder = {\n    values: [],\n    hydrated: [],\n    deferred: {},\n    plugins\n  };\n  const decoded = await decodeInitial.call(decoder, reader);\n  let donePromise = done.promise;\n  if (decoded.done) {\n    done.resolve();\n  } else {\n    donePromise = decodeDeferred.call(decoder, reader).then(done.resolve).catch((reason) => {\n      for (const deferred of Object.values(decoder.deferred)) {\n        deferred.reject(reason);\n      }\n      done.reject(reason);\n    });\n  }\n  return {\n    done: donePromise.then(() => reader.closed),\n    value: decoded.value\n  };\n}\nasync function decodeInitial(reader) {\n  const read = await reader.read();\n  if (!read.value) {\n    throw new SyntaxError();\n  }\n  let line;\n  try {\n    line = JSON.parse(read.value);\n  } catch (reason) {\n    throw new SyntaxError();\n  }\n  return {\n    done: read.done,\n    value: unflatten.call(this, line)\n  };\n}\nasync function decodeDeferred(reader) {\n  let read = await reader.read();\n  while (!read.done) {\n    if (!read.value) continue;\n    const line = read.value;\n    switch (line[0]) {\n      case TYPE_PROMISE: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.resolve(value);\n        break;\n      }\n      case TYPE_ERROR: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.reject(value);\n        break;\n      }\n      default:\n        throw new SyntaxError();\n    }\n    read = await reader.read();\n  }\n}\nfunction encode(input, options) {\n  const { plugins, postPlugins, signal } = options ?? {};\n  const encoder2 = {\n    deferred: {},\n    index: 0,\n    indices: /* @__PURE__ */ new Map(),\n    stringified: [],\n    plugins,\n    postPlugins,\n    signal\n  };\n  const textEncoder = new TextEncoder();\n  let lastSentIndex = 0;\n  const readable = new ReadableStream({\n    async start(controller) {\n      const id = flatten.call(encoder2, input);\n      if (Array.isArray(id)) {\n        throw new Error(\"This should never happen\");\n      }\n      if (id < 0) {\n        controller.enqueue(textEncoder.encode(`${id}\n`));\n      } else {\n        controller.enqueue(\n          textEncoder.encode(`[${encoder2.stringified.join(\",\")}]\n`)\n        );\n        lastSentIndex = encoder2.stringified.length - 1;\n      }\n      const seenPromises = /* @__PURE__ */ new WeakSet();\n      if (Object.keys(encoder2.deferred).length) {\n        let raceDone;\n        const racePromise = new Promise((resolve, reject) => {\n          raceDone = resolve;\n          if (signal) {\n            const rejectPromise = () => reject(signal.reason || new Error(\"Signal was aborted.\"));\n            if (signal.aborted) {\n              rejectPromise();\n            } else {\n              signal.addEventListener(\"abort\", (event) => {\n                rejectPromise();\n              });\n            }\n          }\n        });\n        while (Object.keys(encoder2.deferred).length > 0) {\n          for (const [deferredId, deferred] of Object.entries(\n            encoder2.deferred\n          )) {\n            if (seenPromises.has(deferred)) continue;\n            seenPromises.add(\n              // biome-ignore lint/suspicious/noAssignInExpressions: <explanation>\n              encoder2.deferred[Number(deferredId)] = Promise.race([\n                racePromise,\n                deferred\n              ]).then(\n                (resolved) => {\n                  const id2 = flatten.call(encoder2, resolved);\n                  if (Array.isArray(id2)) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                      )\n                    );\n                    encoder2.index++;\n                    lastSentIndex++;\n                  } else if (id2 < 0) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:${id2}\n`\n                      )\n                    );\n                  } else {\n                    const values = encoder2.stringified.slice(lastSentIndex + 1).join(\",\");\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_PROMISE}${deferredId}:[${values}]\n`\n                      )\n                    );\n                    lastSentIndex = encoder2.stringified.length - 1;\n                  }\n                },\n                (reason) => {\n                  if (!reason || typeof reason !== \"object\" || !(reason instanceof Error)) {\n                    reason = new Error(\"An unknown error occurred\");\n                  }\n                  const id2 = flatten.call(encoder2, reason);\n                  if (Array.isArray(id2)) {\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_ERROR}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                      )\n                    );\n                    encoder2.index++;\n                    lastSentIndex++;\n                  } else if (id2 < 0) {\n                    controller.enqueue(\n                      textEncoder.encode(`${TYPE_ERROR}${deferredId}:${id2}\n`)\n                    );\n                  } else {\n                    const values = encoder2.stringified.slice(lastSentIndex + 1).join(\",\");\n                    controller.enqueue(\n                      textEncoder.encode(\n                        `${TYPE_ERROR}${deferredId}:[${values}]\n`\n                      )\n                    );\n                    lastSentIndex = encoder2.stringified.length - 1;\n                  }\n                }\n              ).finally(() => {\n                delete encoder2.deferred[Number(deferredId)];\n              })\n            );\n          }\n          await Promise.race(Object.values(encoder2.deferred));\n        }\n        raceDone();\n      }\n      await Promise.all(Object.values(encoder2.deferred));\n      controller.close();\n    }\n  });\n  return readable;\n}\n\n// lib/dom/ssr/data.ts\nasync function createRequestInit(request) {\n  let init = { signal: request.signal };\n  if (request.method !== \"GET\") {\n    init.method = request.method;\n    let contentType = request.headers.get(\"Content-Type\");\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      init.headers = { \"Content-Type\": contentType };\n      init.body = JSON.stringify(await request.json());\n    } else if (contentType && /\\btext\\/plain\\b/.test(contentType)) {\n      init.headers = { \"Content-Type\": contentType };\n      init.body = await request.text();\n    } else if (contentType && /\\bapplication\\/x-www-form-urlencoded\\b/.test(contentType)) {\n      init.body = new URLSearchParams(await request.text());\n    } else {\n      init.body = await request.formData();\n    }\n  }\n  return init;\n}\n\n// lib/dom/ssr/single-fetch.tsx\nvar SingleFetchRedirectSymbol = Symbol(\"SingleFetchRedirect\");\nvar SingleFetchNoResultError = class extends Error {\n};\nvar SINGLE_FETCH_REDIRECT_STATUS = 202;\nvar NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([100, 101, 204, 205]);\nfunction StreamTransfer({\n  context,\n  identifier,\n  reader,\n  textDecoder,\n  nonce\n}) {\n  if (!context.renderMeta || !context.renderMeta.didRenderScripts) {\n    return null;\n  }\n  if (!context.renderMeta.streamCache) {\n    context.renderMeta.streamCache = {};\n  }\n  let { streamCache } = context.renderMeta;\n  let promise = streamCache[identifier];\n  if (!promise) {\n    promise = streamCache[identifier] = reader.read().then((result) => {\n      streamCache[identifier].result = {\n        done: result.done,\n        value: textDecoder.decode(result.value, { stream: true })\n      };\n    }).catch((e) => {\n      streamCache[identifier].error = e;\n    });\n  }\n  if (promise.error) {\n    throw promise.error;\n  }\n  if (promise.result === void 0) {\n    throw promise;\n  }\n  let { done, value } = promise.result;\n  let scriptTag = value ? /* @__PURE__ */ React4.createElement(\n    \"script\",\n    {\n      nonce,\n      dangerouslySetInnerHTML: {\n        __html: `window.__reactRouterContext.streamController.enqueue(${escapeHtml(\n          JSON.stringify(value)\n        )});`\n      }\n    }\n  ) : null;\n  if (done) {\n    return /* @__PURE__ */ React4.createElement(React4.Fragment, null, scriptTag, /* @__PURE__ */ React4.createElement(\n      \"script\",\n      {\n        nonce,\n        dangerouslySetInnerHTML: {\n          __html: `window.__reactRouterContext.streamController.close();`\n        }\n      }\n    ));\n  } else {\n    return /* @__PURE__ */ React4.createElement(React4.Fragment, null, scriptTag, /* @__PURE__ */ React4.createElement(React4.Suspense, null, /* @__PURE__ */ React4.createElement(\n      StreamTransfer,\n      {\n        context,\n        identifier: identifier + 1,\n        reader,\n        textDecoder,\n        nonce\n      }\n    )));\n  }\n}\nfunction getTurboStreamSingleFetchDataStrategy(getRouter, manifest, routeModules, ssr, basename) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let manifestRoute = manifest.routes[match.route.id];\n      invariant2(manifestRoute, \"Route not found in manifest\");\n      let routeModule = routeModules[match.route.id];\n      return {\n        hasLoader: manifestRoute.hasLoader,\n        hasClientLoader: manifestRoute.hasClientLoader,\n        hasShouldRevalidate: Boolean(routeModule?.shouldRevalidate)\n      };\n    },\n    fetchAndDecodeViaTurboStream,\n    ssr,\n    basename\n  );\n  return async (args) => args.unstable_runClientMiddleware(dataStrategy);\n}\nfunction getSingleFetchDataStrategyImpl(getRouter, getRouteInfo, fetchAndDecode, ssr, basename) {\n  return async (args) => {\n    let { request, matches, fetcherKey } = args;\n    let router = getRouter();\n    if (request.method !== \"GET\") {\n      return singleFetchActionStrategy(args, fetchAndDecode, basename);\n    }\n    let foundRevalidatingServerLoader = matches.some((m) => {\n      let { hasLoader, hasClientLoader } = getRouteInfo(m);\n      return m.unstable_shouldCallHandler() && hasLoader && !hasClientLoader;\n    });\n    if (!ssr && !foundRevalidatingServerLoader) {\n      return nonSsrStrategy(args, getRouteInfo, fetchAndDecode, basename);\n    }\n    if (fetcherKey) {\n      return singleFetchLoaderFetcherStrategy(args, fetchAndDecode, basename);\n    }\n    return singleFetchLoaderNavigationStrategy(\n      args,\n      router,\n      getRouteInfo,\n      fetchAndDecode,\n      ssr,\n      basename\n    );\n  };\n}\nasync function singleFetchActionStrategy(args, fetchAndDecode, basename) {\n  let actionMatch = args.matches.find((m) => m.unstable_shouldCallHandler());\n  invariant2(actionMatch, \"No action match found\");\n  let actionStatus = void 0;\n  let result = await actionMatch.resolve(async (handler) => {\n    let result2 = await handler(async () => {\n      let { data: data2, status } = await fetchAndDecode(args, basename, [\n        actionMatch.route.id\n      ]);\n      actionStatus = status;\n      return unwrapSingleFetchResult(data2, actionMatch.route.id);\n    });\n    return result2;\n  });\n  if (isResponse(result.result) || isRouteErrorResponse(result.result) || isDataWithResponseInit(result.result)) {\n    return { [actionMatch.route.id]: result };\n  }\n  return {\n    [actionMatch.route.id]: {\n      type: result.type,\n      result: data(result.result, actionStatus)\n    }\n  };\n}\nasync function nonSsrStrategy(args, getRouteInfo, fetchAndDecode, basename) {\n  let matchesToLoad = args.matches.filter(\n    (m) => m.unstable_shouldCallHandler()\n  );\n  let results = {};\n  await Promise.all(\n    matchesToLoad.map(\n      (m) => m.resolve(async (handler) => {\n        try {\n          let { hasClientLoader } = getRouteInfo(m);\n          let routeId = m.route.id;\n          let result = hasClientLoader ? await handler(async () => {\n            let { data: data2 } = await fetchAndDecode(args, basename, [routeId]);\n            return unwrapSingleFetchResult(data2, routeId);\n          }) : await handler();\n          results[m.route.id] = { type: \"data\", result };\n        } catch (e) {\n          results[m.route.id] = { type: \"error\", result: e };\n        }\n      })\n    )\n  );\n  return results;\n}\nasync function singleFetchLoaderNavigationStrategy(args, router, getRouteInfo, fetchAndDecode, ssr, basename) {\n  let routesParams = /* @__PURE__ */ new Set();\n  let foundOptOutRoute = false;\n  let routeDfds = args.matches.map(() => createDeferred2());\n  let singleFetchDfd = createDeferred2();\n  let results = {};\n  let resolvePromise = Promise.all(\n    args.matches.map(\n      async (m, i) => m.resolve(async (handler) => {\n        routeDfds[i].resolve();\n        let routeId = m.route.id;\n        let { hasLoader, hasClientLoader, hasShouldRevalidate } = getRouteInfo(m);\n        let defaultShouldRevalidate = !m.unstable_shouldRevalidateArgs || m.unstable_shouldRevalidateArgs.actionStatus == null || m.unstable_shouldRevalidateArgs.actionStatus < 400;\n        let shouldCall = m.unstable_shouldCallHandler(defaultShouldRevalidate);\n        if (!shouldCall) {\n          foundOptOutRoute || (foundOptOutRoute = m.unstable_shouldRevalidateArgs != null && // This is a revalidation,\n          hasLoader && // for a route with a server loader,\n          hasShouldRevalidate === true);\n          return;\n        }\n        if (hasClientLoader) {\n          if (hasLoader) {\n            foundOptOutRoute = true;\n          }\n          try {\n            let result = await handler(async () => {\n              let { data: data2 } = await fetchAndDecode(args, basename, [routeId]);\n              return unwrapSingleFetchResult(data2, routeId);\n            });\n            results[routeId] = { type: \"data\", result };\n          } catch (e) {\n            results[routeId] = { type: \"error\", result: e };\n          }\n          return;\n        }\n        if (hasLoader) {\n          routesParams.add(routeId);\n        }\n        try {\n          let result = await handler(async () => {\n            let data2 = await singleFetchDfd.promise;\n            return unwrapSingleFetchResult(data2, routeId);\n          });\n          results[routeId] = { type: \"data\", result };\n        } catch (e) {\n          results[routeId] = { type: \"error\", result: e };\n        }\n      })\n    )\n  );\n  await Promise.all(routeDfds.map((d) => d.promise));\n  let isInitialLoad = !router.state.initialized && router.state.navigation.state === \"idle\";\n  if ((isInitialLoad || routesParams.size === 0) && !window.__reactRouterHdrActive) {\n    singleFetchDfd.resolve({ routes: {} });\n  } else {\n    let targetRoutes = ssr && foundOptOutRoute && routesParams.size > 0 ? [...routesParams.keys()] : void 0;\n    try {\n      let data2 = await fetchAndDecode(args, basename, targetRoutes);\n      singleFetchDfd.resolve(data2.data);\n    } catch (e) {\n      singleFetchDfd.reject(e);\n    }\n  }\n  await resolvePromise;\n  await bubbleMiddlewareErrors(\n    singleFetchDfd.promise,\n    args.matches,\n    routesParams,\n    results\n  );\n  return results;\n}\nasync function bubbleMiddlewareErrors(singleFetchPromise, matches, routesParams, results) {\n  try {\n    let middlewareError;\n    let fetchedData = await singleFetchPromise;\n    if (\"routes\" in fetchedData) {\n      for (let match of matches) {\n        if (match.route.id in fetchedData.routes) {\n          let routeResult = fetchedData.routes[match.route.id];\n          if (\"error\" in routeResult) {\n            middlewareError = routeResult.error;\n            break;\n          }\n        }\n      }\n    }\n    if (middlewareError !== void 0) {\n      Array.from(routesParams.values()).forEach((routeId) => {\n        if (results[routeId].result instanceof SingleFetchNoResultError) {\n          results[routeId].result = middlewareError;\n        }\n      });\n    }\n  } catch (e) {\n  }\n}\nasync function singleFetchLoaderFetcherStrategy(args, fetchAndDecode, basename) {\n  let fetcherMatch = args.matches.find((m) => m.unstable_shouldCallHandler());\n  invariant2(fetcherMatch, \"No fetcher match found\");\n  let routeId = fetcherMatch.route.id;\n  let result = await fetcherMatch.resolve(\n    async (handler) => handler(async () => {\n      let { data: data2 } = await fetchAndDecode(args, basename, [routeId]);\n      return unwrapSingleFetchResult(data2, routeId);\n    })\n  );\n  return { [fetcherMatch.route.id]: result };\n}\nfunction stripIndexParam(url) {\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  return url;\n}\nfunction singleFetchUrl(reqUrl, basename) {\n  let url = typeof reqUrl === \"string\" ? new URL(\n    reqUrl,\n    // This can be called during the SSR flow via PrefetchPageLinksImpl so\n    // don't assume window is available\n    typeof window === \"undefined\" ? \"server://singlefetch/\" : window.location.origin\n  ) : reqUrl;\n  if (url.pathname === \"/\") {\n    url.pathname = \"_root.data\";\n  } else if (basename && stripBasename(url.pathname, basename) === \"/\") {\n    url.pathname = `${basename.replace(/\\/$/, \"\")}/_root.data`;\n  } else {\n    url.pathname = `${url.pathname.replace(/\\/$/, \"\")}.data`;\n  }\n  return url;\n}\nasync function fetchAndDecodeViaTurboStream(args, basename, targetRoutes) {\n  let { request } = args;\n  let url = singleFetchUrl(request.url, basename);\n  if (request.method === \"GET\") {\n    url = stripIndexParam(url);\n    if (targetRoutes) {\n      url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n    }\n  }\n  let res = await fetch(url, await createRequestInit(request));\n  if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n    throw new ErrorResponseImpl(404, \"Not Found\", true);\n  }\n  if (res.status === 204 && res.headers.has(\"X-Remix-Redirect\")) {\n    return {\n      status: SINGLE_FETCH_REDIRECT_STATUS,\n      data: {\n        redirect: {\n          redirect: res.headers.get(\"X-Remix-Redirect\"),\n          status: Number(res.headers.get(\"X-Remix-Status\") || \"302\"),\n          revalidate: res.headers.get(\"X-Remix-Revalidate\") === \"true\",\n          reload: res.headers.get(\"X-Remix-Reload-Document\") === \"true\",\n          replace: res.headers.get(\"X-Remix-Replace\") === \"true\"\n        }\n      }\n    };\n  }\n  if (NO_BODY_STATUS_CODES.has(res.status)) {\n    let routes = {};\n    if (targetRoutes && request.method !== \"GET\") {\n      routes[targetRoutes[0]] = { data: void 0 };\n    }\n    return {\n      status: res.status,\n      data: { routes }\n    };\n  }\n  invariant2(res.body, \"No response body to decode\");\n  try {\n    let decoded = await decodeViaTurboStream(res.body, window);\n    let data2;\n    if (request.method === \"GET\") {\n      let typed = decoded.value;\n      if (SingleFetchRedirectSymbol in typed) {\n        data2 = { redirect: typed[SingleFetchRedirectSymbol] };\n      } else {\n        data2 = { routes: typed };\n      }\n    } else {\n      let typed = decoded.value;\n      let routeId = targetRoutes?.[0];\n      invariant2(routeId, \"No routeId found for single fetch call decoding\");\n      if (\"redirect\" in typed) {\n        data2 = { redirect: typed };\n      } else {\n        data2 = { routes: { [routeId]: typed } };\n      }\n    }\n    return { status: res.status, data: data2 };\n  } catch (e) {\n    throw new Error(\"Unable to decode turbo-stream response\");\n  }\n}\nfunction decodeViaTurboStream(body, global2) {\n  return decode(body, {\n    plugins: [\n      (type, ...rest) => {\n        if (type === \"SanitizedError\") {\n          let [name, message, stack] = rest;\n          let Constructor = Error;\n          if (name && name in global2 && typeof global2[name] === \"function\") {\n            Constructor = global2[name];\n          }\n          let error = new Constructor(message);\n          error.stack = stack;\n          return { value: error };\n        }\n        if (type === \"ErrorResponse\") {\n          let [data2, status, statusText] = rest;\n          return {\n            value: new ErrorResponseImpl(status, statusText, data2)\n          };\n        }\n        if (type === \"SingleFetchRedirect\") {\n          return { value: { [SingleFetchRedirectSymbol]: rest[0] } };\n        }\n        if (type === \"SingleFetchClassInstance\") {\n          return { value: rest[0] };\n        }\n        if (type === \"SingleFetchFallback\") {\n          return { value: void 0 };\n        }\n      }\n    ]\n  });\n}\nfunction unwrapSingleFetchResult(result, routeId) {\n  if (\"redirect\" in result) {\n    let {\n      redirect: location,\n      revalidate,\n      reload,\n      replace: replace2,\n      status\n    } = result.redirect;\n    throw redirect(location, {\n      status,\n      headers: {\n        // Three R's of redirecting (lol Veep)\n        ...revalidate ? { \"X-Remix-Revalidate\": \"yes\" } : null,\n        ...reload ? { \"X-Remix-Reload-Document\": \"yes\" } : null,\n        ...replace2 ? { \"X-Remix-Replace\": \"yes\" } : null\n      }\n    });\n  }\n  let routeResult = result.routes[routeId];\n  if (routeResult == null) {\n    throw new SingleFetchNoResultError(\n      `No result found for routeId \"${routeId}\"`\n    );\n  } else if (\"error\" in routeResult) {\n    throw routeResult.error;\n  } else if (\"data\" in routeResult) {\n    return routeResult.data;\n  } else {\n    throw new Error(`Invalid response found for routeId \"${routeId}\"`);\n  }\n}\nfunction createDeferred2() {\n  let resolve;\n  let reject;\n  let promise = new Promise((res, rej) => {\n    resolve = async (val) => {\n      res(val);\n      try {\n        await promise;\n      } catch (e) {\n      }\n    };\n    reject = async (error) => {\n      rej(error);\n      try {\n        await promise;\n      } catch (e) {\n      }\n    };\n  });\n  return {\n    promise,\n    //@ts-ignore\n    resolve,\n    //@ts-ignore\n    reject\n  };\n}\n\n// lib/dom/ssr/fog-of-war.ts\nimport * as React8 from \"react\";\n\n// lib/dom/ssr/routes.tsx\nimport * as React7 from \"react\";\n\n// lib/dom/ssr/errorBoundaries.tsx\nimport * as React5 from \"react\";\nvar RemixErrorBoundary = class extends React5.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: props.error || null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: props.error || null, location: props.location };\n    }\n    return { error: props.error || state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React5.createElement(\n        RemixRootDefaultErrorBoundary,\n        {\n          error: this.state.error,\n          isOutsideRemixApp: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction RemixRootDefaultErrorBoundary({\n  error,\n  isOutsideRemixApp\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React5.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React5.createElement(BoundaryShell, { title: \"Unhandled Thrown Response!\" }, /* @__PURE__ */ React5.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React5.createElement(\n    BoundaryShell,\n    {\n      title: \"Application Error!\",\n      isOutsideRemixApp\n    },\n    /* @__PURE__ */ React5.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"),\n    /* @__PURE__ */ React5.createElement(\n      \"pre\",\n      {\n        style: {\n          padding: \"2rem\",\n          background: \"hsla(10, 50%, 50%, 0.1)\",\n          color: \"red\",\n          overflow: \"auto\"\n        }\n      },\n      errorInstance.stack\n    ),\n    heyDeveloper\n  );\n}\nfunction BoundaryShell({\n  title,\n  renderScripts,\n  isOutsideRemixApp,\n  children\n}) {\n  let { routeModules } = useFrameworkContext();\n  if (routeModules.root?.Layout && !isOutsideRemixApp) {\n    return children;\n  }\n  return /* @__PURE__ */ React5.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React5.createElement(\"head\", null, /* @__PURE__ */ React5.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React5.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React5.createElement(\"title\", null, title)), /* @__PURE__ */ React5.createElement(\"body\", null, /* @__PURE__ */ React5.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children, renderScripts ? /* @__PURE__ */ React5.createElement(Scripts, null) : null)));\n}\n\n// lib/dom/ssr/fallback.tsx\nimport * as React6 from \"react\";\nfunction RemixRootDefaultHydrateFallback() {\n  return /* @__PURE__ */ React6.createElement(BoundaryShell, { title: \"Loading...\", renderScripts: true }, ENABLE_DEV_WARNINGS ? /* @__PURE__ */ React6.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n              console.log(\n                \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this \" +\n                \"when your app is loading JS modules and/or running \\`clientLoader\\` \" +\n                \"functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback \" +\n                \"for more information.\"\n              );\n            `\n      }\n    }\n  ) : null);\n}\n\n// lib/dom/ssr/routes.tsx\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction getRouteComponents(route, routeModule, isSpaMode) {\n  let Component4 = getRouteModuleComponent(routeModule);\n  let HydrateFallback = routeModule.HydrateFallback && (!isSpaMode || route.id === \"root\") ? routeModule.HydrateFallback : route.id === \"root\" ? RemixRootDefaultHydrateFallback : void 0;\n  let ErrorBoundary = routeModule.ErrorBoundary ? routeModule.ErrorBoundary : route.id === \"root\" ? () => /* @__PURE__ */ React7.createElement(RemixRootDefaultErrorBoundary, { error: useRouteError() }) : void 0;\n  if (route.id === \"root\" && routeModule.Layout) {\n    return {\n      ...Component4 ? {\n        element: /* @__PURE__ */ React7.createElement(routeModule.Layout, null, /* @__PURE__ */ React7.createElement(Component4, null))\n      } : { Component: Component4 },\n      ...ErrorBoundary ? {\n        errorElement: /* @__PURE__ */ React7.createElement(routeModule.Layout, null, /* @__PURE__ */ React7.createElement(ErrorBoundary, null))\n      } : { ErrorBoundary },\n      ...HydrateFallback ? {\n        hydrateFallbackElement: /* @__PURE__ */ React7.createElement(routeModule.Layout, null, /* @__PURE__ */ React7.createElement(HydrateFallback, null))\n      } : { HydrateFallback }\n    };\n  }\n  return { Component: Component4, ErrorBoundary, HydrateFallback };\n}\nfunction createServerRoutes(manifest, routeModules, future, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), spaModeLazyPromise = Promise.resolve({ Component: () => null })) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let routeModule = routeModules[route.id];\n    invariant2(\n      routeModule,\n      \"No `routeModule` available to create server routes\"\n    );\n    let dataRoute = {\n      ...getRouteComponents(route, routeModule, isSpaMode),\n      caseSensitive: route.caseSensitive,\n      id: route.id,\n      index: route.index,\n      path: route.path,\n      handle: routeModule.handle,\n      // For SPA Mode, all routes are lazy except root.  However we tell the\n      // router root is also lazy here too since we don't need a full\n      // implementation - we just need a `lazy` prop to tell the RR rendering\n      // where to stop which is always at the root route in SPA mode\n      lazy: isSpaMode ? () => spaModeLazyPromise : void 0,\n      // For partial hydration rendering, we need to indicate when the route\n      // has a loader/clientLoader, but it won't ever be called during the static\n      // render, so just give it a no-op function so we can render down to the\n      // proper fallback\n      loader: route.hasLoader || route.hasClientLoader ? () => null : void 0\n      // We don't need middleware/action/shouldRevalidate on these routes since\n      // they're for a static render\n    };\n    let children = createServerRoutes(\n      manifest,\n      routeModules,\n      future,\n      isSpaMode,\n      route.id,\n      routesByParentId,\n      spaModeLazyPromise\n    );\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction createClientRoutesWithHMRRevalidationOptOut(needsRevalidation, manifest, routeModulesCache, initialState, ssr, isSpaMode) {\n  return createClientRoutes(\n    manifest,\n    routeModulesCache,\n    initialState,\n    ssr,\n    isSpaMode,\n    \"\",\n    groupRoutesByParentId(manifest),\n    needsRevalidation\n  );\n}\nfunction preventInvalidServerHandlerCall(type, route) {\n  if (type === \"loader\" && !route.hasLoader || type === \"action\" && !route.hasAction) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${route.id}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nfunction noActionDefinedError(type, routeId) {\n  let article = type === \"clientAction\" ? \"a\" : \"an\";\n  let msg = `Route \"${routeId}\" does not have ${article} ${type}, but you are trying to submit to it. To fix this, please add ${article} \\`${type}\\` function to the route`;\n  console.error(msg);\n  throw new ErrorResponseImpl(405, \"Method Not Allowed\", new Error(msg), true);\n}\nfunction createClientRoutes(manifest, routeModulesCache, initialState, ssr, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), needsRevalidation) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let routeModule = routeModulesCache[route.id];\n    function fetchServerHandler(singleFetch) {\n      invariant2(\n        typeof singleFetch === \"function\",\n        \"No single fetch function available for route handler\"\n      );\n      return singleFetch();\n    }\n    function fetchServerLoader(singleFetch) {\n      if (!route.hasLoader) return Promise.resolve(null);\n      return fetchServerHandler(singleFetch);\n    }\n    function fetchServerAction(singleFetch) {\n      if (!route.hasAction) {\n        throw noActionDefinedError(\"action\", route.id);\n      }\n      return fetchServerHandler(singleFetch);\n    }\n    function prefetchModule(modulePath) {\n      import(\n        /* @vite-ignore */\n        /* webpackIgnore: true */\n        modulePath\n      );\n    }\n    function prefetchRouteModuleChunks(route2) {\n      if (route2.clientActionModule) {\n        prefetchModule(route2.clientActionModule);\n      }\n      if (route2.clientLoaderModule) {\n        prefetchModule(route2.clientLoaderModule);\n      }\n    }\n    async function prefetchStylesAndCallHandler(handler) {\n      let cachedModule = routeModulesCache[route.id];\n      let linkPrefetchPromise = cachedModule ? prefetchStyleLinks(route, cachedModule) : Promise.resolve();\n      try {\n        return handler();\n      } finally {\n        await linkPrefetchPromise;\n      }\n    }\n    let dataRoute = {\n      id: route.id,\n      index: route.index,\n      path: route.path\n    };\n    if (routeModule) {\n      Object.assign(dataRoute, {\n        ...dataRoute,\n        ...getRouteComponents(route, routeModule, isSpaMode),\n        unstable_middleware: routeModule.unstable_clientMiddleware,\n        handle: routeModule.handle,\n        shouldRevalidate: getShouldRevalidateFunction(\n          dataRoute.path,\n          routeModule,\n          route,\n          ssr,\n          needsRevalidation\n        )\n      });\n      let hasInitialData = initialState && initialState.loaderData && route.id in initialState.loaderData;\n      let initialData = hasInitialData ? initialState?.loaderData?.[route.id] : void 0;\n      let hasInitialError = initialState && initialState.errors && route.id in initialState.errors;\n      let initialError = hasInitialError ? initialState?.errors?.[route.id] : void 0;\n      let isHydrationRequest = needsRevalidation == null && (routeModule.clientLoader?.hydrate === true || !route.hasLoader);\n      dataRoute.loader = async ({ request, params, context }, singleFetch) => {\n        try {\n          let result = await prefetchStylesAndCallHandler(async () => {\n            invariant2(\n              routeModule,\n              \"No `routeModule` available for critical-route loader\"\n            );\n            if (!routeModule.clientLoader) {\n              return fetchServerLoader(singleFetch);\n            }\n            return routeModule.clientLoader({\n              request,\n              params,\n              context,\n              async serverLoader() {\n                preventInvalidServerHandlerCall(\"loader\", route);\n                if (isHydrationRequest) {\n                  if (hasInitialData) {\n                    return initialData;\n                  }\n                  if (hasInitialError) {\n                    throw initialError;\n                  }\n                }\n                return fetchServerLoader(singleFetch);\n              }\n            });\n          });\n          return result;\n        } finally {\n          isHydrationRequest = false;\n        }\n      };\n      dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n        route.id,\n        routeModule.clientLoader,\n        route.hasLoader,\n        isSpaMode\n      );\n      dataRoute.action = ({ request, params, context }, singleFetch) => {\n        return prefetchStylesAndCallHandler(async () => {\n          invariant2(\n            routeModule,\n            \"No `routeModule` available for critical-route action\"\n          );\n          if (!routeModule.clientAction) {\n            if (isSpaMode) {\n              throw noActionDefinedError(\"clientAction\", route.id);\n            }\n            return fetchServerAction(singleFetch);\n          }\n          return routeModule.clientAction({\n            request,\n            params,\n            context,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route);\n              return fetchServerAction(singleFetch);\n            }\n          });\n        });\n      };\n    } else {\n      if (!route.hasClientLoader) {\n        dataRoute.loader = (_, singleFetch) => prefetchStylesAndCallHandler(() => {\n          return fetchServerLoader(singleFetch);\n        });\n      }\n      if (!route.hasClientAction) {\n        dataRoute.action = (_, singleFetch) => prefetchStylesAndCallHandler(() => {\n          if (isSpaMode) {\n            throw noActionDefinedError(\"clientAction\", route.id);\n          }\n          return fetchServerAction(singleFetch);\n        });\n      }\n      let lazyRoutePromise;\n      async function getLazyRoute() {\n        if (lazyRoutePromise) {\n          return await lazyRoutePromise;\n        }\n        lazyRoutePromise = (async () => {\n          if (route.clientLoaderModule || route.clientActionModule) {\n            await new Promise((resolve) => setTimeout(resolve, 0));\n          }\n          let routeModulePromise = loadRouteModuleWithBlockingLinks(\n            route,\n            routeModulesCache\n          );\n          prefetchRouteModuleChunks(route);\n          return await routeModulePromise;\n        })();\n        return await lazyRoutePromise;\n      }\n      dataRoute.lazy = {\n        loader: route.hasClientLoader ? async () => {\n          let { clientLoader } = route.clientLoaderModule ? await import(\n            /* @vite-ignore */\n            /* webpackIgnore: true */\n            route.clientLoaderModule\n          ) : await getLazyRoute();\n          invariant2(clientLoader, \"No `clientLoader` export found\");\n          return (args, singleFetch) => clientLoader({\n            ...args,\n            async serverLoader() {\n              preventInvalidServerHandlerCall(\"loader\", route);\n              return fetchServerLoader(singleFetch);\n            }\n          });\n        } : void 0,\n        action: route.hasClientAction ? async () => {\n          let clientActionPromise = route.clientActionModule ? import(\n            /* @vite-ignore */\n            /* webpackIgnore: true */\n            route.clientActionModule\n          ) : getLazyRoute();\n          prefetchRouteModuleChunks(route);\n          let { clientAction } = await clientActionPromise;\n          invariant2(clientAction, \"No `clientAction` export found\");\n          return (args, singleFetch) => clientAction({\n            ...args,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route);\n              return fetchServerAction(singleFetch);\n            }\n          });\n        } : void 0,\n        unstable_middleware: route.hasClientMiddleware ? async () => {\n          let { unstable_clientMiddleware } = route.clientMiddlewareModule ? await import(\n            /* @vite-ignore */\n            /* webpackIgnore: true */\n            route.clientMiddlewareModule\n          ) : await getLazyRoute();\n          invariant2(\n            unstable_clientMiddleware,\n            \"No `unstable_clientMiddleware` export found\"\n          );\n          return unstable_clientMiddleware;\n        } : void 0,\n        shouldRevalidate: async () => {\n          let lazyRoute = await getLazyRoute();\n          return getShouldRevalidateFunction(\n            dataRoute.path,\n            lazyRoute,\n            route,\n            ssr,\n            needsRevalidation\n          );\n        },\n        handle: async () => (await getLazyRoute()).handle,\n        // No need to wrap these in layout since the root route is never\n        // loaded via route.lazy()\n        Component: async () => (await getLazyRoute()).Component,\n        ErrorBoundary: route.hasErrorBoundary ? async () => (await getLazyRoute()).ErrorBoundary : void 0\n      };\n    }\n    let children = createClientRoutes(\n      manifest,\n      routeModulesCache,\n      initialState,\n      ssr,\n      isSpaMode,\n      route.id,\n      routesByParentId,\n      needsRevalidation\n    );\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction getShouldRevalidateFunction(path, route, manifestRoute, ssr, needsRevalidation) {\n  if (needsRevalidation) {\n    return wrapShouldRevalidateForHdr(\n      manifestRoute.id,\n      route.shouldRevalidate,\n      needsRevalidation\n    );\n  }\n  if (!ssr && manifestRoute.hasLoader && !manifestRoute.hasClientLoader) {\n    let myParams = path ? compilePath(path)[1].map((p) => p.paramName) : [];\n    const didParamsChange = (opts) => myParams.some((p) => opts.currentParams[p] !== opts.nextParams[p]);\n    if (route.shouldRevalidate) {\n      let fn = route.shouldRevalidate;\n      return (opts) => fn({\n        ...opts,\n        defaultShouldRevalidate: didParamsChange(opts)\n      });\n    } else {\n      return (opts) => didParamsChange(opts);\n    }\n  }\n  if (ssr && route.shouldRevalidate) {\n    let fn = route.shouldRevalidate;\n    return (opts) => fn({ ...opts, defaultShouldRevalidate: true });\n  }\n  return route.shouldRevalidate;\n}\nfunction wrapShouldRevalidateForHdr(routeId, routeShouldRevalidate, needsRevalidation) {\n  let handledRevalidation = false;\n  return (arg) => {\n    if (!handledRevalidation) {\n      handledRevalidation = true;\n      return needsRevalidation.has(routeId);\n    }\n    return routeShouldRevalidate ? routeShouldRevalidate(arg) : arg.defaultShouldRevalidate;\n  };\n}\nasync function loadRouteModuleWithBlockingLinks(route, routeModules) {\n  let routeModulePromise = loadRouteModule(route, routeModules);\n  let prefetchRouteCssPromise = prefetchRouteCss(route);\n  let routeModule = await routeModulePromise;\n  await Promise.all([\n    prefetchRouteCssPromise,\n    prefetchStyleLinks(route, routeModule)\n  ]);\n  return {\n    Component: getRouteModuleComponent(routeModule),\n    ErrorBoundary: routeModule.ErrorBoundary,\n    unstable_clientMiddleware: routeModule.unstable_clientMiddleware,\n    clientAction: routeModule.clientAction,\n    clientLoader: routeModule.clientLoader,\n    handle: routeModule.handle,\n    links: routeModule.links,\n    meta: routeModule.meta,\n    shouldRevalidate: routeModule.shouldRevalidate\n  };\n}\nfunction getRouteModuleComponent(routeModule) {\n  if (routeModule.default == null) return void 0;\n  let isEmptyObject = typeof routeModule.default === \"object\" && Object.keys(routeModule.default).length === 0;\n  if (!isEmptyObject) {\n    return routeModule.default;\n  }\n}\nfunction shouldHydrateRouteLoader(routeId, clientLoader, hasLoader, isSpaMode) {\n  return isSpaMode && routeId !== \"root\" || clientLoader != null && (clientLoader.hydrate === true || hasLoader !== true);\n}\n\n// lib/dom/ssr/fog-of-war.ts\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction isFogOfWarEnabled(routeDiscovery, ssr) {\n  return routeDiscovery.mode === \"lazy\" && ssr === true;\n}\nfunction getPartialManifest({ sri, ...manifest }, router) {\n  let routeIds = new Set(router.state.matches.map((m) => m.route.id));\n  let segments = router.state.location.pathname.split(\"/\").filter(Boolean);\n  let paths = [\"/\"];\n  segments.pop();\n  while (segments.length > 0) {\n    paths.push(`/${segments.join(\"/\")}`);\n    segments.pop();\n  }\n  paths.forEach((path) => {\n    let matches = matchRoutes(router.routes, path, router.basename);\n    if (matches) {\n      matches.forEach((m) => routeIds.add(m.route.id));\n    }\n  });\n  let initialRoutes = [...routeIds].reduce(\n    (acc, id) => Object.assign(acc, { [id]: manifest.routes[id] }),\n    {}\n  );\n  return {\n    ...manifest,\n    routes: initialRoutes,\n    sri: sri ? true : void 0\n  };\n}\nfunction getPatchRoutesOnNavigationFunction(manifest, routeModules, ssr, routeDiscovery, isSpaMode, basename) {\n  if (!isFogOfWarEnabled(routeDiscovery, ssr)) {\n    return void 0;\n  }\n  return async ({ path, patch, signal, fetcherKey }) => {\n    if (discoveredPaths.has(path)) {\n      return;\n    }\n    await fetchAndApplyManifestPatches(\n      [path],\n      fetcherKey ? window.location.href : path,\n      manifest,\n      routeModules,\n      ssr,\n      isSpaMode,\n      basename,\n      routeDiscovery.manifestPath,\n      patch,\n      signal\n    );\n  };\n}\nfunction useFogOFWarDiscovery(router, manifest, routeModules, ssr, routeDiscovery, isSpaMode) {\n  React8.useEffect(() => {\n    if (!isFogOfWarEnabled(routeDiscovery, ssr) || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let lazyPaths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (lazyPaths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          lazyPaths,\n          null,\n          manifest,\n          routeModules,\n          ssr,\n          isSpaMode,\n          router.basename,\n          routeDiscovery.manifestPath,\n          router.patchRoutes\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n    return () => observer.disconnect();\n  }, [ssr, isSpaMode, manifest, routeModules, router, routeDiscovery]);\n}\nfunction getManifestPath(_manifestPath, basename) {\n  let manifestPath = _manifestPath || \"/__manifest\";\n  if (basename == null) {\n    return manifestPath;\n  }\n  return `${basename}${manifestPath}`.replace(/\\/+/g, \"/\");\n}\nvar MANIFEST_VERSION_STORAGE_KEY = \"react-router-manifest-version\";\nasync function fetchAndApplyManifestPatches(paths, errorReloadPath, manifest, routeModules, ssr, isSpaMode, basename, manifestPath, patchRoutes, signal) {\n  let url = new URL(\n    getManifestPath(manifestPath, basename),\n    window.location.origin\n  );\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  url.searchParams.set(\"version\", manifest.version);\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let serverPatches;\n  try {\n    let res = await fetch(url, { signal });\n    if (!res.ok) {\n      throw new Error(`${res.status} ${res.statusText}`);\n    } else if (res.status === 204 && res.headers.has(\"X-Remix-Reload-Document\")) {\n      if (!errorReloadPath) {\n        console.warn(\n          \"Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.\"\n        );\n        return;\n      }\n      if (sessionStorage.getItem(MANIFEST_VERSION_STORAGE_KEY) === manifest.version) {\n        console.error(\n          \"Unable to discover routes due to manifest version mismatch.\"\n        );\n        return;\n      }\n      sessionStorage.setItem(MANIFEST_VERSION_STORAGE_KEY, manifest.version);\n      window.location.href = errorReloadPath;\n      console.warn(\"Detected manifest version mismatch, reloading...\");\n      await new Promise(() => {\n      });\n    } else if (res.status >= 400) {\n      throw new Error(await res.text());\n    }\n    sessionStorage.removeItem(MANIFEST_VERSION_STORAGE_KEY);\n    serverPatches = await res.json();\n  } catch (e) {\n    if (signal?.aborted) return;\n    throw e;\n  }\n  let knownRoutes = new Set(Object.keys(manifest.routes));\n  let patches = Object.values(serverPatches).reduce((acc, route) => {\n    if (route && !knownRoutes.has(route.id)) {\n      acc[route.id] = route;\n    }\n    return acc;\n  }, {});\n  Object.assign(manifest.routes, patches);\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  let parentIds = /* @__PURE__ */ new Set();\n  Object.values(patches).forEach((patch) => {\n    if (patch && (!patch.parentId || !patches[patch.parentId])) {\n      parentIds.add(patch.parentId);\n    }\n  });\n  parentIds.forEach(\n    (parentId) => patchRoutes(\n      parentId || null,\n      createClientRoutes(patches, routeModules, null, ssr, isSpaMode, parentId)\n    )\n  );\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/dom/ssr/components.tsx\nfunction useDataRouterContext2() {\n  let context = React9.useContext(DataRouterContext);\n  invariant2(\n    context,\n    \"You must render this element inside a <DataRouterContext.Provider> element\"\n  );\n  return context;\n}\nfunction useDataRouterStateContext() {\n  let context = React9.useContext(DataRouterStateContext);\n  invariant2(\n    context,\n    \"You must render this element inside a <DataRouterStateContext.Provider> element\"\n  );\n  return context;\n}\nvar FrameworkContext = React9.createContext(void 0);\nFrameworkContext.displayName = \"FrameworkContext\";\nfunction useFrameworkContext() {\n  let context = React9.useContext(FrameworkContext);\n  invariant2(\n    context,\n    \"You must render this element inside a <HydratedRouter> element\"\n  );\n  return context;\n}\nfunction usePrefetchBehavior(prefetch, theirElementProps) {\n  let frameworkContext = React9.useContext(FrameworkContext);\n  let [maybePrefetch, setMaybePrefetch] = React9.useState(false);\n  let [shouldPrefetch, setShouldPrefetch] = React9.useState(false);\n  let { onFocus, onBlur, onMouseEnter, onMouseLeave, onTouchStart } = theirElementProps;\n  let ref = React9.useRef(null);\n  React9.useEffect(() => {\n    if (prefetch === \"render\") {\n      setShouldPrefetch(true);\n    }\n    if (prefetch === \"viewport\") {\n      let callback = (entries) => {\n        entries.forEach((entry) => {\n          setShouldPrefetch(entry.isIntersecting);\n        });\n      };\n      let observer = new IntersectionObserver(callback, { threshold: 0.5 });\n      if (ref.current) observer.observe(ref.current);\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [prefetch]);\n  React9.useEffect(() => {\n    if (maybePrefetch) {\n      let id = setTimeout(() => {\n        setShouldPrefetch(true);\n      }, 100);\n      return () => {\n        clearTimeout(id);\n      };\n    }\n  }, [maybePrefetch]);\n  let setIntent = () => {\n    setMaybePrefetch(true);\n  };\n  let cancelIntent = () => {\n    setMaybePrefetch(false);\n    setShouldPrefetch(false);\n  };\n  if (!frameworkContext) {\n    return [false, ref, {}];\n  }\n  if (prefetch !== \"intent\") {\n    return [shouldPrefetch, ref, {}];\n  }\n  return [\n    shouldPrefetch,\n    ref,\n    {\n      onFocus: composeEventHandlers(onFocus, setIntent),\n      onBlur: composeEventHandlers(onBlur, cancelIntent),\n      onMouseEnter: composeEventHandlers(onMouseEnter, setIntent),\n      onMouseLeave: composeEventHandlers(onMouseLeave, cancelIntent),\n      onTouchStart: composeEventHandlers(onTouchStart, setIntent)\n    }\n  ];\n}\nfunction composeEventHandlers(theirHandler, ourHandler) {\n  return (event) => {\n    theirHandler && theirHandler(event);\n    if (!event.defaultPrevented) {\n      ourHandler(event);\n    }\n  };\n}\nfunction getActiveMatches(matches, errors, isSpaMode) {\n  if (isSpaMode && !isHydrated) {\n    return [matches[0]];\n  }\n  if (errors) {\n    let errorIdx = matches.findIndex((m) => errors[m.route.id] !== void 0);\n    return matches.slice(0, errorIdx + 1);\n  }\n  return matches;\n}\nfunction Links() {\n  let { isSpaMode, manifest, routeModules, criticalCss } = useFrameworkContext();\n  let { errors, matches: routerMatches } = useDataRouterStateContext();\n  let matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let keyedLinks = React9.useMemo(\n    () => getKeyedLinksForMatches(matches, routeModules, manifest),\n    [matches, routeModules, manifest]\n  );\n  return /* @__PURE__ */ React9.createElement(React9.Fragment, null, typeof criticalCss === \"string\" ? /* @__PURE__ */ React9.createElement(\"style\", { dangerouslySetInnerHTML: { __html: criticalCss } }) : null, typeof criticalCss === \"object\" ? /* @__PURE__ */ React9.createElement(\"link\", { rel: \"stylesheet\", href: criticalCss.href }) : null, keyedLinks.map(\n    ({ key, link }) => isPageLinkDescriptor(link) ? /* @__PURE__ */ React9.createElement(PrefetchPageLinks, { key, ...link }) : /* @__PURE__ */ React9.createElement(\"link\", { key, ...link })\n  ));\n}\nfunction PrefetchPageLinks({\n  page,\n  ...dataLinkProps\n}) {\n  let { router } = useDataRouterContext2();\n  let matches = React9.useMemo(\n    () => matchRoutes(router.routes, page, router.basename),\n    [router.routes, page, router.basename]\n  );\n  if (!matches) {\n    return null;\n  }\n  return /* @__PURE__ */ React9.createElement(PrefetchPageLinksImpl, { page, matches, ...dataLinkProps });\n}\nfunction useKeyedPrefetchLinks(matches) {\n  let { manifest, routeModules } = useFrameworkContext();\n  let [keyedPrefetchLinks, setKeyedPrefetchLinks] = React9.useState([]);\n  React9.useEffect(() => {\n    let interrupted = false;\n    void getKeyedPrefetchLinks(matches, manifest, routeModules).then(\n      (links) => {\n        if (!interrupted) {\n          setKeyedPrefetchLinks(links);\n        }\n      }\n    );\n    return () => {\n      interrupted = true;\n    };\n  }, [matches, manifest, routeModules]);\n  return keyedPrefetchLinks;\n}\nfunction PrefetchPageLinksImpl({\n  page,\n  matches: nextMatches,\n  ...linkProps\n}) {\n  let location = useLocation();\n  let { manifest, routeModules } = useFrameworkContext();\n  let { basename } = useDataRouterContext2();\n  let { loaderData, matches } = useDataRouterStateContext();\n  let newMatchesForData = React9.useMemo(\n    () => getNewMatchesForLinks(\n      page,\n      nextMatches,\n      matches,\n      manifest,\n      location,\n      \"data\"\n    ),\n    [page, nextMatches, matches, manifest, location]\n  );\n  let newMatchesForAssets = React9.useMemo(\n    () => getNewMatchesForLinks(\n      page,\n      nextMatches,\n      matches,\n      manifest,\n      location,\n      \"assets\"\n    ),\n    [page, nextMatches, matches, manifest, location]\n  );\n  let dataHrefs = React9.useMemo(() => {\n    if (page === location.pathname + location.search + location.hash) {\n      return [];\n    }\n    let routesParams = /* @__PURE__ */ new Set();\n    let foundOptOutRoute = false;\n    nextMatches.forEach((m) => {\n      let manifestRoute = manifest.routes[m.route.id];\n      if (!manifestRoute || !manifestRoute.hasLoader) {\n        return;\n      }\n      if (!newMatchesForData.some((m2) => m2.route.id === m.route.id) && m.route.id in loaderData && routeModules[m.route.id]?.shouldRevalidate) {\n        foundOptOutRoute = true;\n      } else if (manifestRoute.hasClientLoader) {\n        foundOptOutRoute = true;\n      } else {\n        routesParams.add(m.route.id);\n      }\n    });\n    if (routesParams.size === 0) {\n      return [];\n    }\n    let url = singleFetchUrl(page, basename);\n    if (foundOptOutRoute && routesParams.size > 0) {\n      url.searchParams.set(\n        \"_routes\",\n        nextMatches.filter((m) => routesParams.has(m.route.id)).map((m) => m.route.id).join(\",\")\n      );\n    }\n    return [url.pathname + url.search];\n  }, [\n    basename,\n    loaderData,\n    location,\n    manifest,\n    newMatchesForData,\n    nextMatches,\n    page,\n    routeModules\n  ]);\n  let moduleHrefs = React9.useMemo(\n    () => getModuleLinkHrefs(newMatchesForAssets, manifest),\n    [newMatchesForAssets, manifest]\n  );\n  let keyedPrefetchLinks = useKeyedPrefetchLinks(newMatchesForAssets);\n  return /* @__PURE__ */ React9.createElement(React9.Fragment, null, dataHrefs.map((href2) => /* @__PURE__ */ React9.createElement(\"link\", { key: href2, rel: \"prefetch\", as: \"fetch\", href: href2, ...linkProps })), moduleHrefs.map((href2) => /* @__PURE__ */ React9.createElement(\"link\", { key: href2, rel: \"modulepreload\", href: href2, ...linkProps })), keyedPrefetchLinks.map(({ key, link }) => (\n    // these don't spread `linkProps` because they are full link descriptors\n    // already with their own props\n    /* @__PURE__ */ React9.createElement(\"link\", { key, ...link })\n  )));\n}\nfunction Meta() {\n  let { isSpaMode, routeModules } = useFrameworkContext();\n  let {\n    errors,\n    matches: routerMatches,\n    loaderData\n  } = useDataRouterStateContext();\n  let location = useLocation();\n  let _matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let error = null;\n  if (errors) {\n    error = errors[_matches[_matches.length - 1].route.id];\n  }\n  let meta = [];\n  let leafMeta = null;\n  let matches = [];\n  for (let i = 0; i < _matches.length; i++) {\n    let _match = _matches[i];\n    let routeId = _match.route.id;\n    let data2 = loaderData[routeId];\n    let params = _match.params;\n    let routeModule = routeModules[routeId];\n    let routeMeta = [];\n    let match = {\n      id: routeId,\n      data: data2,\n      meta: [],\n      params: _match.params,\n      pathname: _match.pathname,\n      handle: _match.route.handle,\n      error\n    };\n    matches[i] = match;\n    if (routeModule?.meta) {\n      routeMeta = typeof routeModule.meta === \"function\" ? routeModule.meta({\n        data: data2,\n        params,\n        location,\n        matches,\n        error\n      }) : Array.isArray(routeModule.meta) ? [...routeModule.meta] : routeModule.meta;\n    } else if (leafMeta) {\n      routeMeta = [...leafMeta];\n    }\n    routeMeta = routeMeta || [];\n    if (!Array.isArray(routeMeta)) {\n      throw new Error(\n        \"The route at \" + _match.route.path + \" returns an invalid value. All route meta functions must return an array of meta objects.\\n\\nTo reference the meta function API, see https://remix.run/route/meta\"\n      );\n    }\n    match.meta = routeMeta;\n    matches[i] = match;\n    meta = [...routeMeta];\n    leafMeta = meta;\n  }\n  return /* @__PURE__ */ React9.createElement(React9.Fragment, null, meta.flat().map((metaProps) => {\n    if (!metaProps) {\n      return null;\n    }\n    if (\"tagName\" in metaProps) {\n      let { tagName, ...rest } = metaProps;\n      if (!isValidMetaTag(tagName)) {\n        console.warn(\n          `A meta object uses an invalid tagName: ${tagName}. Expected either 'link' or 'meta'`\n        );\n        return null;\n      }\n      let Comp = tagName;\n      return /* @__PURE__ */ React9.createElement(Comp, { key: JSON.stringify(rest), ...rest });\n    }\n    if (\"title\" in metaProps) {\n      return /* @__PURE__ */ React9.createElement(\"title\", { key: \"title\" }, String(metaProps.title));\n    }\n    if (\"charset\" in metaProps) {\n      metaProps.charSet ?? (metaProps.charSet = metaProps.charset);\n      delete metaProps.charset;\n    }\n    if (\"charSet\" in metaProps && metaProps.charSet != null) {\n      return typeof metaProps.charSet === \"string\" ? /* @__PURE__ */ React9.createElement(\"meta\", { key: \"charSet\", charSet: metaProps.charSet }) : null;\n    }\n    if (\"script:ld+json\" in metaProps) {\n      try {\n        let json = JSON.stringify(metaProps[\"script:ld+json\"]);\n        return /* @__PURE__ */ React9.createElement(\n          \"script\",\n          {\n            key: `script:ld+json:${json}`,\n            type: \"application/ld+json\",\n            dangerouslySetInnerHTML: { __html: json }\n          }\n        );\n      } catch (err) {\n        return null;\n      }\n    }\n    return /* @__PURE__ */ React9.createElement(\"meta\", { key: JSON.stringify(metaProps), ...metaProps });\n  }));\n}\nfunction isValidMetaTag(tagName) {\n  return typeof tagName === \"string\" && /^(meta|link)$/.test(tagName);\n}\nvar isHydrated = false;\nfunction Scripts(props) {\n  let {\n    manifest,\n    serverHandoffString,\n    isSpaMode,\n    renderMeta,\n    routeDiscovery,\n    ssr\n  } = useFrameworkContext();\n  let { router, static: isStatic, staticContext } = useDataRouterContext2();\n  let { matches: routerMatches } = useDataRouterStateContext();\n  let enableFogOfWar = isFogOfWarEnabled(routeDiscovery, ssr);\n  if (renderMeta) {\n    renderMeta.didRenderScripts = true;\n  }\n  let matches = getActiveMatches(routerMatches, null, isSpaMode);\n  React9.useEffect(() => {\n    isHydrated = true;\n  }, []);\n  let initialScripts = React9.useMemo(() => {\n    let streamScript = \"window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());\";\n    let contextScript = staticContext ? `window.__reactRouterContext = ${serverHandoffString};${streamScript}` : \" \";\n    let routeModulesScript = !isStatic ? \" \" : `${manifest.hmr?.runtime ? `import ${JSON.stringify(manifest.hmr.runtime)};` : \"\"}${!enableFogOfWar ? `import ${JSON.stringify(manifest.url)}` : \"\"};\n${matches.map((match, routeIndex) => {\n      let routeVarName = `route${routeIndex}`;\n      let manifestEntry = manifest.routes[match.route.id];\n      invariant2(manifestEntry, `Route ${match.route.id} not found in manifest`);\n      let {\n        clientActionModule,\n        clientLoaderModule,\n        clientMiddlewareModule,\n        hydrateFallbackModule,\n        module\n      } = manifestEntry;\n      let chunks = [\n        ...clientActionModule ? [\n          {\n            module: clientActionModule,\n            varName: `${routeVarName}_clientAction`\n          }\n        ] : [],\n        ...clientLoaderModule ? [\n          {\n            module: clientLoaderModule,\n            varName: `${routeVarName}_clientLoader`\n          }\n        ] : [],\n        ...clientMiddlewareModule ? [\n          {\n            module: clientMiddlewareModule,\n            varName: `${routeVarName}_clientMiddleware`\n          }\n        ] : [],\n        ...hydrateFallbackModule ? [\n          {\n            module: hydrateFallbackModule,\n            varName: `${routeVarName}_HydrateFallback`\n          }\n        ] : [],\n        { module, varName: `${routeVarName}_main` }\n      ];\n      if (chunks.length === 1) {\n        return `import * as ${routeVarName} from ${JSON.stringify(module)};`;\n      }\n      let chunkImportsSnippet = chunks.map((chunk) => `import * as ${chunk.varName} from \"${chunk.module}\";`).join(\"\\n\");\n      let mergedChunksSnippet = `const ${routeVarName} = {${chunks.map((chunk) => `...${chunk.varName}`).join(\",\")}};`;\n      return [chunkImportsSnippet, mergedChunksSnippet].join(\"\\n\");\n    }).join(\"\\n\")}\n  ${enableFogOfWar ? (\n      // Inline a minimal manifest with the SSR matches\n      `window.__reactRouterManifest = ${JSON.stringify(\n        getPartialManifest(manifest, router),\n        null,\n        2\n      )};`\n    ) : \"\"}\n  window.__reactRouterRouteModules = {${matches.map((match, index) => `${JSON.stringify(match.route.id)}:route${index}`).join(\",\")}};\n\nimport(${JSON.stringify(manifest.entry.module)});`;\n    return /* @__PURE__ */ React9.createElement(React9.Fragment, null, /* @__PURE__ */ React9.createElement(\n      \"script\",\n      {\n        ...props,\n        suppressHydrationWarning: true,\n        dangerouslySetInnerHTML: createHtml(contextScript),\n        type: void 0\n      }\n    ), /* @__PURE__ */ React9.createElement(\n      \"script\",\n      {\n        ...props,\n        suppressHydrationWarning: true,\n        dangerouslySetInnerHTML: createHtml(routeModulesScript),\n        type: \"module\",\n        async: true\n      }\n    ));\n  }, []);\n  let preloads = isHydrated ? [] : dedupe(\n    manifest.entry.imports.concat(\n      getModuleLinkHrefs(matches, manifest, {\n        includeHydrateFallback: true\n      })\n    )\n  );\n  let sri = typeof manifest.sri === \"object\" ? manifest.sri : {};\n  return isHydrated ? null : /* @__PURE__ */ React9.createElement(React9.Fragment, null, typeof manifest.sri === \"object\" ? /* @__PURE__ */ React9.createElement(\n    \"script\",\n    {\n      \"rr-importmap\": \"\",\n      type: \"importmap\",\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: {\n        __html: JSON.stringify({\n          integrity: sri\n        })\n      }\n    }\n  ) : null, !enableFogOfWar ? /* @__PURE__ */ React9.createElement(\n    \"link\",\n    {\n      rel: \"modulepreload\",\n      href: manifest.url,\n      crossOrigin: props.crossOrigin,\n      integrity: sri[manifest.url],\n      suppressHydrationWarning: true\n    }\n  ) : null, /* @__PURE__ */ React9.createElement(\n    \"link\",\n    {\n      rel: \"modulepreload\",\n      href: manifest.entry.module,\n      crossOrigin: props.crossOrigin,\n      integrity: sri[manifest.entry.module],\n      suppressHydrationWarning: true\n    }\n  ), preloads.map((path) => /* @__PURE__ */ React9.createElement(\n    \"link\",\n    {\n      key: path,\n      rel: \"modulepreload\",\n      href: path,\n      crossOrigin: props.crossOrigin,\n      integrity: sri[path],\n      suppressHydrationWarning: true\n    }\n  )), initialScripts);\n}\nfunction dedupe(array) {\n  return [...new Set(array)];\n}\nfunction mergeRefs(...refs) {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\n// lib/dom/lib.tsx\nvar isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\ntry {\n  if (isBrowser) {\n    window.__reactRouterVersion = \"7.6.3\";\n  }\n} catch (e) {\n}\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts?.basename,\n    unstable_getContext: opts?.unstable_getContext,\n    future: opts?.future,\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    hydrationRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts?.basename,\n    unstable_getContext: opts?.unstable_getContext,\n    future: opts?.future,\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    hydrationRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window\n  }).initialize();\n}\nfunction parseHydrationData() {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors)\n    };\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nfunction BrowserRouter({\n  basename,\n  children,\n  window: window2\n}) {\n  let historyRef = React10.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window: window2, v5Compat: true });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React10.useState({\n    action: history.action,\n    location: history.location\n  });\n  let setState = React10.useCallback(\n    (newState) => {\n      React10.startTransition(() => setStateImpl(newState));\n    },\n    [setStateImpl]\n  );\n  React10.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /* @__PURE__ */ React10.createElement(\n    Router,\n    {\n      basename,\n      children,\n      location: state.location,\n      navigationType: state.action,\n      navigator: history\n    }\n  );\n}\nfunction HashRouter({ basename, children, window: window2 }) {\n  let historyRef = React10.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window: window2, v5Compat: true });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React10.useState({\n    action: history.action,\n    location: history.location\n  });\n  let setState = React10.useCallback(\n    (newState) => {\n      React10.startTransition(() => setStateImpl(newState));\n    },\n    [setStateImpl]\n  );\n  React10.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /* @__PURE__ */ React10.createElement(\n    Router,\n    {\n      basename,\n      children,\n      location: state.location,\n      navigationType: state.action,\n      navigator: history\n    }\n  );\n}\nfunction HistoryRouter({\n  basename,\n  children,\n  history\n}) {\n  let [state, setStateImpl] = React10.useState({\n    action: history.action,\n    location: history.location\n  });\n  let setState = React10.useCallback(\n    (newState) => {\n      React10.startTransition(() => setStateImpl(newState));\n    },\n    [setStateImpl]\n  );\n  React10.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /* @__PURE__ */ React10.createElement(\n    Router,\n    {\n      basename,\n      children,\n      location: state.location,\n      navigationType: state.action,\n      navigator: history\n    }\n  );\n}\nHistoryRouter.displayName = \"unstable_HistoryRouter\";\nvar ABSOLUTE_URL_REGEX2 = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nvar Link = React10.forwardRef(\n  function LinkWithRef({\n    onClick,\n    discover = \"render\",\n    prefetch = \"none\",\n    relative,\n    reloadDocument,\n    replace: replace2,\n    state,\n    target,\n    to,\n    preventScrollReset,\n    viewTransition,\n    ...rest\n  }, forwardedRef) {\n    let { basename } = React10.useContext(NavigationContext);\n    let isAbsolute = typeof to === \"string\" && ABSOLUTE_URL_REGEX2.test(to);\n    let absoluteHref;\n    let isExternal = false;\n    if (typeof to === \"string\" && isAbsolute) {\n      absoluteHref = to;\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\") ? new URL(currentUrl.protocol + to) : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n    let href2 = useHref(to, { relative });\n    let [shouldPrefetch, prefetchRef, prefetchHandlers] = usePrefetchBehavior(\n      prefetch,\n      rest\n    );\n    let internalOnClick = useLinkClickHandler(to, {\n      replace: replace2,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition\n    });\n    function handleClick(event) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n    let link = (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      /* @__PURE__ */ React10.createElement(\n        \"a\",\n        {\n          ...rest,\n          ...prefetchHandlers,\n          href: absoluteHref || href2,\n          onClick: isExternal || reloadDocument ? onClick : handleClick,\n          ref: mergeRefs(forwardedRef, prefetchRef),\n          target,\n          \"data-discover\": !isAbsolute && discover === \"render\" ? \"true\" : void 0\n        }\n      )\n    );\n    return shouldPrefetch && !isAbsolute ? /* @__PURE__ */ React10.createElement(React10.Fragment, null, link, /* @__PURE__ */ React10.createElement(PrefetchPageLinks, { page: href2 })) : link;\n  }\n);\nLink.displayName = \"Link\";\nvar NavLink = React10.forwardRef(\n  function NavLinkWithRef({\n    \"aria-current\": ariaCurrentProp = \"page\",\n    caseSensitive = false,\n    className: classNameProp = \"\",\n    end = false,\n    style: styleProp,\n    to,\n    viewTransition,\n    children,\n    ...rest\n  }, ref) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React10.useContext(DataRouterStateContext);\n    let { navigator, basename } = React10.useContext(NavigationContext);\n    let isTransitioning = routerState != null && // Conditional usage is OK here because the usage of a data router is static\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useViewTransitionState(path) && viewTransition === true;\n    let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n      toPathname = toPathname.toLowerCase();\n    }\n    if (nextLocationPathname && basename) {\n      nextLocationPathname = stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n    const endSlashPosition = toPathname !== \"/\" && toPathname.endsWith(\"/\") ? toPathname.length - 1 : toPathname.length;\n    let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(endSlashPosition) === \"/\";\n    let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning\n    };\n    let ariaCurrent = isActive ? ariaCurrentProp : void 0;\n    let className;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null\n      ].filter(Boolean).join(\" \");\n    }\n    let style = typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n    return /* @__PURE__ */ React10.createElement(\n      Link,\n      {\n        ...rest,\n        \"aria-current\": ariaCurrent,\n        className,\n        ref,\n        style,\n        to,\n        viewTransition\n      },\n      typeof children === \"function\" ? children(renderProps) : children\n    );\n  }\n);\nNavLink.displayName = \"NavLink\";\nvar Form = React10.forwardRef(\n  ({\n    discover = \"render\",\n    fetcherKey,\n    navigate,\n    reloadDocument,\n    replace: replace2,\n    state,\n    method = defaultMethod,\n    action,\n    onSubmit,\n    relative,\n    preventScrollReset,\n    viewTransition,\n    ...props\n  }, forwardedRef) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let isAbsolute = typeof action === \"string\" && ABSOLUTE_URL_REGEX2.test(action);\n    let submitHandler = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n      let submitter = event.nativeEvent.submitter;\n      let submitMethod = submitter?.getAttribute(\"formmethod\") || method;\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace: replace2,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition\n      });\n    };\n    return /* @__PURE__ */ React10.createElement(\n      \"form\",\n      {\n        ref: forwardedRef,\n        method: formMethod,\n        action: formAction,\n        onSubmit: reloadDocument ? onSubmit : submitHandler,\n        ...props,\n        \"data-discover\": !isAbsolute && discover === \"render\" ? \"true\" : void 0\n      }\n    );\n  }\n);\nForm.displayName = \"Form\";\nfunction ScrollRestoration({\n  getKey,\n  storageKey,\n  ...props\n}) {\n  let remixContext = React10.useContext(FrameworkContext);\n  let { basename } = React10.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  useScrollRestoration({ getKey, storageKey });\n  let ssrKey = React10.useMemo(\n    () => {\n      if (!remixContext || !getKey) return null;\n      let userKey = getScrollRestorationKey(\n        location,\n        matches,\n        basename,\n        getKey\n      );\n      return userKey !== location.key ? userKey : null;\n    },\n    // Nah, we only need this the first time for the SSR render\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []\n  );\n  if (!remixContext || remixContext.isSpaMode) {\n    return null;\n  }\n  let restoreScroll = ((storageKey2, restoreKey) => {\n    if (!window.history.state || !window.history.state.key) {\n      let key = Math.random().toString(32).slice(2);\n      window.history.replaceState({ key }, \"\");\n    }\n    try {\n      let positions = JSON.parse(sessionStorage.getItem(storageKey2) || \"{}\");\n      let storedY = positions[restoreKey || window.history.state.key];\n      if (typeof storedY === \"number\") {\n        window.scrollTo(0, storedY);\n      }\n    } catch (error) {\n      console.error(error);\n      sessionStorage.removeItem(storageKey2);\n    }\n  }).toString();\n  return /* @__PURE__ */ React10.createElement(\n    \"script\",\n    {\n      ...props,\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: {\n        __html: `(${restoreScroll})(${JSON.stringify(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        )}, ${JSON.stringify(ssrKey)})`\n      }\n    }\n  );\n}\nScrollRestoration.displayName = \"ScrollRestoration\";\nfunction getDataRouterConsoleError2(hookName) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`;\n}\nfunction useDataRouterContext3(hookName) {\n  let ctx = React10.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError2(hookName));\n  return ctx;\n}\nfunction useDataRouterState2(hookName) {\n  let state = React10.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError2(hookName));\n  return state;\n}\nfunction useLinkClickHandler(to, {\n  target,\n  replace: replaceProp,\n  state,\n  preventScrollReset,\n  relative,\n  viewTransition\n} = {}) {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n  return React10.useCallback(\n    (event) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n        let replace2 = replaceProp !== void 0 ? replaceProp : createPath(location) === createPath(path);\n        navigate(to, {\n          replace: replace2,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition\n    ]\n  );\n}\nfunction useSearchParams(defaultInit) {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.`\n  );\n  let defaultSearchParamsRef = React10.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React10.useRef(false);\n  let location = useLocation();\n  let searchParams = React10.useMemo(\n    () => (\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      )\n    ),\n    [location.search]\n  );\n  let navigate = useNavigate();\n  let setSearchParams = React10.useCallback(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n  return [searchParams, setSearchParams];\n}\nvar fetcherId = 0;\nvar getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\nfunction useSubmit() {\n  let { router } = useDataRouterContext3(\"useSubmit\" /* UseSubmit */);\n  let { basename } = React10.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n  return React10.useCallback(\n    async (target, options = {}) => {\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        await router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || method,\n          formEncType: options.encType || encType,\n          flushSync: options.flushSync\n        });\n      } else {\n        await router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || method,\n          formEncType: options.encType || encType,\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\nfunction useFormAction(action, { relative } = {}) {\n  let { basename } = React10.useContext(NavigationContext);\n  let routeContext = React10.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n  let [match] = routeContext.matches.slice(-1);\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n  let location = useLocation();\n  if (action == null) {\n    path.search = location.search;\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\nfunction useFetcher({\n  key\n} = {}) {\n  let { router } = useDataRouterContext3(\"useFetcher\" /* UseFetcher */);\n  let state = useDataRouterState2(\"useFetcher\" /* UseFetcher */);\n  let fetcherData = React10.useContext(FetchersContext);\n  let route = React10.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n  let defaultKey = React10.useId();\n  let [fetcherKey, setFetcherKey] = React10.useState(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  }\n  React10.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => router.deleteFetcher(fetcherKey);\n  }, [router, fetcherKey]);\n  let load = React10.useCallback(\n    async (href2, opts) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      await router.fetch(fetcherKey, routeId, href2, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n  let submitImpl = useSubmit();\n  let submit = React10.useCallback(\n    async (target, opts) => {\n      await submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n  let FetcherForm = React10.useMemo(() => {\n    let FetcherForm2 = React10.forwardRef(\n      (props, ref) => {\n        return /* @__PURE__ */ React10.createElement(Form, { ...props, navigate: false, fetcherKey, ref });\n      }\n    );\n    FetcherForm2.displayName = \"fetcher.Form\";\n    return FetcherForm2;\n  }, [fetcherKey]);\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data2 = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React10.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data: data2\n    }),\n    [FetcherForm, submit, load, fetcher, data2]\n  );\n  return fetcherWithComponents;\n}\nfunction useFetchers() {\n  let state = useDataRouterState2(\"useFetchers\" /* UseFetchers */);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key\n  }));\n}\nvar SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nvar savedScrollPositions = {};\nfunction getScrollRestorationKey(location, matches, basename, getKey) {\n  let key = null;\n  if (getKey) {\n    if (basename !== \"/\") {\n      key = getKey(\n        {\n          ...location,\n          pathname: stripBasename(location.pathname, basename) || location.pathname\n        },\n        matches\n      );\n    } else {\n      key = getKey(location, matches);\n    }\n  }\n  if (key == null) {\n    key = location.key;\n  }\n  return key;\n}\nfunction useScrollRestoration({\n  getKey,\n  storageKey\n} = {}) {\n  let { router } = useDataRouterContext3(\"useScrollRestoration\" /* UseScrollRestoration */);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState2(\n    \"useScrollRestoration\" /* UseScrollRestoration */\n  );\n  let { basename } = React10.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n  React10.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n  usePageHide(\n    React10.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = getScrollRestorationKey(location, matches, basename, getKey);\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [navigation.state, getKey, basename, location, matches, storageKey])\n  );\n  if (typeof document !== \"undefined\") {\n    React10.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n      }\n    }, [storageKey]);\n    React10.useLayoutEffect(() => {\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKey ? (location2, matches2) => getScrollRestorationKey(location2, matches2, basename, getKey) : void 0\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n    React10.useLayoutEffect(() => {\n      if (restoreScrollPosition === false) {\n        return;\n      }\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n      if (preventScrollReset === true) {\n        return;\n      }\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\nfunction useBeforeUnload(callback, options) {\n  let { capture } = options || {};\n  React10.useEffect(() => {\n    let opts = capture != null ? { capture } : void 0;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\nfunction usePageHide(callback, options) {\n  let { capture } = options || {};\n  React10.useEffect(() => {\n    let opts = capture != null ? { capture } : void 0;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\nfunction usePrompt({\n  when,\n  message\n}) {\n  let blocker = useBlocker(when);\n  React10.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n  React10.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\nfunction useViewTransitionState(to, opts = {}) {\n  let vtContext = React10.useContext(ViewTransitionContext);\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n  let { basename } = useDataRouterContext3(\n    \"useViewTransitionState\" /* useViewTransitionState */\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n  let currentPath = stripBasename(vtContext.currentLocation.pathname, basename) || vtContext.currentLocation.pathname;\n  let nextPath = stripBasename(vtContext.nextLocation.pathname, basename) || vtContext.nextLocation.pathname;\n  return matchPath(path.pathname, nextPath) != null || matchPath(path.pathname, currentPath) != null;\n}\n\n// lib/dom/server.tsx\nimport * as React11 from \"react\";\nfunction StaticRouter({\n  basename,\n  children,\n  location: locationProp = \"/\"\n}) {\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let action = \"POP\" /* Pop */;\n  let location = {\n    pathname: locationProp.pathname || \"/\",\n    search: locationProp.search || \"\",\n    hash: locationProp.hash || \"\",\n    state: locationProp.state != null ? locationProp.state : null,\n    key: locationProp.key || \"default\"\n  };\n  let staticNavigator = getStatelessNavigator();\n  return /* @__PURE__ */ React11.createElement(\n    Router,\n    {\n      basename,\n      children,\n      location,\n      navigationType: action,\n      navigator: staticNavigator,\n      static: true\n    }\n  );\n}\nfunction StaticRouterProvider({\n  context,\n  router,\n  hydrate: hydrate2 = true,\n  nonce\n}) {\n  invariant(\n    router && context,\n    \"You must provide `router` and `context` to <StaticRouterProvider>\"\n  );\n  let dataRouterContext = {\n    router,\n    navigator: getStatelessNavigator(),\n    static: true,\n    staticContext: context,\n    basename: context.basename || \"/\"\n  };\n  let fetchersContext = /* @__PURE__ */ new Map();\n  let hydrateScript = \"\";\n  if (hydrate2 !== false) {\n    let data2 = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors)\n    };\n    let json = htmlEscape(JSON.stringify(JSON.stringify(data2)));\n    hydrateScript = `window.__staticRouterHydrationData = JSON.parse(${json});`;\n  }\n  let { state } = dataRouterContext.router;\n  return /* @__PURE__ */ React11.createElement(React11.Fragment, null, /* @__PURE__ */ React11.createElement(DataRouterContext.Provider, { value: dataRouterContext }, /* @__PURE__ */ React11.createElement(DataRouterStateContext.Provider, { value: state }, /* @__PURE__ */ React11.createElement(FetchersContext.Provider, { value: fetchersContext }, /* @__PURE__ */ React11.createElement(ViewTransitionContext.Provider, { value: { isTransitioning: false } }, /* @__PURE__ */ React11.createElement(\n    Router,\n    {\n      basename: dataRouterContext.basename,\n      location: state.location,\n      navigationType: state.historyAction,\n      navigator: dataRouterContext.navigator,\n      static: dataRouterContext.static\n    },\n    /* @__PURE__ */ React11.createElement(\n      DataRoutes2,\n      {\n        routes: router.routes,\n        future: router.future,\n        state\n      }\n    )\n  ))))), hydrateScript ? /* @__PURE__ */ React11.createElement(\n    \"script\",\n    {\n      suppressHydrationWarning: true,\n      nonce,\n      dangerouslySetInnerHTML: { __html: hydrateScript }\n    }\n  ) : null);\n}\nfunction DataRoutes2({\n  routes,\n  future,\n  state\n}) {\n  return useRoutesImpl(routes, void 0, state, future);\n}\nfunction serializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      serialized[key] = {\n        message: val.message,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.\n        ...val.name !== \"Error\" ? {\n          __subType: val.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nfunction getStatelessNavigator() {\n  return {\n    createHref,\n    encodeLocation,\n    push(to) {\n      throw new Error(\n        `You cannot use navigator.push() on the server because it is a stateless environment. This error was probably triggered when you did a \\`navigate(${JSON.stringify(to)})\\` somewhere in your app.`\n      );\n    },\n    replace(to) {\n      throw new Error(\n        `You cannot use navigator.replace() on the server because it is a stateless environment. This error was probably triggered when you did a \\`navigate(${JSON.stringify(to)}, { replace: true })\\` somewhere in your app.`\n      );\n    },\n    go(delta) {\n      throw new Error(\n        `You cannot use navigator.go() on the server because it is a stateless environment. This error was probably triggered when you did a \\`navigate(${delta})\\` somewhere in your app.`\n      );\n    },\n    back() {\n      throw new Error(\n        `You cannot use navigator.back() on the server because it is a stateless environment.`\n      );\n    },\n    forward() {\n      throw new Error(\n        `You cannot use navigator.forward() on the server because it is a stateless environment.`\n      );\n    }\n  };\n}\nfunction createStaticHandler2(routes, opts) {\n  return createStaticHandler(routes, {\n    ...opts,\n    mapRouteProperties\n  });\n}\nfunction createStaticRouter(routes, context, opts = {}) {\n  let manifest = {};\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    void 0,\n    manifest\n  );\n  let matches = context.matches.map((match) => {\n    let route = manifest[match.route.id] || match.route;\n    return {\n      ...match,\n      route\n    };\n  });\n  let msg = (method) => `You cannot use router.${method}() on the server because it is a stateless environment`;\n  return {\n    get basename() {\n      return context.basename;\n    },\n    get future() {\n      return {\n        unstable_middleware: false,\n        ...opts?.future\n      };\n    },\n    get state() {\n      return {\n        historyAction: \"POP\" /* Pop */,\n        location: context.location,\n        matches,\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: context.errors,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        restoreScrollPosition: null,\n        preventScrollReset: false,\n        revalidation: \"idle\",\n        fetchers: /* @__PURE__ */ new Map(),\n        blockers: /* @__PURE__ */ new Map()\n      };\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return void 0;\n    },\n    initialize() {\n      throw msg(\"initialize\");\n    },\n    subscribe() {\n      throw msg(\"subscribe\");\n    },\n    enableScrollRestoration() {\n      throw msg(\"enableScrollRestoration\");\n    },\n    navigate() {\n      throw msg(\"navigate\");\n    },\n    fetch() {\n      throw msg(\"fetch\");\n    },\n    revalidate() {\n      throw msg(\"revalidate\");\n    },\n    createHref,\n    encodeLocation,\n    getFetcher() {\n      return IDLE_FETCHER;\n    },\n    deleteFetcher() {\n      throw msg(\"deleteFetcher\");\n    },\n    dispose() {\n      throw msg(\"dispose\");\n    },\n    getBlocker() {\n      return IDLE_BLOCKER;\n    },\n    deleteBlocker() {\n      throw msg(\"deleteBlocker\");\n    },\n    patchRoutes() {\n      throw msg(\"patchRoutes\");\n    },\n    _internalFetchControllers: /* @__PURE__ */ new Map(),\n    _internalSetRoutes() {\n      throw msg(\"_internalSetRoutes\");\n    }\n  };\n}\nfunction createHref(to) {\n  return typeof to === \"string\" ? to : createPath(to);\n}\nfunction encodeLocation(to) {\n  let href2 = typeof to === \"string\" ? to : createPath(to);\n  href2 = href2.replace(/ $/, \"%20\");\n  let encoded = ABSOLUTE_URL_REGEX3.test(href2) ? new URL(href2) : new URL(href2, \"http://localhost\");\n  return {\n    pathname: encoded.pathname,\n    search: encoded.search,\n    hash: encoded.hash\n  };\n}\nvar ABSOLUTE_URL_REGEX3 = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nvar ESCAPE_LOOKUP2 = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX2 = /[&><\\u2028\\u2029]/g;\nfunction htmlEscape(str) {\n  return str.replace(ESCAPE_REGEX2, (match) => ESCAPE_LOOKUP2[match]);\n}\n\n// lib/dom/ssr/server.tsx\nimport * as React12 from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React12.createElement(React12.Fragment, null, /* @__PURE__ */ React12.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React12.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React12.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React12.createElement(React12.Suspense, null, /* @__PURE__ */ React12.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React13 from \"react\";\nfunction createRoutesStub(routes, unstable_getContext) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React13.useRef();\n    let remixContextRef = React13.useRef();\n    if (routerRef.current == null) {\n      remixContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          unstable_middleware: future?.unstable_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        remixContextRef.current.manifest,\n        remixContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        unstable_getContext,\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React13.createElement(FrameworkContext.Provider, { value: remixContextRef.current }, /* @__PURE__ */ React13.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in @remix-run/testing processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action,\n      loader: route.loader,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey2(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey2(secret, [\"verify\"]);\n  let signature = byteStringToUint8Array(atob(hash));\n  let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n  return valid ? value : false;\n};\nvar createKey2 = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo2, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo2[routeId] = route.module;\n    }\n    return memo2;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors2(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant3(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId2(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId2(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId2(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      unstable_middleware: route.module.unstable_middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant3(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant3(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant3(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/markup.ts\nvar ESCAPE_LOOKUP3 = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX3 = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml2(html) {\n  return html.replace(ESCAPE_REGEX3, (match) => ESCAPE_LOOKUP3[match]);\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml2(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(build, context) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant3(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers());\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(build, context);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: getSingleFetchRedirect(\n            context.statusCode,\n            headers,\n            build.basename\n          ),\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let singleFetchResult;\n      if (context.errors) {\n        singleFetchResult = { error: Object.values(context.errors)[0] };\n      } else {\n        singleFetchResult = {\n          data: Object.values(context.actionData || {})[0]\n        };\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: singleFetchResult,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: getSingleFetchRedirect(\n          result.status,\n          result.headers,\n          build.basename\n        ),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let respond2 = function(context) {\n      let headers = getDocumentHeaders(build, context);\n      if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n        return generateSingleFetchResponse(request, build, serverMode, {\n          result: {\n            [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n              context.statusCode,\n              headers,\n              build.basename\n            )\n          },\n          headers,\n          status: SINGLE_FETCH_REDIRECT_STATUS\n        });\n      }\n      if (context.errors) {\n        Object.values(context.errors).forEach((err) => {\n          if (!isRouteErrorResponse(err) || err.error) {\n            handleError(err);\n          }\n        });\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let results = {};\n      let loadedMatches = new Set(\n        context.matches.filter(\n          (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n        ).map((m) => m.route.id)\n      );\n      if (context.errors) {\n        for (let [id, error] of Object.entries(context.errors)) {\n          results[id] = { error };\n        }\n      }\n      for (let [id, data2] of Object.entries(context.loaderData)) {\n        if (!(id in results) && loadedMatches.has(id)) {\n          results[id] = { data: data2 };\n        }\n      }\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: results,\n        headers,\n        status: context.statusCode\n      });\n    };\n    var respond = respond2;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n    let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      unstable_respond: respond2\n    });\n    if (!isResponse(result)) {\n      result = respond2(result);\n    }\n    if (isRedirectResponse(result)) {\n      return generateSingleFetchResponse(request, build, serverMode, {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(\n            result.status,\n            result.headers,\n            build.basename\n          )\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      });\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { root: { error } },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.unstable_middleware) {\n      if (initialContext == null) {\n        loadContext = new unstable_RouterContextProvider();\n      } else {\n        try {\n          loadContext = new unstable_RouterContextProvider(\n            initialContext\n          );\n        } catch (e) {\n          let error = new Error(\n            `Unable to create initial \\`unstable_RouterContextProvider\\` instance. Please confirm you are returning an instance of \\`Map<unstable_routerContext, unknown>\\` from your \\`getLoadContext\\` function.\n\nError: ${e instanceof Error ? e.toString() : e}`\n          );\n          handleError(error);\n          return returnLastResortErrorResponse(error, serverMode);\n        }\n      }\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(normalizedPath) && !_build.prerender.includes(normalizedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${normalizedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          let result = getSingleFetchRedirect(\n            response.status,\n            response.headers,\n            _build.basename\n          );\n          if (request.method === \"GET\") {\n            result = {\n              [SingleFetchRedirectSymbol]: result\n            };\n          }\n          let headers = new Headers(response.headers);\n          headers.set(\"Content-Type\", \"text/x-script\");\n          return new Response(\n            encodeViaTurboStream(\n              result,\n              request.signal,\n              _build.entry.module.streamTimeout,\n              serverMode\n            ),\n            {\n              status: SINGLE_FETCH_REDIRECT_STATUS,\n              headers\n            }\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let response = await staticHandler.query(request, {\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => renderHtml(ctx, isSpaMode) : void 0\n    });\n    return isResponse(response) ? response : renderHtml(response, isSpaMode);\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    if (isResponse(context)) {\n      return context;\n    }\n    let headers = getDocumentHeaders(build, context);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors2(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors2(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let response = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      unstable_respond: build.future.unstable_middleware ? (ctx) => ctx : void 0\n    });\n    if (isResponse(response)) {\n      return response;\n    }\n    if (typeof response === \"string\") {\n      return new Response(response);\n    }\n    return Response.json(response);\n  } catch (error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      if (error) {\n        handleError(error);\n      }\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  return path.split(\"/\").map((segment) => {\n    if (segment === \"*\") {\n      return params ? params[\"*\"] : void 0;\n    }\n    const match = segment.match(/^:([\\w-]+)(\\?)?/);\n    if (!match) return segment;\n    const param = match[1];\n    const value = params ? params[param] : void 0;\n    const isRequired = match[2] === void 0;\n    if (isRequired && value === void 0) {\n      throw Error(\n        `Path '${path}' requires param '${param}' but it was not provided`\n      );\n    }\n    return value;\n  }).filter((segment) => segment !== void 0).join(\"/\");\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors2(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData(state, routes, getRouteInfo, location, basename, isSpaMode) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\nexport {\n  Action,\n  createBrowserHistory,\n  invariant,\n  createPath,\n  parsePath,\n  unstable_createContext,\n  unstable_RouterContextProvider,\n  matchRoutes,\n  generatePath,\n  matchPath,\n  resolvePath,\n  data,\n  redirect,\n  redirectDocument,\n  replace,\n  ErrorResponseImpl,\n  isRouteErrorResponse,\n  IDLE_NAVIGATION,\n  IDLE_FETCHER,\n  IDLE_BLOCKER,\n  createRouter,\n  DataRouterContext,\n  DataRouterStateContext,\n  ViewTransitionContext,\n  FetchersContext,\n  NavigationContext,\n  LocationContext,\n  RouteContext,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useNavigationType,\n  useMatch,\n  useNavigate,\n  useOutletContext,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useNavigation,\n  useRevalidator,\n  useMatches,\n  useLoaderData,\n  useRouteLoaderData,\n  useActionData,\n  useRouteError,\n  useAsyncValue,\n  useAsyncError,\n  useBlocker,\n  mapRouteProperties,\n  hydrationRouteProperties,\n  createMemoryRouter,\n  RouterProvider,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  Await,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  renderMatches,\n  withComponentProps,\n  withHydrateFallbackProps,\n  withErrorBoundaryProps,\n  createSearchParams,\n  SingleFetchRedirectSymbol,\n  getTurboStreamSingleFetchDataStrategy,\n  decodeViaTurboStream,\n  RemixErrorBoundary,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createClientRoutes,\n  shouldHydrateRouteLoader,\n  getPatchRoutesOnNavigationFunction,\n  useFogOFWarDiscovery,\n  FrameworkContext,\n  Links,\n  PrefetchPageLinks,\n  Meta,\n  Scripts,\n  createBrowserRouter,\n  createHashRouter,\n  BrowserRouter,\n  HashRouter,\n  HistoryRouter,\n  Link,\n  NavLink,\n  Form,\n  ScrollRestoration,\n  useLinkClickHandler,\n  useSearchParams,\n  useSubmit,\n  useFormAction,\n  useFetcher,\n  useFetchers,\n  useScrollRestoration,\n  useBeforeUnload,\n  usePrompt,\n  useViewTransitionState,\n  StaticRouter,\n  StaticRouterProvider,\n  createStaticHandler2 as createStaticHandler,\n  createStaticRouter,\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  deserializeErrors2 as deserializeErrors,\n  getHydrationData\n};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { createRoot } from \"react-dom\";\nimport App from \"./App.jsx\";\nimport { createHooks } from \"@wordpress/hooks\";\nimport { BrowserRouter } from \"react-router\";\nwindow.dom5_member_directory_hooks = createHooks();\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n\tconst body = document.getElementById(\"dom5-member-directory-body\");\n\tconst root = createRoot(body);\n\n\troot.render(\n\t\t<BrowserRouter>\n\t\t\t<App />\n\t\t</BrowserRouter>,\n\t);\n});\n"], "names": ["useLocation", "DefineRoutes", "useQuery", "URLSearchParams", "search", "App", "_query$get", "query", "page", "get", "createElement", "Fragment", "ContactSubmission", "Routes", "Route", "Outlet", "MemberDirectory", "Members", "Teams", "Settings", "PrimaryMenu", "__", "route_path", "pages", "label", "key", "component", "icon", "path", "element", "Layout", "index", "window", "dom5MemberDirectory", "Link", "Object", "entries", "map", "value", "className", "to", "createRoot", "createHooks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dom5_member_directory_hooks", "document", "addEventListener", "body", "getElementById", "root", "render"], "sourceRoot": ""}