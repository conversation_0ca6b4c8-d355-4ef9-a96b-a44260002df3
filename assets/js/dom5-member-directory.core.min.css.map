{"version": 3, "file": "dom5-member-directory.core.min.css", "mappings": ";;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAFJ;;AAOI;EACI;EACA;EACA;AAJR;;AASA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AANJ;AAQI;EACI;EACA;EACA;EACA;AANR;AASI;EACI;EACA;EACA;AAPR;AAUI;EACI;AARR;;AAaA;EACI;EACA;EACA;AAVJ;AAYI;EACI;EACA;AAVR;;AAcA;EACI;IAAO;EAVT;EAWE;IAAK;EARP;AACF;AAWA;EACI;EACA;EACA;AATJ;AAWI;EACI;EACA;AATR;AAWQ;EACI;EACA;EACA;AATZ;AAYQ;EACI;EACA;AAVZ;AAaQ;EACI;EACA;AAXZ;;AAiBA;EACI;EACA;EACA;EACA;EACA;AAdJ;AAgBI;EACI;EACA;EACA;EACA;AAdR;AAiBI;EACI;EACA;EACA;AAfR;AAiBQ;EACI;EACA;EACA;EACA;EACA;AAfZ;AAiBY;EACI;EACA;EACA;AAfhB;AAmBQ;EACI;EACA;EACA;EACA;EACA;EACA;AAjBZ;AAqBI;EACI;EACA;EACA;AAnBR;AAqBQ;EACI;EACA;AAnBZ;AAsBQ;EACI;EACA;EACA;EACA;AApBZ;AAwBI;EACI;EACA;EACA;EACA;EACA;AAtBR;AAyBI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AAvBR;AAyBQ;EACI;EACA;EACA;AAvBZ;AA0BQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAxBZ;AA0BY;EACI;EACA;EACA;EACA;EACA;AAxBhB;AA0BgB;EACI;AAxBpB;;AAgCA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7BJ;AA+BI;EACI;EACA;EACA;AA7BR;AAgCI;EACI;EACA;EACA;AA9BR;AAiCI;EACI;EACA;EACA;AA/BR;AAkCI;EACI;EACA;EACA;AAhCR;AAmCI;EACI;EACA;EACA;AAjCR;;AAsCA;;EAEI;EACA;EACA;EACA;AAnCJ;;AAsCA;EACI;EACA;EACA;EACA;AAnCJ;;AAuCA;;;EAGI;EACA;EACA;EACA;EACA;EACA;AApCJ;AAsCI;;;EACI;EACA;AAlCR;;AAwCI;EACI;EACA;EACA;EACA;AArCR;AAuCQ;;EAEI;EACA;EACA;EACA;EACA;EACA;AArCZ;AAuCY;;EACI;EACA;EACA;KAAA;AApChB;AAwCQ;EACI;EACA;EACA;EACA;AAtCZ;AAwCY;EACI;EACA;EACA;AAtChB;AA2CI;EACI;AAzCR;AA2CQ;EACI;EACA;EACA;EACA;AAzCZ;AA4CQ;EACI;EACA;AA1CZ;AA4CY;EACI;EACA;EACA;AA1ChB;AA4CgB;EACI;AA1CpB;AA+CQ;EACI;EACA;EACA;EACA;EACA;EACA;AA7CZ;AA+CY;EACI;EACA;AA7ChB;AAiDQ;EACI;EACA;EACA;EACA;EACA;AA/CZ;AAiDY;EACI;EACA;EACA;EACA;EACA;AA/ChB;AAmDQ;EACI;EACA;EACA;EACA;AAjDZ;AAmDY;EACI;EACA;EACA;EACA;EACA;EACA;AAjDhB;AAqDQ;EACI;EACA;AAnDZ;AAuDI;EACI;EACA;EACA;EACA;EACA;EACA;AArDR;AAuDQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AArDZ;AAuDY;EACI;AArDhB;;AA4DA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAzDJ;;AA4DA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;AAzDJ;AA2DI;;EACI;EACA;EACA;EACA;EACA;AAxDR;AA0DQ;;EACI;EACA;EACA;AAvDZ;AA0DQ;;EACI;EACA;EACA;EACA;EACA;EACA;AAvDZ;AAyDY;;EACI;AAtDhB;;AA4DA;;EAEI;AAzDJ;AA2DI;;EACI;EACA;EACA;AAxDR;AA0DQ;EALJ;;IAMQ;EAtDV;AACF;AAyDI;;EACI;AAtDR;AAwDQ;;EACI;EACA;EACA;EACA;AArDZ;AAwDQ;;;;;;EAGI;EACA;EACA;EACA;EACA;AAnDZ;AAqDY;;;;;;EACI;EACA;EACA;AA9ChB;AAiDY;;;;;;EACI;AA1ChB;AA8CQ;;EACI;EACA;EACA;EACA;AA3CZ;AAgDQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7CZ;AAgDQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7CZ;AA+CY;;EACI;AA5ChB;AA+CY;;EACI;EACA;EACA;AA5ChB;AA+CY;;EACI;EACA;EACA;AA5ChB;AA+CY;;EACI;EACA;EACA;EACA;AA5ChB;AAgDQ;;EACI;EACA;EACA;EACA;AA7CZ;AAkDQ;;EACI;EACA;EACA;AA/CZ;AAiDY;;EACI;EACA;EACA;EACA;AA9ChB;AAiDY;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA9ChB;AAgDgB;;EACI;AA7CpB;AAmDI;;EACI;EACA;EACA;EACA;EACA;EACA;AAhDR;AAkDQ;;EACI;EACA;AA/CZ;;AAsDI;EACI;EACA;AAnDR;AAqDQ;EACI;EACA;EACA;EACA;AAnDZ;AAsDQ;EACI;EACA;EACA;EACA;EACA;EACA;AApDZ;AAwDI;EACI;AAtDR;AAwDQ;EACI;EACA;EACA;AAtDZ;AAyDQ;EACI;AAvDZ;AAyDY;EACI;EACA;EACA;EACA;AAvDhB;AAyDgB;EACI;EACA;AAvDpB;AA0DgB;EACI;EACA;AAxDpB;AA6DQ;EACI;EACA;AA3DZ;AA+DI;EACI;EACA;EACA;EACA;EACA;EACA;AA7DR;AA+DQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AA7DZ;AA+DY;EACI;AA7DhB;;AAoEA;EACI;EACA;AAjEJ;AAmEI;EACI;AAjER;AAoEI;EACI;EACA;AAlER;AAqEI;EACI;AAnER;AAsEI;EACI;EACA;EACA;EACA;EACA;AApER;AAsEQ;EACI;AApEZ;AAsEY;EACI;EACA;EACA;EACA;AApEhB;AAuEY;EACI;AArEhB;AAuEgB;EACI;EACA;EACA;AArEpB;AAuEoB;EACI;AArExB;AA4EI;EACI;AA1ER;AA4EQ;EACI;EACA;AA1EZ;AA4EY;EACI;EACA;AA1EhB;AA6EY;EACI;EACA;AA3EhB;AA+EQ;EACI;EACA;EACA;EACA;AA7EZ;AAgFQ;EACI;EACA;EACA;EACA;AA9EZ;AAgFY;;EAEI;EACA;EACA;AA9EhB;AAgFgB;;EACI;AA7EpB;AAmFI;EACI;EACA;EACA;EACA;EACA;EACA;AAjFR;AAmFQ;EACI;EACA;EACA;AAjFZ;AAmFY;EACI;AAjFhB;;AAyFI;EACI;EACA;EACA;EACA;AAtFR;AAwFQ;EANJ;IAOQ;EArFV;AACF;AAwFI;EACI;EACA;EACA;AAtFR;AAyFI;EACI;EACA;EACA;AAvFR;;AA2FA;EACI;EACA;EACA;EACA;AAxFJ;AA0FI;EACI;EACA;EACA;AAxFR;AA2FI;EACI;EACA;EACA;AAzFR;AA4FI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA1FR;AA4FQ;EACI;AA1FZ;AA6FQ;EACI;AA3FZ;AA8FQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AA5FZ;AA8FY;EACI;EACA;AA5FhB;AAgGQ;EACI;AA9FZ;AAgGY;EACI;EACA;EACA;EACA;AA9FhB;AAiGY;EACI;EACA;EACA;AA/FhB;AAoGQ;EACI;AAlGZ;AAqGQ;EACI;AAnGZ;AAsGQ;EACI;AApGZ;AAuGQ;EACI;AArGZ;AAwGQ;EACI;AAtGZ;AAyGQ;EACI;AAvGZ;;AA6GA;EACI;EACA;EACA;EACA;AA1GJ;AA4GI;EACI;EACA;EACA;AA1GR;AA6GI;EACI;EACA;EACA;AA3GR;AA+GQ;EACI;EACA;EACA;EACA;EACA;EACA;AA7GZ;AA+GY;EACI;AA7GhB;AAiHQ;EACI;EACA;EACA;AA/GZ;AAiHY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA/GhB;AAiHgB;EACI;AA/GpB;AAkHgB;EACI;AAhHpB;AAkHoB;EACI;EACA;EACA;AAhHxB;AAmHoB;EACI;EACA;EACA;AAjHxB;AAoHoB;EACI;EACA;EACA;AAlHxB;AAsHgB;EACI;EACA;AApHpB;AAyHQ;EACI;EACA;EACA;EACA;AAvHZ;;AA6HA;EACI;EACA;EACA;EACA;AA1HJ;AA4HI;EACI;EACA;EACA;AA1HR;AA6HI;EACI;EACA;EACA;EACA;AA3HR;AA8HI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA5HR;AA8HQ;EACI;EACA;EACA;EACA;AA5HZ;AA+HQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AA7HZ;AA+HY;EACI;EACA;AA7HhB;AAiIQ;EACI;AA/HZ;AAiIY;EACI;EACA;EACA;AA/HhB;AAkIY;EACI;EACA;EACA;AAhIhB;AAoIQ;EACI;EACA;AAlIZ;AAsIQ;EACI;AApIZ;AAuIQ;EACI;AArIZ;AAwIQ;EACI;AAtIZ;AAyIQ;EACI;AAvIZ;AA2II;EACI;EACA;AAzIR;AA2IQ;EACI;EACA;EACA;AAzIZ;AA4IQ;EACI;EACA;EACA;AA1IZ;AA6IQ;EACI;EACA;EACA;EACA;AA3IZ;;AAiJA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA9IJ;AAgJI;EAVJ;IAWQ;IACA;EA7IN;AACF;AA+II;EACI;EACA;AA7IR;AAgJI;EACI;EACA;EACA;AA9IR;AAiJI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA/IR;AAiJQ;EACI;EACA;EACA;EACA;AA/IZ;AAkJQ;EACI;EACA;AAhJZ;AAmJQ;EACI;AAjJZ;AAqJI;EACI;EACA;AAnJR;AAsJI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AApJR;AAsJQ;EACI;EACA;AApJZ;AAuJQ;EACI;EACA;EACA;AArJZ;AAwJQ;EACI;EACA;EACA;EACA;AAtJZ;;AA6JI;EACI;EACA;EACA;EACA;EACA;AA1JR;AA4JQ;EACI;EACA;EACA;EACA;EACA;AA1JZ;AA8JI;EACI;EACA;EACA;EACA;AA5JR;AA8JQ;EACI;EACA;EACA;AA5JZ;AA+JQ;EACI;EACA;EACA;AA7JZ;AAgKQ;EACI;EACA;EACA;EACA;AA9JZ;AAgKY;EACI;EACA;AA9JhB;AAiKY;EACI;EACA;AA/JhB;;AAsKA;EACI;EACA;EACA;EACA;EAAA;EACA;EACA;AAnKJ;AAqKI;EACI;EACA;EACA;EACA;EACA;AAnKR;AAqKQ;EACI;EACA;EACA;AAnKZ;AAsKQ;EACI;EACA;EACA;EACA;EACA;EACA;AApKZ;AAsKY;EACI;AApKhB;AAyKI;EACI;EACA;EACA;AAvKR;AA0KI;EACI;AAxKR;AA0KQ;EACI;EACA;EACA;AAxKZ;AA6KY;;;;EAEI;EACA;AAzKhB;AA4KY;;;;EAEI;EACA;AAxKhB;AA0KgB;;;;EACI;EACA;AArKpB;AAuKoB;;;;EACI;AAlKxB;AAwKQ;EACI;EACA;EACA;EACA;AAtKZ;AAwKY;EACI;EACA;AAtKhB;AA2KI;EACI;AAzKR;AA2KQ;EACI;EACA;EACA;AAzKZ;AA4KQ;EACI;EACA;EACA;EACA;EACA;EACA;AA1KZ;AA4KY;EACI;AA1KhB;AA4KgB;EACI;AA1KpB;AAgLI;EACI;AA9KR;AAgLQ;EACI;EACA;EACA;AA9KZ;AAiLQ;EACI;EACA;EACA;AA/KZ;AAkLQ;EACI;EACA;EACA;EACA;EACA;AAhLZ;AAkLY;EACI;AAhLhB;AAmLY;EACI;EACA;EACA;EACA;AAjLhB;AAoLY;EACI;EACA;EACA;AAlLhB;AAoLgB;EACI;EACA;AAlLpB;AAwLI;EACI;EACA;EACA;EACA;EACA;AAtLR;AAwLQ;EACI;EACA;EACA;EACA;EACA;AAtLZ;;AA4LA;EACI;EACA;EACA;AAzLJ;AA2LI;EALJ;IAMQ;EAxLN;AACF;AA0LI;EACI;AAxLR;AA4LQ;EADJ;IAEQ;EAzLV;AACF;;AA8LA;EACI;IACI;IACA;IACA;EA3LN;EA6LM;IACI;EA3LV;EA+LE;;IAEI;EA7LN;EAgME;IACI;EA9LN;EAiME;IACI;EA/LN;EAoMM;;IACI;EAjMV;AACF,C", "sources": ["webpack://dom5-member-directory/./assets/scss/dom5-member-directory.scss"], "sourcesContent": ["// DOM5 Member Directory Styles\n\n// Variables\n:root {\n    --dom5md-primary: #0073aa;\n    --dom5md-secondary: #005177;\n    --dom5md-success: #46b450;\n    --dom5md-warning: #ffb900;\n    --dom5md-error: #dc3232;\n    --dom5md-text: #23282d;\n    --dom5md-text-light: #646970;\n    --dom5md-border: #c3c4c7;\n    --dom5md-bg: #f6f7f7;\n    --dom5md-white: #ffffff;\n    --dom5md-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    --dom5md-shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.15);\n    --dom5md-radius: 4px;\n    --dom5md-spacing: 16px;\n}\n\n// Base styles\n.dom5md-admin-page {\n    .dom5-member-directory-body {\n        font-family: -apple-system, BlinkMacSystemFont, \"Se<PERSON><PERSON> UI\", <PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Can<PERSON>ell, \"Helvetica Neue\", sans-serif;\n        color: var(--dom5md-text);\n        line-height: 1.5;\n    }\n}\n\n// Page header\n.dom5md-page-header {\n    margin-bottom: var(--dom5md-spacing);\n    padding-bottom: var(--dom5md-spacing);\n    border-bottom: 1px solid var(--dom5md-border);\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    flex-wrap: wrap;\n    gap: var(--dom5md-spacing);\n\n    h1 {\n        margin: 0;\n        font-size: 1.75rem;\n        font-weight: 600;\n        color: var(--dom5md-text);\n    }\n\n    .page-description {\n        color: var(--dom5md-text-light);\n        margin: 8px 0 0 0;\n        font-size: 0.9rem;\n    }\n\n    .button {\n        flex-shrink: 0;\n    }\n}\n\n// Loading states\n.dom5md-loading {\n    text-align: center;\n    padding: 2rem;\n    color: var(--dom5md-text-light);\n\n    .dashicons {\n        animation: spin 1s linear infinite;\n        margin-right: 8px;\n    }\n}\n\n@keyframes spin {\n    from { transform: rotate(0deg); }\n    to { transform: rotate(360deg); }\n}\n\n// No results\n.dom5md-no-results {\n    text-align: center;\n    padding: 3rem 1rem;\n    color: var(--dom5md-text-light);\n\n    .no-results-content {\n        max-width: 400px;\n        margin: 0 auto;\n\n        .dashicons {\n            font-size: 3rem;\n            opacity: 0.5;\n            margin-bottom: 1rem;\n        }\n\n        h3 {\n            margin: 0 0 0.5rem 0;\n            color: var(--dom5md-text);\n        }\n\n        p {\n            margin: 0;\n            font-size: 0.9rem;\n        }\n    }\n}\n\n// Search and filters\n.dom5md-search-filter {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    padding: var(--dom5md-spacing);\n    margin-bottom: var(--dom5md-spacing);\n\n    .filter-row {\n        display: flex;\n        gap: var(--dom5md-spacing);\n        align-items: center;\n        flex-wrap: wrap;\n    }\n\n    .search-group {\n        position: relative;\n        flex: 1;\n        min-width: 250px;\n\n        .search-input {\n            width: 100%;\n            padding: 8px 12px 8px 36px;\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            font-size: 14px;\n\n            &:focus {\n                border-color: var(--dom5md-primary);\n                box-shadow: 0 0 0 1px var(--dom5md-primary);\n                outline: none;\n            }\n        }\n\n        .search-icon {\n            position: absolute;\n            left: 12px;\n            top: 50%;\n            transform: translateY(-50%);\n            color: var(--dom5md-text-light);\n            pointer-events: none;\n        }\n    }\n\n    .filter-group {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n\n        label {\n            font-weight: 500;\n            white-space: nowrap;\n        }\n\n        select {\n            padding: 6px 8px;\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            font-size: 14px;\n        }\n    }\n\n    .clear-filters {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        padding: 6px 12px;\n        font-size: 13px;\n    }\n\n    .active-filters {\n        margin-top: var(--dom5md-spacing);\n        padding-top: var(--dom5md-spacing);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        flex-wrap: wrap;\n\n        .filters-label {\n            font-weight: 500;\n            color: var(--dom5md-text-light);\n            font-size: 13px;\n        }\n\n        .filter-tag {\n            background: var(--dom5md-bg);\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            padding: 4px 8px;\n            font-size: 12px;\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .remove-filter {\n                background: none;\n                border: none;\n                padding: 0;\n                cursor: pointer;\n                color: var(--dom5md-text-light);\n\n                &:hover {\n                    color: var(--dom5md-error);\n                }\n            }\n        }\n    }\n}\n\n// Status badges\n.dom5md-status-badge {\n    display: inline-flex;\n    align-items: center;\n    gap: 4px;\n    padding: 4px 8px;\n    border-radius: var(--dom5md-radius);\n    font-size: 12px;\n    font-weight: 500;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n\n    &.status-active {\n        background: #d4edda;\n        color: #155724;\n        border: 1px solid #c3e6cb;\n    }\n\n    &.status-draft {\n        background: #fff3cd;\n        color: #856404;\n        border: 1px solid #ffeaa7;\n    }\n\n    &.status-unread {\n        background: #f8d7da;\n        color: #721c24;\n        border: 1px solid #f5c6cb;\n    }\n\n    &.status-read {\n        background: #d1ecf1;\n        color: #0c5460;\n        border: 1px solid #bee5eb;\n    }\n\n    &.status-replied {\n        background: #d4edda;\n        color: #155724;\n        border: 1px solid #c3e6cb;\n    }\n}\n\n// Grid layouts\n.dom5md-members-grid,\n.dom5md-teams-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: var(--dom5md-spacing);\n    margin-bottom: var(--dom5md-spacing);\n}\n\n.dom5md-submissions-grid {\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 12px;\n    margin-bottom: var(--dom5md-spacing);\n}\n\n// Card components\n.dom5md-member-card,\n.dom5md-team-card,\n.dom5md-submission-card {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    box-shadow: var(--dom5md-shadow);\n    transition: all 0.2s ease;\n    overflow: hidden;\n\n    &:hover {\n        box-shadow: var(--dom5md-shadow-hover);\n        transform: translateY(-1px);\n    }\n}\n\n// Member card specific styles\n.dom5md-member-card {\n    .member-card-header {\n        position: relative;\n        padding: var(--dom5md-spacing);\n        text-align: center;\n        border-bottom: 1px solid var(--dom5md-border);\n\n        .member-avatar,\n        .member-avatar-placeholder {\n            width: 80px;\n            height: 80px;\n            border-radius: 50%;\n            margin: 0 auto 12px;\n            overflow: hidden;\n            border: 3px solid var(--dom5md-border);\n\n            img {\n                width: 100%;\n                height: 100%;\n                object-fit: cover;\n            }\n        }\n\n        .member-avatar-placeholder {\n            background: var(--dom5md-bg);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            .avatar-initials {\n                font-size: 24px;\n                font-weight: 600;\n                color: var(--dom5md-text-light);\n            }\n        }\n    }\n\n    .member-card-body {\n        padding: var(--dom5md-spacing);\n\n        .member-name {\n            margin: 0 0 8px 0;\n            font-size: 1.1rem;\n            font-weight: 600;\n            text-align: center;\n        }\n\n        .member-email {\n            text-align: center;\n            margin: 0 0 12px 0;\n\n            a {\n                color: var(--dom5md-primary);\n                text-decoration: none;\n                font-size: 0.9rem;\n\n                &:hover {\n                    text-decoration: underline;\n                }\n            }\n        }\n\n        .member-address {\n            display: flex;\n            align-items: flex-start;\n            gap: 6px;\n            margin: 0 0 8px 0;\n            font-size: 0.9rem;\n            color: var(--dom5md-text-light);\n\n            .dashicons {\n                margin-top: 2px;\n                flex-shrink: 0;\n            }\n        }\n\n        .member-color {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            margin: 0 0 12px 0;\n            font-size: 0.9rem;\n\n            .color-swatch {\n                width: 20px;\n                height: 20px;\n                border-radius: 50%;\n                border: 2px solid var(--dom5md-border);\n                flex-shrink: 0;\n            }\n        }\n\n        .member-meta {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin: 0 0 8px 0;\n\n            .member-slug {\n                font-family: monospace;\n                font-size: 0.8rem;\n                color: var(--dom5md-text-light);\n                background: var(--dom5md-bg);\n                padding: 2px 6px;\n                border-radius: var(--dom5md-radius);\n            }\n        }\n\n        .member-dates {\n            font-size: 0.8rem;\n            color: var(--dom5md-text-light);\n        }\n    }\n\n    .member-card-actions {\n        padding: 12px var(--dom5md-spacing);\n        background: var(--dom5md-bg);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        gap: 8px;\n        justify-content: space-between;\n\n        .button {\n            flex: 1;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 4px;\n            padding: 6px 12px;\n            font-size: 12px;\n            text-decoration: none;\n\n            .dashicons {\n                font-size: 14px;\n            }\n        }\n    }\n}\n\n// Form components\n.dom5md-member-form-overlay,\n.dom5md-team-form-overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.5);\n    z-index: 100000;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 2rem;\n}\n\n.dom5md-member-form-modal,\n.dom5md-team-form-modal {\n    background: var(--dom5md-white);\n    border-radius: var(--dom5md-radius);\n    box-shadow: var(--dom5md-shadow-hover);\n    max-width: 800px;\n    width: 100%;\n    max-height: 90vh;\n    overflow-y: auto;\n\n    .modal-header {\n        padding: var(--dom5md-spacing);\n        border-bottom: 1px solid var(--dom5md-border);\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n\n        h2 {\n            margin: 0;\n            font-size: 1.5rem;\n            color: var(--dom5md-text);\n        }\n\n        .modal-close {\n            background: none;\n            border: none;\n            font-size: 1.5rem;\n            cursor: pointer;\n            color: var(--dom5md-text-light);\n            padding: 4px;\n\n            &:hover {\n                color: var(--dom5md-error);\n            }\n        }\n    }\n}\n\n.dom5md-member-form,\n.dom5md-team-form {\n    padding: var(--dom5md-spacing);\n\n    .form-row {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: var(--dom5md-spacing);\n\n        @media (max-width: 768px) {\n            grid-template-columns: 1fr;\n        }\n    }\n\n    .form-group {\n        margin-bottom: var(--dom5md-spacing);\n\n        label {\n            display: block;\n            margin-bottom: 6px;\n            font-weight: 500;\n            color: var(--dom5md-text);\n        }\n\n        input,\n        textarea,\n        select {\n            width: 100%;\n            padding: 8px 12px;\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            font-size: 14px;\n\n            &:focus {\n                border-color: var(--dom5md-primary);\n                box-shadow: 0 0 0 1px var(--dom5md-primary);\n                outline: none;\n            }\n\n            &.error {\n                border-color: var(--dom5md-error);\n            }\n        }\n\n        .error-message {\n            display: block;\n            color: var(--dom5md-error);\n            font-size: 12px;\n            margin-top: 4px;\n        }\n    }\n\n    .teams-selection {\n        .teams-checkboxes {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 12px;\n            max-height: 200px;\n            overflow-y: auto;\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            padding: 12px;\n        }\n\n        .team-checkbox {\n            display: flex;\n            align-items: flex-start;\n            gap: 8px;\n            padding: 8px;\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            cursor: pointer;\n            transition: background-color 0.2s ease;\n\n            &:hover {\n                background: var(--dom5md-bg);\n            }\n\n            input[type=\"checkbox\"] {\n                width: auto;\n                margin: 0;\n                flex-shrink: 0;\n            }\n\n            .team-name {\n                font-weight: 500;\n                color: var(--dom5md-text);\n                display: block;\n            }\n\n            .team-description {\n                font-size: 12px;\n                color: var(--dom5md-text-light);\n                display: block;\n                margin-top: 2px;\n            }\n        }\n\n        .no-teams {\n            color: var(--dom5md-text-light);\n            font-style: italic;\n            text-align: center;\n            padding: 2rem;\n        }\n    }\n\n    .image-upload-field {\n        .image-preview {\n            position: relative;\n            display: inline-block;\n            margin-bottom: 8px;\n\n            img {\n                max-width: 150px;\n                max-height: 150px;\n                border-radius: var(--dom5md-radius);\n                border: 1px solid var(--dom5md-border);\n            }\n\n            .remove-image {\n                position: absolute;\n                top: -8px;\n                right: -8px;\n                background: var(--dom5md-error);\n                color: var(--dom5md-white);\n                border: none;\n                border-radius: 50%;\n                width: 24px;\n                height: 24px;\n                cursor: pointer;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                &:hover {\n                    background: #b32d2d;\n                }\n            }\n        }\n    }\n\n    .form-actions {\n        margin-top: 2rem;\n        padding-top: var(--dom5md-spacing);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        gap: 12px;\n        justify-content: flex-end;\n\n        .button {\n            padding: 10px 20px;\n            font-size: 14px;\n        }\n    }\n}\n\n// Team card specific styles\n.dom5md-team-card {\n    .team-card-header {\n        padding: var(--dom5md-spacing);\n        border-bottom: 1px solid var(--dom5md-border);\n\n        .team-name {\n            margin: 0 0 8px 0;\n            font-size: 1.2rem;\n            font-weight: 600;\n            color: var(--dom5md-text);\n        }\n\n        .team-slug {\n            font-family: monospace;\n            font-size: 0.8rem;\n            color: var(--dom5md-text-light);\n            background: var(--dom5md-bg);\n            padding: 2px 6px;\n            border-radius: var(--dom5md-radius);\n        }\n    }\n\n    .team-card-body {\n        padding: var(--dom5md-spacing);\n\n        .team-description {\n            color: var(--dom5md-text-light);\n            line-height: 1.5;\n            margin: 0 0 12px 0;\n        }\n\n        .team-stats {\n            margin-bottom: 12px;\n\n            .stat-item {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                padding: 4px 0;\n\n                .stat-label {\n                    font-weight: 500;\n                    color: var(--dom5md-text);\n                }\n\n                .stat-value {\n                    font-weight: 600;\n                    color: var(--dom5md-primary);\n                }\n            }\n        }\n\n        .team-meta {\n            font-size: 0.8rem;\n            color: var(--dom5md-text-light);\n        }\n    }\n\n    .team-card-actions {\n        padding: 12px var(--dom5md-spacing);\n        background: var(--dom5md-bg);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        gap: 8px;\n        justify-content: space-between;\n\n        .button {\n            flex: 1;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 4px;\n            padding: 6px 12px;\n            font-size: 12px;\n\n            .dashicons {\n                font-size: 14px;\n            }\n        }\n    }\n}\n\n// Submission card specific styles\n.dom5md-submission-card {\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n        border-color: var(--dom5md-primary);\n    }\n\n    &.selected {\n        border-color: var(--dom5md-primary);\n        box-shadow: 0 0 0 1px var(--dom5md-primary);\n    }\n\n    &.unread {\n        border-left: 4px solid var(--dom5md-error);\n    }\n\n    .submission-card-header {\n        padding: var(--dom5md-spacing);\n        border-bottom: 1px solid var(--dom5md-border);\n        display: flex;\n        justify-content: space-between;\n        align-items: flex-start;\n\n        .sender-info {\n            flex: 1;\n\n            .sender-name {\n                margin: 0 0 4px 0;\n                font-size: 1.1rem;\n                font-weight: 600;\n                color: var(--dom5md-text);\n            }\n\n            .sender-email {\n                margin: 0;\n\n                a {\n                    color: var(--dom5md-primary);\n                    text-decoration: none;\n                    font-size: 0.9rem;\n\n                    &:hover {\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n    }\n\n    .submission-card-body {\n        padding: var(--dom5md-spacing);\n\n        .recipient-info {\n            margin-bottom: 12px;\n            font-size: 0.9rem;\n\n            .recipient-label {\n                color: var(--dom5md-text-light);\n                margin-right: 4px;\n            }\n\n            .recipient-name {\n                font-weight: 500;\n                color: var(--dom5md-text);\n            }\n        }\n\n        .message-preview {\n            color: var(--dom5md-text-light);\n            line-height: 1.5;\n            margin-bottom: 12px;\n            font-size: 0.9rem;\n        }\n\n        .submission-meta {\n            display: flex;\n            gap: 16px;\n            font-size: 0.8rem;\n            color: var(--dom5md-text-light);\n\n            .submission-date,\n            .submission-ip {\n                display: flex;\n                align-items: center;\n                gap: 4px;\n\n                .dashicons {\n                    font-size: 12px;\n                }\n            }\n        }\n    }\n\n    .submission-card-actions {\n        padding: 8px var(--dom5md-spacing);\n        background: var(--dom5md-bg);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        gap: 4px;\n        justify-content: flex-end;\n\n        .button {\n            padding: 4px 8px;\n            font-size: 12px;\n            min-width: auto;\n\n            .dashicons {\n                font-size: 14px;\n            }\n        }\n    }\n}\n\n// Dashboard specific styles\n.dom5md-dashboard {\n    .dashboard-content {\n        display: grid;\n        grid-template-columns: 2fr 1fr;\n        gap: 2rem;\n        margin-top: var(--dom5md-spacing);\n\n        @media (max-width: 1024px) {\n            grid-template-columns: 1fr;\n        }\n    }\n\n    .dashboard-main {\n        display: flex;\n        flex-direction: column;\n        gap: 2rem;\n    }\n\n    .dashboard-sidebar {\n        display: flex;\n        flex-direction: column;\n        gap: 1.5rem;\n    }\n}\n\n.dom5md-dashboard-stats {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    padding: var(--dom5md-spacing);\n\n    h2 {\n        margin: 0 0 var(--dom5md-spacing) 0;\n        font-size: 1.3rem;\n        color: var(--dom5md-text);\n    }\n\n    .stats-grid {\n        display: grid;\n        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n        gap: var(--dom5md-spacing);\n    }\n\n    .stat-card {\n        background: var(--dom5md-white);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        padding: var(--dom5md-spacing);\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        transition: all 0.2s ease;\n\n        &:hover {\n            box-shadow: var(--dom5md-shadow-hover);\n        }\n\n        &.loading {\n            opacity: 0.6;\n        }\n\n        .stat-icon {\n            width: 48px;\n            height: 48px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n\n            .dashicons {\n                font-size: 24px;\n                color: var(--dom5md-white);\n            }\n        }\n\n        .stat-content {\n            flex: 1;\n\n            .stat-value {\n                font-size: 1.5rem;\n                font-weight: 600;\n                color: var(--dom5md-text);\n                margin: 0 0 4px 0;\n            }\n\n            .stat-label {\n                font-size: 0.9rem;\n                color: var(--dom5md-text-light);\n                margin: 0;\n            }\n        }\n\n        // Color variations\n        &.blue .stat-icon {\n            background: var(--dom5md-primary);\n        }\n\n        &.green .stat-icon {\n            background: var(--dom5md-success);\n        }\n\n        &.orange .stat-icon {\n            background: var(--dom5md-warning);\n        }\n\n        &.purple .stat-icon {\n            background: #8e44ad;\n        }\n\n        &.teal .stat-icon {\n            background: #16a085;\n        }\n\n        &.red .stat-icon {\n            background: var(--dom5md-error);\n        }\n    }\n}\n\n// Recent activity styles\n.dom5md-recent-activity {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    padding: var(--dom5md-spacing);\n\n    h2 {\n        margin: 0 0 var(--dom5md-spacing) 0;\n        font-size: 1.3rem;\n        color: var(--dom5md-text);\n    }\n\n    .activity-sections {\n        display: flex;\n        flex-direction: column;\n        gap: 1.5rem;\n    }\n\n    .activity-section {\n        h3 {\n            margin: 0 0 12px 0;\n            font-size: 1.1rem;\n            color: var(--dom5md-text);\n            display: flex;\n            align-items: center;\n            gap: 8px;\n\n            .dashicons {\n                color: var(--dom5md-primary);\n            }\n        }\n\n        .activity-list {\n            list-style: none;\n            margin: 0;\n            padding: 0;\n\n            .activity-item {\n                display: flex;\n                justify-content: space-between;\n                align-items: flex-start;\n                padding: 12px;\n                border: 1px solid var(--dom5md-border);\n                border-radius: var(--dom5md-radius);\n                margin-bottom: 8px;\n                transition: background-color 0.2s ease;\n\n                &:hover {\n                    background: var(--dom5md-bg);\n                }\n\n                .activity-content {\n                    flex: 1;\n\n                    strong {\n                        color: var(--dom5md-text);\n                        display: block;\n                        margin-bottom: 4px;\n                    }\n\n                    .activity-meta {\n                        font-size: 0.8rem;\n                        color: var(--dom5md-text-light);\n                        margin-bottom: 4px;\n                    }\n\n                    .submission-preview {\n                        font-size: 0.9rem;\n                        color: var(--dom5md-text-light);\n                        line-height: 1.4;\n                    }\n                }\n\n                .status-badge {\n                    flex-shrink: 0;\n                    margin-left: 12px;\n                }\n            }\n        }\n\n        .no-activity {\n            text-align: center;\n            color: var(--dom5md-text-light);\n            font-style: italic;\n            padding: 2rem;\n        }\n    }\n}\n\n// Quick actions styles\n.dom5md-quick-actions {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    padding: var(--dom5md-spacing);\n\n    h2 {\n        margin: 0 0 var(--dom5md-spacing) 0;\n        font-size: 1.3rem;\n        color: var(--dom5md-text);\n    }\n\n    .actions-list {\n        display: flex;\n        flex-direction: column;\n        gap: 12px;\n        margin-bottom: 2rem;\n    }\n\n    .action-card {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        padding: 12px;\n        background: var(--dom5md-white);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        cursor: pointer;\n        transition: all 0.2s ease;\n        text-decoration: none;\n        color: inherit;\n\n        &:hover {\n            box-shadow: var(--dom5md-shadow-hover);\n            transform: translateY(-1px);\n            text-decoration: none;\n            color: inherit;\n        }\n\n        .action-icon {\n            width: 40px;\n            height: 40px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n\n            .dashicons {\n                font-size: 20px;\n                color: var(--dom5md-white);\n            }\n        }\n\n        .action-content {\n            flex: 1;\n\n            h3 {\n                margin: 0 0 4px 0;\n                font-size: 1rem;\n                color: var(--dom5md-text);\n            }\n\n            p {\n                margin: 0;\n                font-size: 0.8rem;\n                color: var(--dom5md-text-light);\n            }\n        }\n\n        .action-arrow {\n            color: var(--dom5md-text-light);\n            flex-shrink: 0;\n        }\n\n        // Color variations\n        &.blue .action-icon {\n            background: var(--dom5md-primary);\n        }\n\n        &.green .action-icon {\n            background: var(--dom5md-success);\n        }\n\n        &.orange .action-icon {\n            background: var(--dom5md-warning);\n        }\n\n        &.purple .action-icon {\n            background: #8e44ad;\n        }\n    }\n\n    .help-section {\n        border-top: 1px solid var(--dom5md-border);\n        padding-top: var(--dom5md-spacing);\n\n        h3 {\n            margin: 0 0 8px 0;\n            font-size: 1.1rem;\n            color: var(--dom5md-text);\n        }\n\n        p {\n            margin: 0 0 12px 0;\n            color: var(--dom5md-text-light);\n            font-size: 0.9rem;\n        }\n\n        .button {\n            display: inline-flex;\n            align-items: center;\n            gap: 6px;\n            text-decoration: none;\n        }\n    }\n}\n\n// Pagination styles\n.dom5md-pagination {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-top: 2rem;\n    padding: var(--dom5md-spacing);\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n\n    @media (max-width: 768px) {\n        flex-direction: column;\n        gap: 1rem;\n    }\n\n    .pagination-info {\n        color: var(--dom5md-text-light);\n        font-size: 0.9rem;\n    }\n\n    .pagination-controls {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n    }\n\n    .pagination-btn {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        padding: 6px 12px;\n        background: var(--dom5md-white);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        color: var(--dom5md-text);\n        text-decoration: none;\n        font-size: 0.9rem;\n        transition: all 0.2s ease;\n        cursor: pointer;\n\n        &:hover:not(:disabled) {\n            background: var(--dom5md-primary);\n            color: var(--dom5md-white);\n            border-color: var(--dom5md-primary);\n            text-decoration: none;\n        }\n\n        &:disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n        }\n\n        .dashicons {\n            font-size: 16px;\n        }\n    }\n\n    .page-numbers {\n        display: flex;\n        gap: 4px;\n    }\n\n    .page-number {\n        padding: 6px 10px;\n        background: var(--dom5md-white);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        color: var(--dom5md-text);\n        text-decoration: none;\n        font-size: 0.9rem;\n        transition: all 0.2s ease;\n        cursor: pointer;\n        min-width: 32px;\n        text-align: center;\n\n        &:hover:not(.current):not(.ellipsis) {\n            background: var(--dom5md-bg);\n            text-decoration: none;\n        }\n\n        &.current {\n            background: var(--dom5md-primary);\n            color: var(--dom5md-white);\n            border-color: var(--dom5md-primary);\n        }\n\n        &.ellipsis {\n            border: none;\n            background: none;\n            cursor: default;\n            color: var(--dom5md-text-light);\n        }\n    }\n}\n\n// Settings page styles\n.dom5md-settings-page {\n    .settings-section {\n        background: var(--dom5md-white);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        padding: var(--dom5md-spacing);\n        margin-bottom: var(--dom5md-spacing);\n\n        h2 {\n            margin: 0 0 var(--dom5md-spacing) 0;\n            font-size: 1.3rem;\n            color: var(--dom5md-text);\n            border-bottom: 1px solid var(--dom5md-border);\n            padding-bottom: 8px;\n        }\n    }\n\n    .settings-info {\n        background: var(--dom5md-bg);\n        border: 1px solid var(--dom5md-border);\n        border-radius: var(--dom5md-radius);\n        padding: var(--dom5md-spacing);\n\n        h2 {\n            margin: 0 0 var(--dom5md-spacing) 0;\n            font-size: 1.2rem;\n            color: var(--dom5md-text);\n        }\n\n        .info-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: var(--dom5md-spacing);\n        }\n\n        .info-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 8px 0;\n\n            label {\n                font-weight: 500;\n                color: var(--dom5md-text);\n            }\n\n            span {\n                color: var(--dom5md-text-light);\n                font-family: monospace;\n            }\n        }\n    }\n}\n\n// Submission detail styles\n.dom5md-submission-detail {\n    background: var(--dom5md-white);\n    border: 1px solid var(--dom5md-border);\n    border-radius: var(--dom5md-radius);\n    height: fit-content;\n    position: sticky;\n    top: 2rem;\n\n    .detail-header {\n        padding: var(--dom5md-spacing);\n        border-bottom: 1px solid var(--dom5md-border);\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n\n        h2 {\n            margin: 0;\n            font-size: 1.3rem;\n            color: var(--dom5md-text);\n        }\n\n        .close-button {\n            background: none;\n            border: none;\n            font-size: 1.5rem;\n            cursor: pointer;\n            color: var(--dom5md-text-light);\n            padding: 4px;\n\n            &:hover {\n                color: var(--dom5md-error);\n            }\n        }\n    }\n\n    .detail-content {\n        padding: var(--dom5md-spacing);\n        max-height: 70vh;\n        overflow-y: auto;\n    }\n\n    .info-section {\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0 0 8px 0;\n            font-size: 1rem;\n            color: var(--dom5md-text);\n        }\n\n        .sender-details,\n        .recipient-details {\n            .sender-name,\n            .recipient-name {\n                margin: 0 0 4px 0;\n                font-weight: 600;\n            }\n\n            .sender-email,\n            .recipient-email {\n                margin: 0;\n                color: var(--dom5md-text-light);\n\n                a {\n                    color: var(--dom5md-primary);\n                    text-decoration: none;\n\n                    &:hover {\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n\n        .status-section {\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            flex-wrap: wrap;\n\n            .status-actions {\n                display: flex;\n                gap: 8px;\n            }\n        }\n    }\n\n    .message-section {\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0 0 12px 0;\n            font-size: 1rem;\n            color: var(--dom5md-text);\n        }\n\n        .message-content {\n            background: var(--dom5md-bg);\n            border: 1px solid var(--dom5md-border);\n            border-radius: var(--dom5md-radius);\n            padding: 12px;\n            line-height: 1.6;\n            color: var(--dom5md-text);\n\n            p {\n                margin: 0 0 8px 0;\n\n                &:last-child {\n                    margin-bottom: 0;\n                }\n            }\n        }\n    }\n\n    .metadata-section {\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0 0 12px 0;\n            font-size: 1rem;\n            color: var(--dom5md-text);\n        }\n\n        .metadata-grid {\n            display: flex;\n            flex-direction: column;\n            gap: 8px;\n        }\n\n        .metadata-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: flex-start;\n            padding: 6px 0;\n            border-bottom: 1px solid var(--dom5md-border);\n\n            &:last-child {\n                border-bottom: none;\n            }\n\n            label {\n                font-weight: 500;\n                color: var(--dom5md-text);\n                margin-right: 12px;\n                flex-shrink: 0;\n            }\n\n            span {\n                color: var(--dom5md-text-light);\n                text-align: right;\n                word-break: break-word;\n\n                &.user-agent {\n                    font-size: 0.8rem;\n                    font-family: monospace;\n                }\n            }\n        }\n    }\n\n    .detail-actions {\n        padding: var(--dom5md-spacing);\n        border-top: 1px solid var(--dom5md-border);\n        display: flex;\n        gap: 8px;\n        flex-wrap: wrap;\n\n        .button {\n            display: flex;\n            align-items: center;\n            gap: 6px;\n            padding: 8px 12px;\n            font-size: 0.9rem;\n        }\n    }\n}\n\n// Submissions layout\n.submissions-layout {\n    display: grid;\n    grid-template-columns: 1fr 400px;\n    gap: 2rem;\n\n    @media (max-width: 1200px) {\n        grid-template-columns: 1fr;\n    }\n\n    .submissions-list-container {\n        min-width: 0; // Prevent grid overflow\n    }\n\n    .submission-detail-container {\n        @media (max-width: 1200px) {\n            order: -1;\n        }\n    }\n}\n\n// Responsive adjustments\n@media (max-width: 768px) {\n    .dom5md-page-header {\n        flex-direction: column;\n        align-items: stretch;\n        text-align: center;\n\n        .button {\n            align-self: center;\n        }\n    }\n\n    .dom5md-members-grid,\n    .dom5md-teams-grid {\n        grid-template-columns: 1fr;\n    }\n\n    .dashboard-content {\n        grid-template-columns: 1fr;\n    }\n\n    .submissions-layout {\n        grid-template-columns: 1fr;\n    }\n\n    .dom5md-member-form,\n    .dom5md-team-form {\n        .form-row {\n            grid-template-columns: 1fr;\n        }\n    }\n}\n"], "names": [], "sourceRoot": ""}