/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./assets/scss/dom5-member-directory.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
:root {
  --dom5md-primary: #0073aa;
  --dom5md-secondary: #005177;
  --dom5md-success: #46b450;
  --dom5md-warning: #ffb900;
  --dom5md-error: #dc3232;
  --dom5md-text: #23282d;
  --dom5md-text-light: #646970;
  --dom5md-border: #c3c4c7;
  --dom5md-bg: #f6f7f7;
  --dom5md-white: #ffffff;
  --dom5md-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --dom5md-shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.15);
  --dom5md-radius: 4px;
  --dom5md-spacing: 16px;
}

.dom5md-admin-page .dom5-member-directory-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: var(--dom5md-text);
  line-height: 1.5;
}

.dom5md-page-header {
  margin-bottom: var(--dom5md-spacing);
  padding-bottom: var(--dom5md-spacing);
  border-bottom: 1px solid var(--dom5md-border);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: var(--dom5md-spacing);
}
.dom5md-page-header h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--dom5md-text);
}
.dom5md-page-header .page-description {
  color: var(--dom5md-text-light);
  margin: 8px 0 0 0;
  font-size: 0.9rem;
}
.dom5md-page-header .button {
  flex-shrink: 0;
}

.dom5md-loading {
  text-align: center;
  padding: 2rem;
  color: var(--dom5md-text-light);
}
.dom5md-loading .dashicons {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.dom5md-no-results {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--dom5md-text-light);
}
.dom5md-no-results .no-results-content {
  max-width: 400px;
  margin: 0 auto;
}
.dom5md-no-results .no-results-content .dashicons {
  font-size: 3rem;
  opacity: 0.5;
  margin-bottom: 1rem;
}
.dom5md-no-results .no-results-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--dom5md-text);
}
.dom5md-no-results .no-results-content p {
  margin: 0;
  font-size: 0.9rem;
}

.dom5md-search-filter {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
  margin-bottom: var(--dom5md-spacing);
}
.dom5md-search-filter .filter-row {
  display: flex;
  gap: var(--dom5md-spacing);
  align-items: center;
  flex-wrap: wrap;
}
.dom5md-search-filter .search-group {
  position: relative;
  flex: 1;
  min-width: 250px;
}
.dom5md-search-filter .search-group .search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  font-size: 14px;
}
.dom5md-search-filter .search-group .search-input:focus {
  border-color: var(--dom5md-primary);
  box-shadow: 0 0 0 1px var(--dom5md-primary);
  outline: none;
}
.dom5md-search-filter .search-group .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dom5md-text-light);
  pointer-events: none;
}
.dom5md-search-filter .filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 7px 7px 7px;
}
.dom5md-search-filter .filter-group label {
  font-weight: 500;
  white-space: nowrap;
}
.dom5md-search-filter .filter-group select {
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  font-size: 14px;
}
.dom5md-search-filter .clear-filters {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 13px;
}
.dom5md-search-filter .active-filters {
  margin-top: var(--dom5md-spacing);
  padding-top: var(--dom5md-spacing);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.dom5md-search-filter .active-filters .filters-label {
  font-weight: 500;
  color: var(--dom5md-text-light);
  font-size: 13px;
}
.dom5md-search-filter .active-filters .filter-tag {
  background: var(--dom5md-bg);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: 4px 8px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.dom5md-search-filter .active-filters .filter-tag .remove-filter {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: var(--dom5md-text-light);
}
.dom5md-search-filter .active-filters .filter-tag .remove-filter:hover {
  color: var(--dom5md-error);
}

.dom5md-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: var(--dom5md-radius);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.dom5md-status-badge.status-active {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.dom5md-status-badge.status-draft {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}
.dom5md-status-badge.status-unread {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.dom5md-status-badge.status-read {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
.dom5md-status-badge.status-replied {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.dom5md-members-grid,
.dom5md-teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--dom5md-spacing);
  margin-bottom: var(--dom5md-spacing);
}

.dom5md-submissions-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: var(--dom5md-spacing);
}

.dom5md-member-card,
.dom5md-team-card,
.dom5md-submission-card {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  box-shadow: var(--dom5md-shadow);
  transition: all 0.2s ease;
  overflow: hidden;
}
.dom5md-member-card:hover,
.dom5md-team-card:hover,
.dom5md-submission-card:hover {
  box-shadow: var(--dom5md-shadow-hover);
  transform: translateY(-1px);
}

.dom5md-member-card .member-card-header {
  position: relative;
  padding: var(--dom5md-spacing);
  text-align: center;
  border-bottom: 1px solid var(--dom5md-border);
}
.dom5md-member-card .member-card-header .member-avatar,
.dom5md-member-card .member-card-header .member-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 12px;
  overflow: hidden;
  border: 3px solid var(--dom5md-border);
}
.dom5md-member-card .member-card-header .member-avatar img,
.dom5md-member-card .member-card-header .member-avatar-placeholder img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.dom5md-member-card .member-card-header .member-avatar-placeholder {
  background: var(--dom5md-bg);
  display: flex;
  align-items: center;
  justify-content: center;
}
.dom5md-member-card .member-card-header .member-avatar-placeholder .avatar-initials {
  font-size: 24px;
  font-weight: 600;
  color: var(--dom5md-text-light);
}
.dom5md-member-card .member-card-body {
  padding: var(--dom5md-spacing);
}
.dom5md-member-card .member-card-body .member-name {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
}
.dom5md-member-card .member-card-body .member-email {
  text-align: center;
  margin: 0 0 12px 0;
}
.dom5md-member-card .member-card-body .member-email a {
  color: var(--dom5md-primary);
  text-decoration: none;
  font-size: 0.9rem;
}
.dom5md-member-card .member-card-body .member-email a:hover {
  text-decoration: underline;
}
.dom5md-member-card .member-card-body .member-address {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: var(--dom5md-text-light);
}
.dom5md-member-card .member-card-body .member-address .dashicons {
  margin-top: 2px;
  flex-shrink: 0;
}
.dom5md-member-card .member-card-body .member-color {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 0.9rem;
}
.dom5md-member-card .member-card-body .member-color .color-swatch {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--dom5md-border);
  flex-shrink: 0;
}
.dom5md-member-card .member-card-body .member-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 8px 0;
}
.dom5md-member-card .member-card-body .member-meta .member-slug {
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
  background: var(--dom5md-bg);
  padding: 2px 6px;
  border-radius: var(--dom5md-radius);
}
.dom5md-member-card .member-card-body .member-dates {
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
}
.dom5md-member-card .member-card-actions {
  padding: 12px var(--dom5md-spacing);
  background: var(--dom5md-bg);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  gap: 8px;
  justify-content: space-between;
}
.dom5md-member-card .member-card-actions .button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 12px;
  text-decoration: none;
}
.dom5md-member-card .member-card-actions .button .dashicons {
  font-size: 14px;
}

.dom5md-member-form-overlay,
.dom5md-team-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.dom5md-member-form-modal,
.dom5md-team-form-modal {
  background: var(--dom5md-white);
  border-radius: var(--dom5md-radius);
  box-shadow: var(--dom5md-shadow-hover);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}
.dom5md-member-form-modal .modal-header,
.dom5md-team-form-modal .modal-header {
  padding: var(--dom5md-spacing);
  border-bottom: 1px solid var(--dom5md-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dom5md-member-form-modal .modal-header h2,
.dom5md-team-form-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--dom5md-text);
}
.dom5md-member-form-modal .modal-header .modal-close,
.dom5md-team-form-modal .modal-header .modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dom5md-text-light);
  padding: 4px;
}
.dom5md-member-form-modal .modal-header .modal-close:hover,
.dom5md-team-form-modal .modal-header .modal-close:hover {
  color: var(--dom5md-error);
}

.dom5md-member-form,
.dom5md-team-form {
  padding: var(--dom5md-spacing);
}
.dom5md-member-form .form-row,
.dom5md-team-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--dom5md-spacing);
}
@media (max-width: 768px) {
  .dom5md-member-form .form-row,
  .dom5md-team-form .form-row {
    grid-template-columns: 1fr;
  }
}
.dom5md-member-form .form-group,
.dom5md-team-form .form-group {
  margin-bottom: var(--dom5md-spacing);
}
.dom5md-member-form .form-group label,
.dom5md-team-form .form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--dom5md-text);
}
.dom5md-member-form .form-group input,
.dom5md-member-form .form-group textarea,
.dom5md-member-form .form-group select,
.dom5md-team-form .form-group input,
.dom5md-team-form .form-group textarea,
.dom5md-team-form .form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  font-size: 14px;
}
.dom5md-member-form .form-group input:focus,
.dom5md-member-form .form-group textarea:focus,
.dom5md-member-form .form-group select:focus,
.dom5md-team-form .form-group input:focus,
.dom5md-team-form .form-group textarea:focus,
.dom5md-team-form .form-group select:focus {
  border-color: var(--dom5md-primary);
  box-shadow: 0 0 0 1px var(--dom5md-primary);
  outline: none;
}
.dom5md-member-form .form-group input.error,
.dom5md-member-form .form-group textarea.error,
.dom5md-member-form .form-group select.error,
.dom5md-team-form .form-group input.error,
.dom5md-team-form .form-group textarea.error,
.dom5md-team-form .form-group select.error {
  border-color: var(--dom5md-error);
}
.dom5md-member-form .form-group .error-message,
.dom5md-team-form .form-group .error-message {
  display: block;
  color: var(--dom5md-error);
  font-size: 12px;
  margin-top: 4px;
}
.dom5md-member-form .teams-selection .teams-checkboxes,
.dom5md-team-form .teams-selection .teams-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: 12px;
}
.dom5md-member-form .teams-selection .team-checkbox,
.dom5md-team-form .teams-selection .team-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.dom5md-member-form .teams-selection .team-checkbox:hover,
.dom5md-team-form .teams-selection .team-checkbox:hover {
  background: var(--dom5md-bg);
}
.dom5md-member-form .teams-selection .team-checkbox input[type=checkbox],
.dom5md-team-form .teams-selection .team-checkbox input[type=checkbox] {
  width: auto;
  margin: 0;
  flex-shrink: 0;
}
.dom5md-member-form .teams-selection .team-checkbox .team-name,
.dom5md-team-form .teams-selection .team-checkbox .team-name {
  font-weight: 500;
  color: var(--dom5md-text);
  display: block;
}
.dom5md-member-form .teams-selection .team-checkbox .team-description,
.dom5md-team-form .teams-selection .team-checkbox .team-description {
  font-size: 12px;
  color: var(--dom5md-text-light);
  display: block;
  margin-top: 2px;
}
.dom5md-member-form .teams-selection .no-teams,
.dom5md-team-form .teams-selection .no-teams {
  color: var(--dom5md-text-light);
  font-style: italic;
  text-align: center;
  padding: 2rem;
}
.dom5md-member-form .image-upload-field .image-preview,
.dom5md-team-form .image-upload-field .image-preview {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
}
.dom5md-member-form .image-upload-field .image-preview img,
.dom5md-team-form .image-upload-field .image-preview img {
  max-width: 150px;
  max-height: 150px;
  border-radius: var(--dom5md-radius);
  border: 1px solid var(--dom5md-border);
}
.dom5md-member-form .image-upload-field .image-preview .remove-image,
.dom5md-team-form .image-upload-field .image-preview .remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--dom5md-error);
  color: var(--dom5md-white);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dom5md-member-form .image-upload-field .image-preview .remove-image:hover,
.dom5md-team-form .image-upload-field .image-preview .remove-image:hover {
  background: #b32d2d;
}
.dom5md-member-form .form-actions,
.dom5md-team-form .form-actions {
  margin-top: 2rem;
  padding-top: var(--dom5md-spacing);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
.dom5md-member-form .form-actions .button,
.dom5md-team-form .form-actions .button {
  padding: 10px 20px;
  font-size: 14px;
}

.dom5md-team-card .team-card-header {
  padding: var(--dom5md-spacing);
  border-bottom: 1px solid var(--dom5md-border);
}
.dom5md-team-card .team-card-header .team-name {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dom5md-text);
}
.dom5md-team-card .team-card-header .team-slug {
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
  background: var(--dom5md-bg);
  padding: 2px 6px;
  border-radius: var(--dom5md-radius);
}
.dom5md-team-card .team-card-body {
  padding: var(--dom5md-spacing);
}
.dom5md-team-card .team-card-body .team-description {
  color: var(--dom5md-text-light);
  line-height: 1.5;
  margin: 0 0 12px 0;
}
.dom5md-team-card .team-card-body .team-stats {
  margin-bottom: 12px;
}
.dom5md-team-card .team-card-body .team-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}
.dom5md-team-card .team-card-body .team-stats .stat-item .stat-label {
  font-weight: 500;
  color: var(--dom5md-text);
}
.dom5md-team-card .team-card-body .team-stats .stat-item .stat-value {
  font-weight: 600;
  color: var(--dom5md-primary);
}
.dom5md-team-card .team-card-body .team-meta {
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
}
.dom5md-team-card .team-card-actions {
  padding: 12px var(--dom5md-spacing);
  background: var(--dom5md-bg);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  gap: 8px;
  justify-content: space-between;
}
.dom5md-team-card .team-card-actions .button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 12px;
}
.dom5md-team-card .team-card-actions .button .dashicons {
  font-size: 14px;
}

.dom5md-submission-card {
  cursor: pointer;
  transition: all 0.2s ease;
}
.dom5md-submission-card:hover {
  border-color: var(--dom5md-primary);
}
.dom5md-submission-card.selected {
  border-color: var(--dom5md-primary);
  box-shadow: 0 0 0 1px var(--dom5md-primary);
}
.dom5md-submission-card.unread {
  border-left: 4px solid var(--dom5md-error);
}
.dom5md-submission-card .submission-card-header {
  padding: var(--dom5md-spacing);
  border-bottom: 1px solid var(--dom5md-border);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.dom5md-submission-card .submission-card-header .sender-info {
  flex: 1;
}
.dom5md-submission-card .submission-card-header .sender-info .sender-name {
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dom5md-text);
}
.dom5md-submission-card .submission-card-header .sender-info .sender-email {
  margin: 0;
}
.dom5md-submission-card .submission-card-header .sender-info .sender-email a {
  color: var(--dom5md-primary);
  text-decoration: none;
  font-size: 0.9rem;
}
.dom5md-submission-card .submission-card-header .sender-info .sender-email a:hover {
  text-decoration: underline;
}
.dom5md-submission-card .submission-card-body {
  padding: var(--dom5md-spacing);
}
.dom5md-submission-card .submission-card-body .recipient-info {
  margin-bottom: 12px;
  font-size: 0.9rem;
}
.dom5md-submission-card .submission-card-body .recipient-info .recipient-label {
  color: var(--dom5md-text-light);
  margin-right: 4px;
}
.dom5md-submission-card .submission-card-body .recipient-info .recipient-name {
  font-weight: 500;
  color: var(--dom5md-text);
}
.dom5md-submission-card .submission-card-body .message-preview {
  color: var(--dom5md-text-light);
  line-height: 1.5;
  margin-bottom: 12px;
  font-size: 0.9rem;
}
.dom5md-submission-card .submission-card-body .submission-meta {
  display: flex;
  gap: 16px;
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
}
.dom5md-submission-card .submission-card-body .submission-meta .submission-date,
.dom5md-submission-card .submission-card-body .submission-meta .submission-ip {
  display: flex;
  align-items: center;
  gap: 4px;
}
.dom5md-submission-card .submission-card-body .submission-meta .submission-date .dashicons,
.dom5md-submission-card .submission-card-body .submission-meta .submission-ip .dashicons {
  font-size: 12px;
}
.dom5md-submission-card .submission-card-actions {
  padding: 8px var(--dom5md-spacing);
  background: var(--dom5md-bg);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  gap: 4px;
  justify-content: flex-end;
}
.dom5md-submission-card .submission-card-actions .button {
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}
.dom5md-submission-card .submission-card-actions .button .dashicons {
  font-size: 14px;
}

.dom5md-dashboard .dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: var(--dom5md-spacing);
}
@media (max-width: 1024px) {
  .dom5md-dashboard .dashboard-content {
    grid-template-columns: 1fr;
  }
}
.dom5md-dashboard .dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}
.dom5md-dashboard .dashboard-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.dom5md-dashboard-stats {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
}
.dom5md-dashboard-stats h2 {
  margin: 0 0 var(--dom5md-spacing) 0;
  font-size: 1.3rem;
  color: var(--dom5md-text);
}
.dom5md-dashboard-stats .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--dom5md-spacing);
}
.dom5md-dashboard-stats .stat-card {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}
.dom5md-dashboard-stats .stat-card:hover {
  box-shadow: var(--dom5md-shadow-hover);
}
.dom5md-dashboard-stats .stat-card.loading {
  opacity: 0.6;
}
.dom5md-dashboard-stats .stat-card .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.dom5md-dashboard-stats .stat-card .stat-icon .dashicons {
  font-size: 24px;
  color: var(--dom5md-white);
}
.dom5md-dashboard-stats .stat-card .stat-content {
  flex: 1;
}
.dom5md-dashboard-stats .stat-card .stat-content .stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dom5md-text);
  margin: 0 0 4px 0;
}
.dom5md-dashboard-stats .stat-card .stat-content .stat-label {
  font-size: 0.9rem;
  color: var(--dom5md-text-light);
  margin: 0;
}
.dom5md-dashboard-stats .stat-card.blue .stat-icon {
  background: var(--dom5md-primary);
}
.dom5md-dashboard-stats .stat-card.green .stat-icon {
  background: var(--dom5md-success);
}
.dom5md-dashboard-stats .stat-card.orange .stat-icon {
  background: var(--dom5md-warning);
}
.dom5md-dashboard-stats .stat-card.purple .stat-icon {
  background: #8e44ad;
}
.dom5md-dashboard-stats .stat-card.teal .stat-icon {
  background: #16a085;
}
.dom5md-dashboard-stats .stat-card.red .stat-icon {
  background: var(--dom5md-error);
}

.dom5md-recent-activity {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
}
.dom5md-recent-activity h2 {
  margin: 0 0 var(--dom5md-spacing) 0;
  font-size: 1.3rem;
  color: var(--dom5md-text);
}
.dom5md-recent-activity .activity-sections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.dom5md-recent-activity .activity-section h3 {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  color: var(--dom5md-text);
  display: flex;
  align-items: center;
  gap: 8px;
}
.dom5md-recent-activity .activity-section h3 .dashicons {
  color: var(--dom5md-primary);
}
.dom5md-recent-activity .activity-section .activity-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  margin-bottom: 8px;
  transition: background-color 0.2s ease;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item:hover {
  background: var(--dom5md-bg);
}
.dom5md-recent-activity .activity-section .activity-list .activity-item .activity-content {
  flex: 1;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item .activity-content strong {
  color: var(--dom5md-text);
  display: block;
  margin-bottom: 4px;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item .activity-content .activity-meta {
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
  margin-bottom: 4px;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item .activity-content .submission-preview {
  font-size: 0.9rem;
  color: var(--dom5md-text-light);
  line-height: 1.4;
}
.dom5md-recent-activity .activity-section .activity-list .activity-item .status-badge {
  flex-shrink: 0;
  margin-left: 12px;
}
.dom5md-recent-activity .activity-section .no-activity {
  text-align: center;
  color: var(--dom5md-text-light);
  font-style: italic;
  padding: 2rem;
}

.dom5md-quick-actions {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
}
.dom5md-quick-actions h2 {
  margin: 0 0 var(--dom5md-spacing) 0;
  font-size: 1.3rem;
  color: var(--dom5md-text);
}
.dom5md-quick-actions .actions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 2rem;
}
.dom5md-quick-actions .action-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}
.dom5md-quick-actions .action-card:hover {
  box-shadow: var(--dom5md-shadow-hover);
  transform: translateY(-1px);
  text-decoration: none;
  color: inherit;
}
.dom5md-quick-actions .action-card .action-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.dom5md-quick-actions .action-card .action-icon .dashicons {
  font-size: 20px;
  color: var(--dom5md-white);
}
.dom5md-quick-actions .action-card .action-content {
  flex: 1;
}
.dom5md-quick-actions .action-card .action-content h3 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  color: var(--dom5md-text);
}
.dom5md-quick-actions .action-card .action-content p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--dom5md-text-light);
}
.dom5md-quick-actions .action-card .action-arrow {
  color: var(--dom5md-text-light);
  flex-shrink: 0;
}
.dom5md-quick-actions .action-card.blue .action-icon {
  background: var(--dom5md-primary);
}
.dom5md-quick-actions .action-card.green .action-icon {
  background: var(--dom5md-success);
}
.dom5md-quick-actions .action-card.orange .action-icon {
  background: var(--dom5md-warning);
}
.dom5md-quick-actions .action-card.purple .action-icon {
  background: #8e44ad;
}
.dom5md-quick-actions .help-section {
  border-top: 1px solid var(--dom5md-border);
  padding-top: var(--dom5md-spacing);
}
.dom5md-quick-actions .help-section h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: var(--dom5md-text);
}
.dom5md-quick-actions .help-section p {
  margin: 0 0 12px 0;
  color: var(--dom5md-text-light);
  font-size: 0.9rem;
}
.dom5md-quick-actions .help-section .button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
}

.dom5md-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: var(--dom5md-spacing);
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
}
@media (max-width: 768px) {
  .dom5md-pagination {
    flex-direction: column;
    gap: 1rem;
  }
}
.dom5md-pagination .pagination-info {
  color: var(--dom5md-text-light);
  font-size: 0.9rem;
}
.dom5md-pagination .pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}
.dom5md-pagination .pagination-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  color: var(--dom5md-text);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  cursor: pointer;
}
.dom5md-pagination .pagination-btn:hover:not(:disabled) {
  background: var(--dom5md-primary);
  color: var(--dom5md-white);
  border-color: var(--dom5md-primary);
  text-decoration: none;
}
.dom5md-pagination .pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.dom5md-pagination .pagination-btn .dashicons {
  font-size: 16px;
}
.dom5md-pagination .page-numbers {
  display: flex;
  gap: 4px;
}
.dom5md-pagination .page-number {
  padding: 6px 10px;
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  color: var(--dom5md-text);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 32px;
  text-align: center;
}
.dom5md-pagination .page-number:hover:not(.current):not(.ellipsis) {
  background: var(--dom5md-bg);
  text-decoration: none;
}
.dom5md-pagination .page-number.current {
  background: var(--dom5md-primary);
  color: var(--dom5md-white);
  border-color: var(--dom5md-primary);
}
.dom5md-pagination .page-number.ellipsis {
  border: none;
  background: none;
  cursor: default;
  color: var(--dom5md-text-light);
}

.dom5md-settings-page .settings-section {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
  margin-bottom: var(--dom5md-spacing);
}
.dom5md-settings-page .settings-section h2 {
  margin: 0 0 var(--dom5md-spacing) 0;
  font-size: 1.3rem;
  color: var(--dom5md-text);
  border-bottom: 1px solid var(--dom5md-border);
  padding-bottom: 8px;
}
.dom5md-settings-page .settings-info {
  background: var(--dom5md-bg);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: var(--dom5md-spacing);
}
.dom5md-settings-page .settings-info h2 {
  margin: 0 0 var(--dom5md-spacing) 0;
  font-size: 1.2rem;
  color: var(--dom5md-text);
}
.dom5md-settings-page .settings-info .info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--dom5md-spacing);
}
.dom5md-settings-page .settings-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}
.dom5md-settings-page .settings-info .info-item label {
  font-weight: 500;
  color: var(--dom5md-text);
}
.dom5md-settings-page .settings-info .info-item span {
  color: var(--dom5md-text-light);
  font-family: monospace;
}

.dom5md-submission-detail {
  background: var(--dom5md-white);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  height: -moz-fit-content;
  height: fit-content;
  position: sticky;
  top: 2rem;
}
.dom5md-submission-detail .detail-header {
  padding: var(--dom5md-spacing);
  border-bottom: 1px solid var(--dom5md-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dom5md-submission-detail .detail-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--dom5md-text);
}
.dom5md-submission-detail .detail-header .close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dom5md-text-light);
  padding: 4px;
}
.dom5md-submission-detail .detail-header .close-button:hover {
  color: var(--dom5md-error);
}
.dom5md-submission-detail .detail-content {
  padding: var(--dom5md-spacing);
  max-height: 70vh;
  overflow-y: auto;
}
.dom5md-submission-detail .info-section {
  margin-bottom: 1.5rem;
}
.dom5md-submission-detail .info-section h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--dom5md-text);
}
.dom5md-submission-detail .info-section .sender-details .sender-name,
.dom5md-submission-detail .info-section .sender-details .recipient-name,
.dom5md-submission-detail .info-section .recipient-details .sender-name,
.dom5md-submission-detail .info-section .recipient-details .recipient-name {
  margin: 0 0 4px 0;
  font-weight: 600;
}
.dom5md-submission-detail .info-section .sender-details .sender-email,
.dom5md-submission-detail .info-section .sender-details .recipient-email,
.dom5md-submission-detail .info-section .recipient-details .sender-email,
.dom5md-submission-detail .info-section .recipient-details .recipient-email {
  margin: 0;
  color: var(--dom5md-text-light);
}
.dom5md-submission-detail .info-section .sender-details .sender-email a,
.dom5md-submission-detail .info-section .sender-details .recipient-email a,
.dom5md-submission-detail .info-section .recipient-details .sender-email a,
.dom5md-submission-detail .info-section .recipient-details .recipient-email a {
  color: var(--dom5md-primary);
  text-decoration: none;
}
.dom5md-submission-detail .info-section .sender-details .sender-email a:hover,
.dom5md-submission-detail .info-section .sender-details .recipient-email a:hover,
.dom5md-submission-detail .info-section .recipient-details .sender-email a:hover,
.dom5md-submission-detail .info-section .recipient-details .recipient-email a:hover {
  text-decoration: underline;
}
.dom5md-submission-detail .info-section .status-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}
.dom5md-submission-detail .info-section .status-section .status-actions {
  display: flex;
  gap: 8px;
}
.dom5md-submission-detail .message-section {
  margin-bottom: 1.5rem;
}
.dom5md-submission-detail .message-section h3 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: var(--dom5md-text);
}
.dom5md-submission-detail .message-section .message-content {
  background: var(--dom5md-bg);
  border: 1px solid var(--dom5md-border);
  border-radius: var(--dom5md-radius);
  padding: 12px;
  line-height: 1.6;
  color: var(--dom5md-text);
}
.dom5md-submission-detail .message-section .message-content p {
  margin: 0 0 8px 0;
}
.dom5md-submission-detail .message-section .message-content p:last-child {
  margin-bottom: 0;
}
.dom5md-submission-detail .metadata-section {
  margin-bottom: 1.5rem;
}
.dom5md-submission-detail .metadata-section h3 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: var(--dom5md-text);
}
.dom5md-submission-detail .metadata-section .metadata-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.dom5md-submission-detail .metadata-section .metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid var(--dom5md-border);
}
.dom5md-submission-detail .metadata-section .metadata-item:last-child {
  border-bottom: none;
}
.dom5md-submission-detail .metadata-section .metadata-item label {
  font-weight: 500;
  color: var(--dom5md-text);
  margin-right: 12px;
  flex-shrink: 0;
}
.dom5md-submission-detail .metadata-section .metadata-item span {
  color: var(--dom5md-text-light);
  text-align: right;
  word-break: break-word;
}
.dom5md-submission-detail .metadata-section .metadata-item span.user-agent {
  font-size: 0.8rem;
  font-family: monospace;
}
.dom5md-submission-detail .detail-actions {
  padding: var(--dom5md-spacing);
  border-top: 1px solid var(--dom5md-border);
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.dom5md-submission-detail .detail-actions .button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  font-size: 0.9rem;
}

.submissions-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
}
@media (max-width: 1200px) {
  .submissions-layout {
    grid-template-columns: 1fr;
  }
}
.submissions-layout .submissions-list-container {
  min-width: 0;
}
@media (max-width: 1200px) {
  .submissions-layout .submission-detail-container {
    order: -1;
  }
}

@media (max-width: 768px) {
  .dom5md-page-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  .dom5md-page-header .button {
    align-self: center;
  }
  .dom5md-members-grid,
  .dom5md-teams-grid {
    grid-template-columns: 1fr;
  }
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  .submissions-layout {
    grid-template-columns: 1fr;
  }
  .dom5md-member-form .form-row,
  .dom5md-team-form .form-row {
    grid-template-columns: 1fr;
  }
}

/*# sourceMappingURL=dom5-member-directory.core.min.css.map*/