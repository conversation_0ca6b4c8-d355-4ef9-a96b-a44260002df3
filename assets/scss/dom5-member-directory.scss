// DOM5 Member Directory Styles

// Variables
:root {
    --dom5md-primary: #0073aa;
    --dom5md-secondary: #005177;
    --dom5md-success: #46b450;
    --dom5md-warning: #ffb900;
    --dom5md-error: #dc3232;
    --dom5md-text: #23282d;
    --dom5md-text-light: #646970;
    --dom5md-border: #c3c4c7;
    --dom5md-bg: #f6f7f7;
    --dom5md-white: #ffffff;
    --dom5md-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --dom5md-shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.15);
    --dom5md-radius: 4px;
    --dom5md-spacing: 16px;
}

// Base styles
.dom5md-admin-page {
    .dom5-member-directory-body {
        font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Can<PERSON>ell, "Helvetica Neue", sans-serif;
        color: var(--dom5md-text);
        line-height: 1.5;
    }
}

// Page header
.dom5md-page-header {
    margin-bottom: var(--dom5md-spacing);
    padding-bottom: var(--dom5md-spacing);
    border-bottom: 1px solid var(--dom5md-border);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--dom5md-spacing);

    h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--dom5md-text);
    }

    .page-description {
        color: var(--dom5md-text-light);
        margin: 8px 0 0 0;
        font-size: 0.9rem;
    }

    .button {
        flex-shrink: 0;
    }
}

// Loading states
.dom5md-loading {
    text-align: center;
    padding: 2rem;
    color: var(--dom5md-text-light);

    .dashicons {
        animation: spin 1s linear infinite;
        margin-right: 8px;
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

// No results
.dom5md-no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--dom5md-text-light);

    .no-results-content {
        max-width: 400px;
        margin: 0 auto;

        .dashicons {
            font-size: 3rem;
            opacity: 0.5;
            margin-bottom: 1rem;
        }

        h3 {
            margin: 0 0 0.5rem 0;
            color: var(--dom5md-text);
        }

        p {
            margin: 0;
            font-size: 0.9rem;
        }
    }
}

// Search and filters
.dom5md-search-filter {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    padding: var(--dom5md-spacing);
    margin-bottom: var(--dom5md-spacing);

    .filter-row {
        display: flex;
        gap: var(--dom5md-spacing);
        align-items: center;
        flex-wrap: wrap;
    }

    .search-group {
        position: relative;
        flex: 1;
        min-width: 250px;

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            font-size: 14px;

            &:focus {
                border-color: var(--dom5md-primary);
                box-shadow: 0 0 0 1px var(--dom5md-primary);
                outline: none;
            }
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--dom5md-text-light);
            pointer-events: none;
        }
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 7px 7px 7px;

        label {
            font-weight: 500;
            white-space: nowrap;
        }

        select {
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            font-size: 14px;
        }
    }

    .clear-filters {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        font-size: 13px;
    }

    .active-filters {
        margin-top: var(--dom5md-spacing);
        padding-top: var(--dom5md-spacing);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .filters-label {
            font-weight: 500;
            color: var(--dom5md-text-light);
            font-size: 13px;
        }

        .filter-tag {
            background: var(--dom5md-bg);
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            padding: 4px 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;

            .remove-filter {
                background: none;
                border: none;
                padding: 0;
                cursor: pointer;
                color: var(--dom5md-text-light);

                &:hover {
                    color: var(--dom5md-error);
                }
            }
        }
    }
}

// Status badges
.dom5md-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: var(--dom5md-radius);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.status-active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    &.status-draft {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    &.status-unread {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    &.status-read {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    &.status-replied {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
}

// Grid layouts
.dom5md-members-grid,
.dom5md-teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--dom5md-spacing);
    margin-bottom: var(--dom5md-spacing);
}

.dom5md-submissions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: var(--dom5md-spacing);
}

// Card components
.dom5md-member-card,
.dom5md-team-card,
.dom5md-submission-card {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    box-shadow: var(--dom5md-shadow);
    transition: all 0.2s ease;
    overflow: hidden;

    &:hover {
        box-shadow: var(--dom5md-shadow-hover);
        transform: translateY(-1px);
    }
}

// Member card specific styles
.dom5md-member-card {
    .member-card-header {
        position: relative;
        padding: var(--dom5md-spacing);
        text-align: center;
        border-bottom: 1px solid var(--dom5md-border);

        .member-avatar,
        .member-avatar-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 12px;
            overflow: hidden;
            border: 3px solid var(--dom5md-border);

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .member-avatar-placeholder {
            background: var(--dom5md-bg);
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-initials {
                font-size: 24px;
                font-weight: 600;
                color: var(--dom5md-text-light);
            }
        }
    }

    .member-card-body {
        padding: var(--dom5md-spacing);

        .member-name {
            margin: 0 0 8px 0;
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
        }

        .member-email {
            text-align: center;
            margin: 0 0 12px 0;

            a {
                color: var(--dom5md-primary);
                text-decoration: none;
                font-size: 0.9rem;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .member-address {
            display: flex;
            align-items: flex-start;
            gap: 6px;
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            color: var(--dom5md-text-light);

            .dashicons {
                margin-top: 2px;
                flex-shrink: 0;
            }
        }

        .member-color {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 12px 0;
            font-size: 0.9rem;

            .color-swatch {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                border: 2px solid var(--dom5md-border);
                flex-shrink: 0;
            }
        }

        .member-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 0 8px 0;

            .member-slug {
                font-family: monospace;
                font-size: 0.8rem;
                color: var(--dom5md-text-light);
                background: var(--dom5md-bg);
                padding: 2px 6px;
                border-radius: var(--dom5md-radius);
            }
        }

        .member-dates {
            font-size: 0.8rem;
            color: var(--dom5md-text-light);
        }
    }

    .member-card-actions {
        padding: 12px var(--dom5md-spacing);
        background: var(--dom5md-bg);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        gap: 8px;
        justify-content: space-between;

        .button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 6px 12px;
            font-size: 12px;
            text-decoration: none;

            .dashicons {
                font-size: 14px;
            }
        }
    }
}

// Form components
.dom5md-member-form-overlay,
.dom5md-team-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.dom5md-member-form-modal,
.dom5md-team-form-modal {
    background: var(--dom5md-white);
    border-radius: var(--dom5md-radius);
    box-shadow: var(--dom5md-shadow-hover);
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;

    .modal-header {
        padding: var(--dom5md-spacing);
        border-bottom: 1px solid var(--dom5md-border);
        display: flex;
        justify-content: space-between;
        align-items: center;

        h2 {
            margin: 0;
            font-size: 1.5rem;
            color: var(--dom5md-text);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dom5md-text-light);
            padding: 4px;

            &:hover {
                color: var(--dom5md-error);
            }
        }
    }
}

.dom5md-member-form,
.dom5md-team-form {
    padding: var(--dom5md-spacing);

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--dom5md-spacing);

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .form-group {
        margin-bottom: var(--dom5md-spacing);

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--dom5md-text);
        }

        input,
        textarea,
        select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            font-size: 14px;

            &:focus {
                border-color: var(--dom5md-primary);
                box-shadow: 0 0 0 1px var(--dom5md-primary);
                outline: none;
            }

            &.error {
                border-color: var(--dom5md-error);
            }
        }

        .error-message {
            display: block;
            color: var(--dom5md-error);
            font-size: 12px;
            margin-top: 4px;
        }
    }

    .teams-selection {
        .teams-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            padding: 12px;
        }

        .team-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 8px;
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
                background: var(--dom5md-bg);
            }

            input[type="checkbox"] {
                width: auto;
                margin: 0;
                flex-shrink: 0;
            }

            .team-name {
                font-weight: 500;
                color: var(--dom5md-text);
                display: block;
            }

            .team-description {
                font-size: 12px;
                color: var(--dom5md-text-light);
                display: block;
                margin-top: 2px;
            }
        }

        .no-teams {
            color: var(--dom5md-text-light);
            font-style: italic;
            text-align: center;
            padding: 2rem;
        }
    }

    .image-upload-field {
        .image-preview {
            position: relative;
            display: inline-block;
            margin-bottom: 8px;

            img {
                max-width: 150px;
                max-height: 150px;
                border-radius: var(--dom5md-radius);
                border: 1px solid var(--dom5md-border);
            }

            .remove-image {
                position: absolute;
                top: -8px;
                right: -8px;
                background: var(--dom5md-error);
                color: var(--dom5md-white);
                border: none;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background: #b32d2d;
                }
            }
        }
    }

    .form-actions {
        margin-top: 2rem;
        padding-top: var(--dom5md-spacing);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        .button {
            padding: 10px 20px;
            font-size: 14px;
        }
    }
}

// Team card specific styles
.dom5md-team-card {
    .team-card-header {
        padding: var(--dom5md-spacing);
        border-bottom: 1px solid var(--dom5md-border);

        .team-name {
            margin: 0 0 8px 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dom5md-text);
        }

        .team-slug {
            font-family: monospace;
            font-size: 0.8rem;
            color: var(--dom5md-text-light);
            background: var(--dom5md-bg);
            padding: 2px 6px;
            border-radius: var(--dom5md-radius);
        }
    }

    .team-card-body {
        padding: var(--dom5md-spacing);

        .team-description {
            color: var(--dom5md-text-light);
            line-height: 1.5;
            margin: 0 0 12px 0;
        }

        .team-stats {
            margin-bottom: 12px;

            .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;

                .stat-label {
                    font-weight: 500;
                    color: var(--dom5md-text);
                }

                .stat-value {
                    font-weight: 600;
                    color: var(--dom5md-primary);
                }
            }
        }

        .team-meta {
            font-size: 0.8rem;
            color: var(--dom5md-text-light);
        }
    }

    .team-card-actions {
        padding: 12px var(--dom5md-spacing);
        background: var(--dom5md-bg);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        gap: 8px;
        justify-content: space-between;

        .button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 6px 12px;
            font-size: 12px;

            .dashicons {
                font-size: 14px;
            }
        }
    }
}

// Submission card specific styles
.dom5md-submission-card {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        border-color: var(--dom5md-primary);
    }

    &.selected {
        border-color: var(--dom5md-primary);
        box-shadow: 0 0 0 1px var(--dom5md-primary);
    }

    &.unread {
        border-left: 4px solid var(--dom5md-error);
    }

    .submission-card-header {
        padding: var(--dom5md-spacing);
        border-bottom: 1px solid var(--dom5md-border);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .sender-info {
            flex: 1;

            .sender-name {
                margin: 0 0 4px 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--dom5md-text);
            }

            .sender-email {
                margin: 0;

                a {
                    color: var(--dom5md-primary);
                    text-decoration: none;
                    font-size: 0.9rem;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }

    .submission-card-body {
        padding: var(--dom5md-spacing);

        .recipient-info {
            margin-bottom: 12px;
            font-size: 0.9rem;

            .recipient-label {
                color: var(--dom5md-text-light);
                margin-right: 4px;
            }

            .recipient-name {
                font-weight: 500;
                color: var(--dom5md-text);
            }
        }

        .message-preview {
            color: var(--dom5md-text-light);
            line-height: 1.5;
            margin-bottom: 12px;
            font-size: 0.9rem;
        }

        .submission-meta {
            display: flex;
            gap: 16px;
            font-size: 0.8rem;
            color: var(--dom5md-text-light);

            .submission-date,
            .submission-ip {
                display: flex;
                align-items: center;
                gap: 4px;

                .dashicons {
                    font-size: 12px;
                }
            }
        }
    }

    .submission-card-actions {
        padding: 8px var(--dom5md-spacing);
        background: var(--dom5md-bg);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        gap: 4px;
        justify-content: flex-end;

        .button {
            padding: 4px 8px;
            font-size: 12px;
            min-width: auto;

            .dashicons {
                font-size: 14px;
            }
        }
    }
}

// Dashboard specific styles
.dom5md-dashboard {
    .dashboard-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-top: var(--dom5md-spacing);

        @media (max-width: 1024px) {
            grid-template-columns: 1fr;
        }
    }

    .dashboard-main {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .dashboard-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
}

.dom5md-dashboard-stats {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    padding: var(--dom5md-spacing);

    h2 {
        margin: 0 0 var(--dom5md-spacing) 0;
        font-size: 1.3rem;
        color: var(--dom5md-text);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--dom5md-spacing);
    }

    .stat-card {
        background: var(--dom5md-white);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        padding: var(--dom5md-spacing);
        display: flex;
        align-items: center;
        gap: 12px;
        transition: all 0.2s ease;

        &:hover {
            box-shadow: var(--dom5md-shadow-hover);
        }

        &.loading {
            opacity: 0.6;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .dashicons {
                font-size: 24px;
                color: var(--dom5md-white);
            }
        }

        .stat-content {
            flex: 1;

            .stat-value {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--dom5md-text);
                margin: 0 0 4px 0;
            }

            .stat-label {
                font-size: 0.9rem;
                color: var(--dom5md-text-light);
                margin: 0;
            }
        }

        // Color variations
        &.blue .stat-icon {
            background: var(--dom5md-primary);
        }

        &.green .stat-icon {
            background: var(--dom5md-success);
        }

        &.orange .stat-icon {
            background: var(--dom5md-warning);
        }

        &.purple .stat-icon {
            background: #8e44ad;
        }

        &.teal .stat-icon {
            background: #16a085;
        }

        &.red .stat-icon {
            background: var(--dom5md-error);
        }
    }
}

// Recent activity styles
.dom5md-recent-activity {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    padding: var(--dom5md-spacing);

    h2 {
        margin: 0 0 var(--dom5md-spacing) 0;
        font-size: 1.3rem;
        color: var(--dom5md-text);
    }

    .activity-sections {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .activity-section {
        h3 {
            margin: 0 0 12px 0;
            font-size: 1.1rem;
            color: var(--dom5md-text);
            display: flex;
            align-items: center;
            gap: 8px;

            .dashicons {
                color: var(--dom5md-primary);
            }
        }

        .activity-list {
            list-style: none;
            margin: 0;
            padding: 0;

            .activity-item {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 12px;
                border: 1px solid var(--dom5md-border);
                border-radius: var(--dom5md-radius);
                margin-bottom: 8px;
                transition: background-color 0.2s ease;

                &:hover {
                    background: var(--dom5md-bg);
                }

                .activity-content {
                    flex: 1;

                    strong {
                        color: var(--dom5md-text);
                        display: block;
                        margin-bottom: 4px;
                    }

                    .activity-meta {
                        font-size: 0.8rem;
                        color: var(--dom5md-text-light);
                        margin-bottom: 4px;
                    }

                    .submission-preview {
                        font-size: 0.9rem;
                        color: var(--dom5md-text-light);
                        line-height: 1.4;
                    }
                }

                .status-badge {
                    flex-shrink: 0;
                    margin-left: 12px;
                }
            }
        }

        .no-activity {
            text-align: center;
            color: var(--dom5md-text-light);
            font-style: italic;
            padding: 2rem;
        }
    }
}

// Quick actions styles
.dom5md-quick-actions {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    padding: var(--dom5md-spacing);

    h2 {
        margin: 0 0 var(--dom5md-spacing) 0;
        font-size: 1.3rem;
        color: var(--dom5md-text);
    }

    .actions-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 2rem;
    }

    .action-card {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: var(--dom5md-white);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        color: inherit;

        &:hover {
            box-shadow: var(--dom5md-shadow-hover);
            transform: translateY(-1px);
            text-decoration: none;
            color: inherit;
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .dashicons {
                font-size: 20px;
                color: var(--dom5md-white);
            }
        }

        .action-content {
            flex: 1;

            h3 {
                margin: 0 0 4px 0;
                font-size: 1rem;
                color: var(--dom5md-text);
            }

            p {
                margin: 0;
                font-size: 0.8rem;
                color: var(--dom5md-text-light);
            }
        }

        .action-arrow {
            color: var(--dom5md-text-light);
            flex-shrink: 0;
        }

        // Color variations
        &.blue .action-icon {
            background: var(--dom5md-primary);
        }

        &.green .action-icon {
            background: var(--dom5md-success);
        }

        &.orange .action-icon {
            background: var(--dom5md-warning);
        }

        &.purple .action-icon {
            background: #8e44ad;
        }
    }

    .help-section {
        border-top: 1px solid var(--dom5md-border);
        padding-top: var(--dom5md-spacing);

        h3 {
            margin: 0 0 8px 0;
            font-size: 1.1rem;
            color: var(--dom5md-text);
        }

        p {
            margin: 0 0 12px 0;
            color: var(--dom5md-text-light);
            font-size: 0.9rem;
        }

        .button {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }
    }
}

// Pagination styles
.dom5md-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding: var(--dom5md-spacing);
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination-info {
        color: var(--dom5md-text-light);
        font-size: 0.9rem;
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .pagination-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        background: var(--dom5md-white);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        color: var(--dom5md-text);
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover:not(:disabled) {
            background: var(--dom5md-primary);
            color: var(--dom5md-white);
            border-color: var(--dom5md-primary);
            text-decoration: none;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .dashicons {
            font-size: 16px;
        }
    }

    .page-numbers {
        display: flex;
        gap: 4px;
    }

    .page-number {
        padding: 6px 10px;
        background: var(--dom5md-white);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        color: var(--dom5md-text);
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        cursor: pointer;
        min-width: 32px;
        text-align: center;

        &:hover:not(.current):not(.ellipsis) {
            background: var(--dom5md-bg);
            text-decoration: none;
        }

        &.current {
            background: var(--dom5md-primary);
            color: var(--dom5md-white);
            border-color: var(--dom5md-primary);
        }

        &.ellipsis {
            border: none;
            background: none;
            cursor: default;
            color: var(--dom5md-text-light);
        }
    }
}

// Settings page styles
.dom5md-settings-page {
    .settings-section {
        background: var(--dom5md-white);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        padding: var(--dom5md-spacing);
        margin-bottom: var(--dom5md-spacing);

        h2 {
            margin: 0 0 var(--dom5md-spacing) 0;
            font-size: 1.3rem;
            color: var(--dom5md-text);
            border-bottom: 1px solid var(--dom5md-border);
            padding-bottom: 8px;
        }
    }

    .settings-info {
        background: var(--dom5md-bg);
        border: 1px solid var(--dom5md-border);
        border-radius: var(--dom5md-radius);
        padding: var(--dom5md-spacing);

        h2 {
            margin: 0 0 var(--dom5md-spacing) 0;
            font-size: 1.2rem;
            color: var(--dom5md-text);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--dom5md-spacing);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;

            label {
                font-weight: 500;
                color: var(--dom5md-text);
            }

            span {
                color: var(--dom5md-text-light);
                font-family: monospace;
            }
        }
    }
}

// Submission detail styles
.dom5md-submission-detail {
    background: var(--dom5md-white);
    border: 1px solid var(--dom5md-border);
    border-radius: var(--dom5md-radius);
    height: fit-content;
    position: sticky;
    top: 2rem;

    .detail-header {
        padding: var(--dom5md-spacing);
        border-bottom: 1px solid var(--dom5md-border);
        display: flex;
        justify-content: space-between;
        align-items: center;

        h2 {
            margin: 0;
            font-size: 1.3rem;
            color: var(--dom5md-text);
        }

        .close-button {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dom5md-text-light);
            padding: 4px;

            &:hover {
                color: var(--dom5md-error);
            }
        }
    }

    .detail-content {
        padding: var(--dom5md-spacing);
        max-height: 70vh;
        overflow-y: auto;
    }

    .info-section {
        margin-bottom: 1.5rem;

        h3 {
            margin: 0 0 8px 0;
            font-size: 1rem;
            color: var(--dom5md-text);
        }

        .sender-details,
        .recipient-details {
            .sender-name,
            .recipient-name {
                margin: 0 0 4px 0;
                font-weight: 600;
            }

            .sender-email,
            .recipient-email {
                margin: 0;
                color: var(--dom5md-text-light);

                a {
                    color: var(--dom5md-primary);
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }

        .status-section {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;

            .status-actions {
                display: flex;
                gap: 8px;
            }
        }
    }

    .message-section {
        margin-bottom: 1.5rem;

        h3 {
            margin: 0 0 12px 0;
            font-size: 1rem;
            color: var(--dom5md-text);
        }

        .message-content {
            background: var(--dom5md-bg);
            border: 1px solid var(--dom5md-border);
            border-radius: var(--dom5md-radius);
            padding: 12px;
            line-height: 1.6;
            color: var(--dom5md-text);

            p {
                margin: 0 0 8px 0;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .metadata-section {
        margin-bottom: 1.5rem;

        h3 {
            margin: 0 0 12px 0;
            font-size: 1rem;
            color: var(--dom5md-text);
        }

        .metadata-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .metadata-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 6px 0;
            border-bottom: 1px solid var(--dom5md-border);

            &:last-child {
                border-bottom: none;
            }

            label {
                font-weight: 500;
                color: var(--dom5md-text);
                margin-right: 12px;
                flex-shrink: 0;
            }

            span {
                color: var(--dom5md-text-light);
                text-align: right;
                word-break: break-word;

                &.user-agent {
                    font-size: 0.8rem;
                    font-family: monospace;
                }
            }
        }
    }

    .detail-actions {
        padding: var(--dom5md-spacing);
        border-top: 1px solid var(--dom5md-border);
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .button {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
    }
}

// Submissions layout
.submissions-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;

    @media (max-width: 1200px) {
        grid-template-columns: 1fr;
    }

    .submissions-list-container {
        min-width: 0; // Prevent grid overflow
    }

    .submission-detail-container {
        @media (max-width: 1200px) {
            order: -1;
        }
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    .dom5md-page-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;

        .button {
            align-self: center;
        }
    }

    .dom5md-members-grid,
    .dom5md-teams-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .submissions-layout {
        grid-template-columns: 1fr;
    }

    .dom5md-member-form,
    .dom5md-team-form {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
}
