<?php
/**
 * Members Listing Template
 * 
 * This template displays all members
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<div class="dom5md-members-page">
    <div class="container">
        <div class="page-header">
            <h1><?php esc_html_e('Members', 'dom5-member-directory'); ?></h1>
            <p><?php esc_html_e('Meet our team members', 'dom5-member-directory'); ?></p>
        </div>

        <div id="dom5md-members-listing" class="members-listing">
            <div class="loading-placeholder">
                <p><?php esc_html_e('Loading members...', 'dom5-member-directory'); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.dom5md-members-page {
    padding: 2rem 0;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    margin: 0 0 1rem 0;
    color: #333;
}

.page-header p {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
}

.members-listing {
    max-width: 1200px;
    margin: 0 auto;
}

.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.dom5md-members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.dom5md-member-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dom5md-member-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.member-card-header {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.member-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    overflow: hidden;
    border: 3px solid #f0f0f0;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #e0e0e0;
}

.avatar-initials {
    font-size: 1.5rem;
    font-weight: bold;
    color: #666;
}

.member-card-body {
    padding: 1.5rem;
}

.member-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    text-align: center;
    color: #333;
}

.member-email {
    text-align: center;
    margin: 0 0 1rem 0;
}

.member-email a {
    color: #0073aa;
    text-decoration: none;
    font-size: 0.9rem;
}

.member-email a:hover {
    text-decoration: underline;
}

.member-address {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    color: #666;
}

.member-color {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.member-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 1rem 0;
}

.member-slug {
    font-family: monospace;
    font-size: 0.8rem;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
}

.member-dates {
    font-size: 0.8rem;
    color: #999;
}

.member-card-actions {
    padding: 1rem 1.5rem;
    background: #f9f9f9;
    border-top: 1px solid #eee;
    text-align: center;
}

.view-member-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.view-member-btn:hover {
    background: #005177;
    color: #fff;
}

.dom5md-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
}

.page-number:hover:not(.current):not(.ellipsis) {
    background: #f0f0f0;
}

.page-number.current {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

.page-number.ellipsis {
    border: none;
    background: none;
    cursor: default;
}

@media (max-width: 768px) {
    .dom5md-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .pagination-btn,
    .page-number {
        padding: 0.4rem 0.6rem;
        font-size: 0.9rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // This would be replaced by the React component in a real implementation
    const container = document.getElementById('dom5md-members-listing');
    if (container) {
        // Placeholder for React component mounting
        container.innerHTML = '<div class="loading-placeholder"><p><?php esc_html_e('Loading members...', 'dom5-member-directory'); ?></p></div>';
    }
});
</script>

<?php get_footer(); ?>
