<?php
/**
 * Teams Listing Template
 * 
 * This template displays all teams
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<div class="dom5md-teams-page">
    <div class="container">
        <div class="page-header">
            <h1><?php esc_html_e('Teams', 'dom5-member-directory'); ?></h1>
            <p><?php esc_html_e('Discover our teams and their members', 'dom5-member-directory'); ?></p>
        </div>

        <div id="dom5md-teams-listing" class="teams-listing">
            <div class="loading-placeholder">
                <p><?php esc_html_e('Loading teams...', 'dom5-member-directory'); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.dom5md-teams-page {
    padding: 2rem 0;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    margin: 0 0 1rem 0;
    color: #333;
}

.page-header p {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
}

.teams-listing {
    max-width: 1200px;
    margin: 0 auto;
}

.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.dom5md-teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.dom5md-team-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dom5md-team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.team-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
}

.team-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #333;
}

.team-slug {
    font-family: monospace;
    font-size: 0.8rem;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 4px;
}

.team-card-body {
    padding: 1.5rem;
}

.team-description {
    color: #666;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
}

.team-stats {
    margin-bottom: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: #333;
}

.stat-value {
    font-weight: 600;
    color: #0073aa;
}

.team-meta {
    font-size: 0.8rem;
    color: #999;
}

.team-card-actions {
    padding: 1rem 1.5rem;
    background: #f9f9f9;
    border-top: 1px solid #eee;
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.team-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: background 0.2s ease;
    border: none;
    cursor: pointer;
}

.team-action-btn:hover {
    background: #005177;
    color: #fff;
}

.team-action-btn.secondary {
    background: #666;
}

.team-action-btn.secondary:hover {
    background: #444;
}

.team-members-preview {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.team-members-preview h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #333;
}

.members-avatars {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.member-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.member-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-avatar-placeholder-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 0.7rem;
    font-weight: bold;
    color: #666;
}

.more-members {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #0073aa;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

@media (max-width: 768px) {
    .dom5md-teams-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .team-card-actions {
        flex-direction: column;
    }
    
    .team-action-btn {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // This would be replaced by the React component in a real implementation
    const container = document.getElementById('dom5md-teams-listing');
    if (container) {
        // Placeholder for React component mounting
        container.innerHTML = '<div class="loading-placeholder"><p><?php esc_html_e('Loading teams...', 'dom5-member-directory'); ?></p></div>';
    }
});
</script>

<?php get_footer(); ?>
