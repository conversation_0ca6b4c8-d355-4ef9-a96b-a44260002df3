<?php
/**
 * Single Member Template
 * 
 * This template displays a single member page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

global $dom5md_member;

if (!$dom5md_member) {
    wp_redirect(home_url());
    exit;
}

get_header(); ?>

<div class="dom5md-single-member">
    <div class="container">
        <div class="member-profile">
            <?php if ($dom5md_member->cover_image_id): ?>
                <div class="member-cover">
                    <?php echo wp_get_attachment_image($dom5md_member->cover_image_id, 'full', false, ['alt' => esc_attr($dom5md_member->first_name . ' ' . $dom5md_member->last_name)]); ?>
                </div>
            <?php endif; ?>

            <div class="member-header">
                <div class="member-avatar">
                    <?php if ($dom5md_member->profile_image_id): ?>
                        <?php echo wp_get_attachment_image($dom5md_member->profile_image_id, 'medium', false, ['alt' => esc_attr($dom5md_member->first_name . ' ' . $dom5md_member->last_name)]); ?>
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <span class="initials">
                                <?php echo esc_html(substr($dom5md_member->first_name, 0, 1) . substr($dom5md_member->last_name, 0, 1)); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="member-info">
                    <h1 class="member-name">
                        <?php echo esc_html($dom5md_member->first_name . ' ' . $dom5md_member->last_name); ?>
                    </h1>

                    <?php if ($dom5md_member->address): ?>
                        <p class="member-address">
                            <span class="dashicons dashicons-location"></span>
                            <?php echo esc_html($dom5md_member->address); ?>
                        </p>
                    <?php endif; ?>

                    <?php if ($dom5md_member->favorite_color): ?>
                        <div class="member-color">
                            <span class="color-label"><?php esc_html_e('Favorite Color:', 'dom5-member-directory'); ?></span>
                            <span class="color-swatch" style="background-color: <?php echo esc_attr($dom5md_member->favorite_color); ?>"></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php
            // Get member teams
            $team_model = new DOM5MemberDirectory\Models\Team();
            $member_teams = $team_model->get_member_teams($dom5md_member->id);
            
            if (!empty($member_teams)): ?>
                <div class="member-teams">
                    <h2><?php esc_html_e('Teams', 'dom5-member-directory'); ?></h2>
                    <div class="teams-list">
                        <?php foreach ($member_teams as $team): ?>
                            <div class="team-item">
                                <h3><?php echo esc_html($team->name); ?></h3>
                                <?php if ($team->short_description): ?>
                                    <p><?php echo esc_html($team->short_description); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (get_option('dom5md_enable_contact_form', true)): ?>
                <div class="member-contact">
                    <h2><?php esc_html_e('Contact', 'dom5-member-directory'); ?></h2>
                    <?php echo do_shortcode('[dom5md_member_contact member_id="' . $dom5md_member->id . '"]'); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.dom5md-single-member {
    padding: 2rem 0;
}

.member-profile {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.member-cover {
    height: 200px;
    overflow: hidden;
}

.member-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-header {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    border-bottom: 1px solid #eee;
}

.member-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.initials {
    font-size: 2rem;
    font-weight: bold;
    color: #666;
}

.member-info h1 {
    margin: 0 0 1rem 0;
    font-size: 2rem;
    color: #333;
}

.member-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    color: #666;
}

.member-color {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.member-teams,
.member-contact {
    padding: 2rem;
    border-bottom: 1px solid #eee;
}

.member-teams:last-child,
.member-contact:last-child {
    border-bottom: none;
}

.teams-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.team-item {
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #eee;
}

.team-item h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.team-item p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .member-header {
        flex-direction: column;
        text-align: center;
    }
    
    .member-avatar {
        width: 100px;
        height: 100px;
    }
    
    .teams-list {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
