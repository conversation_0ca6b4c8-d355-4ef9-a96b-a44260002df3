---
type: "manual"
---

Assignment: WordPress Plugin Development – Member Directory
We’d like you to build a custom, standalone WordPress plugin that functions as a Member
Directory, where each member can be associated with multiple teams. This project will
help us evaluate your understanding of WordPress development best practices.

Plugin Overview
Create a fully functional WordPress plugin with the following capabilities:
Member Fields
● First Name
● Last Name
● Email (must be unique across all members)
● Profile Image
● Cover Image
● Address
● Favorite Color (use a color picker field)
● Status: Active / Draft

Team Fields
● Name
● Short Description

Relationships
● A member can be linked with multiple teams.
● Duplicate email addresses should not be allowed across members.

Frontend Requirements
● Single Member Page
○ URL structure: /first-name_last-name
○ Only members with Active status should be visible.
○ Display all member fields in a clean, user-friendly layout.
○ Include a contact form with:
■ Full Name
■ Email
■ Message
○ On form submission:
■ Send an email to the member’s email address.
■ Store the submission in the WordPress admin.
■ View all submissions from the member’s edit screen in the
dashboard.

● Listing Pages
○ A page to display All Teams.
○ A page to display All Team Members.

Admin Requirements
● Prevent duplicate email addresses when creating/editing members.
● On the admin listing page for members, show the total number of contact form
submissions each member has received.
● From the member edit page, allow viewing all submitted messages.

Technical Guidelines
● Use WordPress coding standards.
● Avoid third-party libraries or frameworks.
● Use WordPress core features wherever possible.
● PSR-4 autoloading is allowed if you prefer.
● Maintain your code using Git (commit history will be reviewed).
● Ensure the code is:
○ Clean
○ Well-structured
○ Optimized
○ Easy to understand
○ Performant
