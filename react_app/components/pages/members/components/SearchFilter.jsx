import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';

const SearchFilter = ({ searchTerm, statusFilter, onSearch, onStatusFilter }) => {
    const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

    // Debounce search input
    useEffect(() => {
        const timer = setTimeout(() => {
            onSearch(localSearchTerm);
        }, 500);

        return () => clearTimeout(timer);
    }, [localSearchTerm, onSearch]);

    const handleSearchChange = (e) => {
        setLocalSearchTerm(e.target.value);
    };

    const handleStatusChange = (e) => {
        onStatusFilter(e.target.value);
    };

    const clearFilters = () => {
        setLocalSearchTerm('');
        onStatusFilter('');
    };

    return (
        <div className="dom5md-search-filter">
            <div className="filter-row">
                <div className="search-group">
                    <label htmlFor="member-search" className="screen-reader-text">
                        {__('Search Members', 'dom5-member-directory')}
                    </label>
                    <input
                        type="text"
                        id="member-search"
                        className="search-input"
                        placeholder={__('Search members by name or email...', 'dom5-member-directory')}
                        value={localSearchTerm}
                        onChange={handleSearchChange}
                    />
                    <span className="search-icon dashicons dashicons-search"></span>
                </div>

                <div className="filter-group">
                    <label htmlFor="status-filter">
                        {__('Status:', 'dom5-member-directory')}
                    </label>
                    <select
                        id="status-filter"
                        value={statusFilter}
                        onChange={handleStatusChange}
                    >
                        <option value="">{__('All Statuses', 'dom5-member-directory')}</option>
                        <option value="active">{__('Active', 'dom5-member-directory')}</option>
                        <option value="draft">{__('Draft', 'dom5-member-directory')}</option>
                    </select>
                </div>

                {(localSearchTerm || statusFilter) && (
                    <button 
                        className="button clear-filters"
                        onClick={clearFilters}
                        title={__('Clear all filters', 'dom5-member-directory')}
                    >
                        <span className="dashicons dashicons-dismiss"></span>
                        {__('Clear', 'dom5-member-directory')}
                    </button>
                )}
            </div>

            {(localSearchTerm || statusFilter) && (
                <div className="active-filters">
                    <span className="filters-label">
                        {__('Active filters:', 'dom5-member-directory')}
                    </span>
                    
                    {localSearchTerm && (
                        <span className="filter-tag">
                            {__('Search:', 'dom5-member-directory')} "{localSearchTerm}"
                            <button 
                                onClick={() => setLocalSearchTerm('')}
                                className="remove-filter"
                            >
                                <span className="dashicons dashicons-no"></span>
                            </button>
                        </span>
                    )}
                    
                    {statusFilter && (
                        <span className="filter-tag">
                            {__('Status:', 'dom5-member-directory')} {statusFilter}
                            <button 
                                onClick={() => onStatusFilter('')}
                                className="remove-filter"
                            >
                                <span className="dashicons dashicons-no"></span>
                            </button>
                        </span>
                    )}
                </div>
            )}
        </div>
    );
};

export default SearchFilter;
