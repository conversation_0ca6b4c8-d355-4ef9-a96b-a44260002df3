import { __ } from '@wordpress/i18n';

const MemberCard = ({ member, onEdit, onDelete }) => {
    const getProfileImageUrl = () => {
        return member.profile_image_url || null;
    };

    const getStatusBadge = (status) => {
        const statusClass = status === 'active' ? 'status-active' : 'status-draft';
        const statusText = status === 'active' 
            ? __('Active', 'dom5-member-directory')
            : __('Draft', 'dom5-member-directory');
        
        return (
            <span className={`dom5md-status-badge ${statusClass}`}>
                {statusText}
            </span>
        );
    };

    const handleEdit = () => {
        onEdit(member);
    };

    const handleDelete = () => {
        onDelete(member.id);
    };

    console.info(member);

    return (
        <div className="dom5md-member-card">
            <div className="member-card-header">
                {getProfileImageUrl() && (
                    <div className="member-avatar">
                        <img
                            src={getProfileImageUrl()}
                            alt={`${member.first_name} ${member.last_name}`}
                            onError={(e) => {
                                e.target.style.display = 'none';
                            }}
                        />
                    </div>
                )}
                {!getProfileImageUrl() && (
                    <div className="member-avatar-placeholder">
                        <span className="avatar-initials">
                            {member.first_name.charAt(0)}{member.last_name.charAt(0)}
                        </span>
                    </div>
                )}
            </div>

            <div className="member-card-body">
                <h3 className="member-name">
                    {member.first_name} {member.last_name}
                </h3>
                
                <p className="member-email">
                    <a href={`mailto:${member.email}`}>{member.email}</a>
                </p>

                {member.address && (
                    <p className="member-address">
                        <span className="dashicons dashicons-location"></span>
                        {member.address}
                    </p>
                )}

                {member.favorite_color && (
                    <div className="member-color">
                        <span className="color-label">
                            {__('Favorite Color:', 'dom5-member-directory')}
                        </span>
                        <span 
                            className="color-swatch"
                            style={{ backgroundColor: member.favorite_color }}
                            title={member.favorite_color}
                        ></span>
                    </div>
                )}

                <div className="member-meta">
                    {getStatusBadge(member.status)}
                    <span className="member-slug">
                        /{member.slug}
                    </span>
                </div>

                <div className="member-dates">
                    <small>
                        {__('Created:', 'dom5-member-directory')} {new Date(member.created_at).toLocaleDateString()}
                    </small>
                </div>
            </div>

            <div className="member-card-actions">
                <button 
                    className="button button-small"
                    onClick={handleEdit}
                    title={__('Edit Member', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-edit"></span>
                    {__('Edit', 'dom5-member-directory')}
                </button>
                
                <button 
                    className="button button-small button-link-delete"
                    onClick={handleDelete}
                    title={__('Delete Member', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-trash"></span>
                    {__('Delete', 'dom5-member-directory')}
                </button>

                <a
                    href={`${window.dom5MemberDirectory?.singleMemberPageUrl || '/single-member'}?member=${member.slug}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="button button-small"
                    title={__('View Member Page', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-external"></span>
                    {__('View', 'dom5-member-directory')}
                </a>
            </div>
        </div>
    );
};

export default MemberCard;
