import { __ } from '@wordpress/i18n';
import MemberCard from './MemberCard';
import Pagination from '../../../utils/Pagination';

const MembersList = ({ 
    members, 
    loading, 
    currentPage, 
    totalPages, 
    onPageChange, 
    onEdit, 
    onDelete 
}) => {
    if (loading) {
        return (
            <div className="dom5md-loading">
                <p>{__('Loading members...', 'dom5-member-directory')}</p>
            </div>
        );
    }

    if (!members || members.length === 0) {
        return (
            <div className="dom5md-no-results">
                <p>{__('No members found.', 'dom5-member-directory')}</p>
            </div>
        );
    }

    return (
        <div className="dom5md-members-list">
            <div className="dom5md-members-grid">
                {members.map(member => (
                    <MemberCard
                        key={member.id}
                        member={member}
                        onEdit={onEdit}
                        onDelete={onDelete}
                    />
                ))}
            </div>

            {totalPages > 1 && (
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                />
            )}
        </div>
    );
};

export default MembersList;
