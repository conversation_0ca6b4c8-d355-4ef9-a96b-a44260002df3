import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';

const MemberForm = ({ member, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
        first_name: '',
        last_name: '',
        email: '',
        profile_image_id: '',
        cover_image_id: '',
        address: '',
        favorite_color: '#000000',
        status: 'draft'
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const [teams, setTeams] = useState([]);
    const [selectedTeams, setSelectedTeams] = useState([]);
    const [loadingTeams, setLoadingTeams] = useState(true);

    // Fetch teams and populate form data
    useEffect(() => {
        fetchTeams();

        if (member) {
            setFormData({
                first_name: member.first_name || '',
                last_name: member.last_name || '',
                email: member.email || '',
                profile_image_id: member.profile_image_id || '',
                cover_image_id: member.cover_image_id || '',
                address: member.address || '',
                favorite_color: member.favorite_color || '#000000',
                status: member.status || 'draft'
            });

            // Fetch member teams if editing
            fetchMemberTeams();
        }
    }, [member]);

    const fetchTeams = async () => {
        try {
            const response = await apiFetch({
                path: '/dom5md/v1/teams?per_page=100',
                method: 'GET',
            });
            setTeams(response.teams || []);
        } catch (err) {
            console.error('Failed to fetch teams:', err);
        } finally {
            setLoadingTeams(false);
        }
    };

    const fetchMemberTeams = async () => {
        if (!member?.id) return;

        try {
            const response = await apiFetch({
                path: `/dom5md/v1/members/${member.id}/teams`,
                method: 'GET',
            });
            setSelectedTeams(response.teams?.map(team => team.id) || []);
        } catch (err) {
            console.error('Failed to fetch member teams:', err);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // Clear error for this field
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.first_name.trim()) {
            newErrors.first_name = __('First name is required', 'dom5-member-directory');
        }

        if (!formData.last_name.trim()) {
            newErrors.last_name = __('Last name is required', 'dom5-member-directory');
        }

        if (!formData.email.trim()) {
            newErrors.email = __('Email is required', 'dom5-member-directory');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = __('Please enter a valid email address', 'dom5-member-directory');
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const memberData = await onSubmit(formData);

            // Update team associations if member was created/updated successfully
            if (memberData?.member?.id || member?.id) {
                const memberId = memberData?.member?.id || member.id;
                await updateMemberTeams(memberId);
            }
        } catch (error) {
            setErrors({ submit: error.message });
        } finally {
            setLoading(false);
        }
    };

    const updateMemberTeams = async (memberId) => {
        try {
            // Get current member teams
            const currentResponse = await apiFetch({
                path: `/dom5md/v1/members/${memberId}/teams`,
                method: 'GET',
            });
            const currentTeamIds = currentResponse.teams?.map(team => team.id) || [];

            // Remove teams that are no longer selected
            for (const teamId of currentTeamIds) {
                if (!selectedTeams.includes(teamId)) {
                    await apiFetch({
                        path: `/dom5md/v1/members/${memberId}/teams/${teamId}`,
                        method: 'DELETE',
                    });
                }
            }

            // Add newly selected teams
            for (const teamId of selectedTeams) {
                if (!currentTeamIds.includes(teamId)) {
                    await apiFetch({
                        path: `/dom5md/v1/members/${memberId}/teams`,
                        method: 'POST',
                        data: { team_id: teamId },
                    });
                }
            }
        } catch (error) {
            console.error('Failed to update member teams:', error);
        }
    };

    const openMediaLibrary = (field) => {
        if (typeof wp !== 'undefined' && wp.media) {
            const mediaUploader = wp.media({
                title: __('Select Image', 'dom5-member-directory'),
                button: {
                    text: __('Use this image', 'dom5-member-directory')
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                setFormData(prev => ({
                    ...prev,
                    [field]: attachment.id
                }));
            });

            mediaUploader.open();
        }
    };

    const removeImage = (field) => {
        setFormData(prev => ({
            ...prev,
            [field]: ''
        }));
    };

    const getImagePreview = (imageId) => {
        if (!imageId) return null;
        // This would typically come from WordPress media library
        return `${window.dom5MemberDirectory.siteUrl}/wp-content/uploads/${imageId}`;
    };

    return (
        <div className="dom5md-member-form-overlay">
            <div className="dom5md-member-form-modal">
                <div className="modal-header">
                    <h2>
                        {member 
                            ? __('Edit Member', 'dom5-member-directory')
                            : __('Add New Member', 'dom5-member-directory')
                        }
                    </h2>
                    <button 
                        className="modal-close"
                        onClick={onCancel}
                        type="button"
                    >
                        <span className="dashicons dashicons-no"></span>
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="dom5md-member-form">
                    {errors.submit && (
                        <div className="notice notice-error">
                            <p>{errors.submit}</p>
                        </div>
                    )}

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="first_name">
                                {__('First Name', 'dom5-member-directory')} *
                            </label>
                            <input
                                type="text"
                                id="first_name"
                                name="first_name"
                                value={formData.first_name}
                                onChange={handleInputChange}
                                className={errors.first_name ? 'error' : ''}
                                required
                            />
                            {errors.first_name && (
                                <span className="error-message">{errors.first_name}</span>
                            )}
                        </div>

                        <div className="form-group">
                            <label htmlFor="last_name">
                                {__('Last Name', 'dom5-member-directory')} *
                            </label>
                            <input
                                type="text"
                                id="last_name"
                                name="last_name"
                                value={formData.last_name}
                                onChange={handleInputChange}
                                className={errors.last_name ? 'error' : ''}
                                required
                            />
                            {errors.last_name && (
                                <span className="error-message">{errors.last_name}</span>
                            )}
                        </div>
                    </div>

                    <div className="form-group">
                        <label htmlFor="email">
                            {__('Email Address', 'dom5-member-directory')} *
                        </label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className={errors.email ? 'error' : ''}
                            required
                        />
                        {errors.email && (
                            <span className="error-message">{errors.email}</span>
                        )}
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label>{__('Profile Image', 'dom5-member-directory')}</label>
                            <div className="image-upload-field">
                                {formData.profile_image_id && (
                                    <div className="image-preview">
                                        <img 
                                            src={getImagePreview(formData.profile_image_id)} 
                                            alt={__('Profile Image', 'dom5-member-directory')}
                                        />
                                        <button 
                                            type="button"
                                            className="remove-image"
                                            onClick={() => removeImage('profile_image_id')}
                                        >
                                            <span className="dashicons dashicons-no"></span>
                                        </button>
                                    </div>
                                )}
                                <button 
                                    type="button"
                                    className="button"
                                    onClick={() => openMediaLibrary('profile_image_id')}
                                >
                                    {formData.profile_image_id 
                                        ? __('Change Image', 'dom5-member-directory')
                                        : __('Select Image', 'dom5-member-directory')
                                    }
                                </button>
                            </div>
                        </div>

                        <div className="form-group">
                            <label>{__('Cover Image', 'dom5-member-directory')}</label>
                            <div className="image-upload-field">
                                {formData.cover_image_id && (
                                    <div className="image-preview">
                                        <img 
                                            src={getImagePreview(formData.cover_image_id)} 
                                            alt={__('Cover Image', 'dom5-member-directory')}
                                        />
                                        <button 
                                            type="button"
                                            className="remove-image"
                                            onClick={() => removeImage('cover_image_id')}
                                        >
                                            <span className="dashicons dashicons-no"></span>
                                        </button>
                                    </div>
                                )}
                                <button 
                                    type="button"
                                    className="button"
                                    onClick={() => openMediaLibrary('cover_image_id')}
                                >
                                    {formData.cover_image_id 
                                        ? __('Change Image', 'dom5-member-directory')
                                        : __('Select Image', 'dom5-member-directory')
                                    }
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="form-group">
                        <label htmlFor="address">
                            {__('Address', 'dom5-member-directory')}
                        </label>
                        <textarea
                            id="address"
                            name="address"
                            value={formData.address}
                            onChange={handleInputChange}
                            rows="3"
                        />
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="favorite_color">
                                {__('Favorite Color', 'dom5-member-directory')}
                            </label>
                            <input
                                type="color"
                                id="favorite_color"
                                name="favorite_color"
                                value={formData.favorite_color}
                                onChange={handleInputChange}
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="status">
                                {__('Status', 'dom5-member-directory')}
                            </label>
                            <select
                                id="status"
                                name="status"
                                value={formData.status}
                                onChange={handleInputChange}
                            >
                                <option value="draft">{__('Draft', 'dom5-member-directory')}</option>
                                <option value="active">{__('Active', 'dom5-member-directory')}</option>
                            </select>
                        </div>
                    </div>

                    <div className="form-group">
                        <label>{__('Teams', 'dom5-member-directory')}</label>
                        {loadingTeams ? (
                            <p>{__('Loading teams...', 'dom5-member-directory')}</p>
                        ) : (
                            <div className="teams-selection">
                                {teams.length > 0 ? (
                                    <div className="teams-checkboxes">
                                        {teams.map(team => (
                                            <label key={team.id} className="team-checkbox">
                                                <input
                                                    type="checkbox"
                                                    checked={selectedTeams.includes(team.id)}
                                                    onChange={(e) => {
                                                        if (e.target.checked) {
                                                            setSelectedTeams(prev => [...prev, team.id]);
                                                        } else {
                                                            setSelectedTeams(prev => prev.filter(id => id !== team.id));
                                                        }
                                                    }}
                                                />
                                                <span className="team-name">{team.name}</span>
                                                {team.short_description && (
                                                    <span className="team-description">{team.short_description}</span>
                                                )}
                                            </label>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="no-teams">
                                        {__('No teams available. Create teams first to assign members.', 'dom5-member-directory')}
                                    </p>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="form-actions">
                        <button 
                            type="submit" 
                            className="button button-primary"
                            disabled={loading}
                        >
                            {loading 
                                ? __('Saving...', 'dom5-member-directory')
                                : (member 
                                    ? __('Update Member', 'dom5-member-directory')
                                    : __('Add Member', 'dom5-member-directory')
                                )
                            }
                        </button>
                        
                        <button 
                            type="button" 
                            className="button"
                            onClick={onCancel}
                            disabled={loading}
                        >
                            {__('Cancel', 'dom5-member-directory')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default MemberForm;
