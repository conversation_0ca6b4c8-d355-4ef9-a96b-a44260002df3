import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';
import MembersList from './components/MembersList';
import MemberForm from './components/MemberForm';
import SearchFilter from './components/SearchFilter';
import { useDataContext } from '../../../context/data-context';

const Members = () => {
    const { members, setMembers, loading, setLoading } = useDataContext()
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [showForm, setShowForm] = useState(false);
    const [editingMember, setEditingMember] = useState(null);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Fetch members data
    useEffect(() => {
        fetchMembers();
    }, [currentPage, searchTerm, statusFilter, refreshTrigger]);

    const fetchMembers = async (forceFetch = false) => {
        setLoading(true);
        setError(null);

        if(members.length > 0 && !forceFetch){
            setLoading(false);
            return;
        }

        try {
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '12',
                ...(searchTerm && { search: searchTerm }),
                ...(statusFilter && { status: statusFilter }),
            });

            const response = await apiFetch({
                path: `/dom5md/v1/members?${params}`,
                method: 'GET',
            });

            console.info(response.members);
            setMembers(response.members || []);
            setTotalPages(response.pages || 1);
        } catch (err) {
            setError(err.message || __('Failed to fetch members', 'dom5-member-directory'));
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (term) => {
        setSearchTerm(term);
        setCurrentPage(1);
    };

    const handleStatusFilter = (status) => {
        setStatusFilter(status);
        setCurrentPage(1);
    };

    const handleAddMember = () => {
        setEditingMember(null);
        setShowForm(true);
    };

    const handleEditMember = (member) => {
        setEditingMember(member);
        setShowForm(true);
    };

    const handleDeleteMember = async (memberId) => {
        if (!confirm(__('Are you sure you want to delete this member?', 'dom5-member-directory'))) {
            return;
        }

        try {
            await apiFetch({
                path: `/dom5md/v1/members/${memberId}`,
                method: 'DELETE',
            });

            setRefreshTrigger(prev => prev + 1);
        } catch (err) {
            alert(err.message || __('Failed to delete member', 'dom5-member-directory'));
        }
    };

    const handleFormSubmit = async (memberData) => {
        try {
            let response;
            if (editingMember) {
                response = await apiFetch({
                    path: `/dom5md/v1/members/${editingMember.id}`,
                    method: 'PUT',
                    data: memberData,
                });
            } else {
                response = await apiFetch({
                    path: '/dom5md/v1/members',
                    method: 'POST',
                    data: memberData,
                });
            }

            setShowForm(false);
            setEditingMember(null);
            setRefreshTrigger(prev => prev + 1);
            fetchMembers(true);
            return response; // Return the response so the form can access the member data
        } catch (err) {
            throw new Error(err.message || __('Failed to save member', 'dom5-member-directory'));
        }
    };

    const handleFormCancel = () => {
        setShowForm(false);
        setEditingMember(null);
    };

    return (
        <div className="dom5md-members-page">
            <div className="dom5md-page-header">
                <h1>{__('Members', 'dom5-member-directory')}</h1>
                <button
                    className="button button-primary"
                    onClick={handleAddMember}
                >
                    {__('Add New Member', 'dom5-member-directory')}
                </button>
            </div>

            {showForm && (
                <MemberForm
                    member={editingMember}
                    onSubmit={handleFormSubmit}
                    onCancel={handleFormCancel}
                />
            )}

            <SearchFilter
                searchTerm={searchTerm}
                statusFilter={statusFilter}
                onSearch={handleSearch}
                onStatusFilter={handleStatusFilter}
            />

            {error && (
                <div className="notice notice-error">
                    <p>{error}</p>
                </div>
            )}

            <MembersList
                members={members}
                loading={loading}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onEdit={handleEditMember}
                onDelete={handleDeleteMember}
            />
        </div>
    );
};

export default Members;