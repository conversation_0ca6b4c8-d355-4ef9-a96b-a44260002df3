import { Routes, Route, Outlet } from "react-router";
import MemberDirectory from "./member-directory";
import Members from "./members";
import Teams from "./teams";
import ContactSubmission from "./contact-submission";
import Settings from "./settings";
import PrimaryMenu from "../../components/utils/navbar/primary-menu";
import {__} from '@wordpress/i18n';
import { route_path } from "../../components/utils/data";

export const pages = {
    'dom5-member-directory': {
        label: __("Members Directory", 'dom5-member-directory'),
        key: "members-directory",
        component: <MemberDirectory />,
        icon: '',
    },
    'dom5-member-directory-members': {
        label: __("Members", 'dom5-member-directory'),
        key: "members",
        component: <Members />,
        icon: '',
    },
    'dom5-member-directory-teams': {
        label: __("Teams", 'dom5-member-directory'),
        key: "teams",
        component: <Teams />,
        icon: '',
    },
    'dom5-member-directory-submissions': {
        label: __("Contact Submissions", 'dom5-member-directory'),
        key: "submissions",
        component: <ContactSubmission />,
        icon: '',
    },
    'dom5-member-directory-settings': {
        label: __("Settings", 'dom5-member-directory'),
        key: "settings",
        component: <Settings />,
        icon: '',
    },
}

const DefineRoutes = ({ page }) => {
  return (
    <>
      <Routes>
        <Route path={route_path + "admin.php"} element={<Layout page={page} />}>
          <Route index element={pages[page]?.component} />
        </Route>
      </Routes>
    </>
  );
};

export default DefineRoutes;
export const Layout = ({ page }) => {
    return (
        <>
            <PrimaryMenu />
            <div>
                <Outlet />
            </div>
        </>
    )
}