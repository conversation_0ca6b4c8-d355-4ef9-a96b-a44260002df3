import { __ } from '@wordpress/i18n';
import SubmissionCard from './SubmissionCard';
import Pagination from '../../../utils/Pagination';

const SubmissionsList = ({ 
    submissions, 
    loading, 
    currentPage, 
    totalPages, 
    onPageChange, 
    onView, 
    onUpdateStatus, 
    onDelete,
    selectedSubmissionId 
}) => {
    if (loading) {
        return (
            <div className="dom5md-loading">
                <p>{__('Loading submissions...', 'dom5-member-directory')}</p>
            </div>
        );
    }

    if (!submissions || submissions.length === 0) {
        return (
            <div className="dom5md-no-results">
                <div className="no-results-content">
                    <span className="dashicons dashicons-email"></span>
                    <h3>{__('No submissions found', 'dom5-member-directory')}</h3>
                    <p>{__('Contact form submissions will appear here when visitors send messages to your members.', 'dom5-member-directory')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="dom5md-submissions-list">
            <div className="submissions-header">
                <h2>
                    {__('Submissions', 'dom5-member-directory')} 
                    <span className="count">({submissions.length})</span>
                </h2>
            </div>

            <div className="submissions-grid">
                {submissions.map(submission => (
                    <SubmissionCard
                        key={submission.id}
                        submission={submission}
                        onView={onView}
                        onUpdateStatus={onUpdateStatus}
                        onDelete={onDelete}
                        isSelected={submission.id === selectedSubmissionId}
                    />
                ))}
            </div>

            {totalPages > 1 && (
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                />
            )}
        </div>
    );
};

export default SubmissionsList;
