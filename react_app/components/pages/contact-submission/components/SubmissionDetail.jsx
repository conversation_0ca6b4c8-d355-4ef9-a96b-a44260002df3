import { __ } from '@wordpress/i18n';

const SubmissionDetail = ({ submission, onClose, onUpdateStatus, onDelete }) => {
    const getStatusBadge = (status) => {
        const statusConfig = {
            unread: {
                class: 'status-unread',
                text: __('Unread', 'dom5-member-directory'),
                icon: 'dashicons-marker'
            },
            read: {
                class: 'status-read',
                text: __('Read', 'dom5-member-directory'),
                icon: 'dashicons-yes'
            },
            replied: {
                class: 'status-replied',
                text: __('Replied', 'dom5-member-directory'),
                icon: 'dashicons-email-alt'
            }
        };

        const config = statusConfig[status] || statusConfig.unread;
        
        return (
            <span className={`dom5md-status-badge ${config.class}`}>
                <span className={`dashicons ${config.icon}`}></span>
                {config.text}
            </span>
        );
    };

    const handleStatusChange = (newStatus) => {
        onUpdateStatus(submission.id, newStatus);
    };

    const handleDelete = () => {
        onDelete(submission.id);
    };

    const handleReply = () => {
        const subject = `Re: ${__('Contact from Member Directory', 'dom5-member-directory')}`;
        const body = `${__('Hello', 'dom5-member-directory')} ${submission.sender_name},\n\n${__('Thank you for your message:', 'dom5-member-directory')}\n\n"${submission.message}"\n\n${__('Best regards,', 'dom5-member-directory')}\n${submission.first_name} ${submission.last_name}`;
        
        const mailtoLink = `mailto:${submission.sender_email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.open(mailtoLink);
        
        // Mark as replied
        handleStatusChange('replied');
    };

    return (
        <div className="dom5md-submission-detail">
            <div className="detail-header">
                <h2>{__('Submission Details', 'dom5-member-directory')}</h2>
                <button 
                    className="close-button"
                    onClick={onClose}
                    title={__('Close', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-no"></span>
                </button>
            </div>

            <div className="detail-content">
                <div className="submission-info">
                    <div className="info-section">
                        <h3>{__('From', 'dom5-member-directory')}</h3>
                        <div className="sender-details">
                            <p className="sender-name">
                                <strong>{submission.sender_name}</strong>
                            </p>
                            <p className="sender-email">
                                <a href={`mailto:${submission.sender_email}`}>
                                    {submission.sender_email}
                                </a>
                            </p>
                        </div>
                    </div>

                    <div className="info-section">
                        <h3>{__('To', 'dom5-member-directory')}</h3>
                        <div className="recipient-details">
                            <p className="recipient-name">
                                <strong>{submission.first_name} {submission.last_name}</strong>
                            </p>
                            <p className="recipient-email">
                                {submission.member_email}
                            </p>
                        </div>
                    </div>

                    <div className="info-section">
                        <h3>{__('Status', 'dom5-member-directory')}</h3>
                        <div className="status-section">
                            {getStatusBadge(submission.status)}
                            <div className="status-actions">
                                {submission.status !== 'read' && (
                                    <button 
                                        className="button button-small"
                                        onClick={() => handleStatusChange('read')}
                                    >
                                        {__('Mark as Read', 'dom5-member-directory')}
                                    </button>
                                )}
                                {submission.status !== 'replied' && (
                                    <button 
                                        className="button button-small"
                                        onClick={() => handleStatusChange('replied')}
                                    >
                                        {__('Mark as Replied', 'dom5-member-directory')}
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="message-section">
                    <h3>{__('Message', 'dom5-member-directory')}</h3>
                    <div className="message-content">
                        {submission.message.split('\n').map((line, index) => (
                            <p key={index}>{line}</p>
                        ))}
                    </div>
                </div>

                <div className="metadata-section">
                    <h3>{__('Additional Information', 'dom5-member-directory')}</h3>
                    <div className="metadata-grid">
                        <div className="metadata-item">
                            <label>{__('Submitted:', 'dom5-member-directory')}</label>
                            <span>{new Date(submission.created_at).toLocaleString()}</span>
                        </div>
                        
                        {submission.ip_address && (
                            <div className="metadata-item">
                                <label>{__('IP Address:', 'dom5-member-directory')}</label>
                                <span>{submission.ip_address}</span>
                            </div>
                        )}
                        
                        {submission.user_agent && (
                            <div className="metadata-item">
                                <label>{__('User Agent:', 'dom5-member-directory')}</label>
                                <span className="user-agent">{submission.user_agent}</span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <div className="detail-actions">
                <button 
                    className="button button-primary"
                    onClick={handleReply}
                >
                    <span className="dashicons dashicons-email"></span>
                    {__('Reply via Email', 'dom5-member-directory')}
                </button>

                <button 
                    className="button button-secondary"
                    onClick={() => {
                        navigator.clipboard.writeText(submission.sender_email);
                        alert(__('Email copied to clipboard!', 'dom5-member-directory'));
                    }}
                >
                    <span className="dashicons dashicons-clipboard"></span>
                    {__('Copy Email', 'dom5-member-directory')}
                </button>

                <button 
                    className="button button-link-delete"
                    onClick={handleDelete}
                >
                    <span className="dashicons dashicons-trash"></span>
                    {__('Delete', 'dom5-member-directory')}
                </button>
            </div>
        </div>
    );
};

export default SubmissionDetail;
