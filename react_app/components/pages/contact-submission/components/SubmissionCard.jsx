import { __ } from '@wordpress/i18n';

const SubmissionCard = ({ submission, onView, onUpdateStatus, onDelete, isSelected }) => {
    const getStatusBadge = (status) => {
        const statusConfig = {
            unread: {
                class: 'status-unread',
                text: __('Unread', 'dom5-member-directory'),
                icon: 'dashicons-marker'
            },
            read: {
                class: 'status-read',
                text: __('Read', 'dom5-member-directory'),
                icon: 'dashicons-yes'
            },
            replied: {
                class: 'status-replied',
                text: __('Replied', 'dom5-member-directory'),
                icon: 'dashicons-email-alt'
            }
        };

        const config = statusConfig[status] || statusConfig.unread;
        
        return (
            <span className={`dom5md-status-badge ${config.class}`}>
                <span className={`dashicons ${config.icon}`}></span>
                {config.text}
            </span>
        );
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return __('Today', 'dom5-member-directory');
        } else if (diffDays === 2) {
            return __('Yesterday', 'dom5-member-directory');
        } else if (diffDays <= 7) {
            return `${diffDays - 1} ${__('days ago', 'dom5-member-directory')}`;
        } else {
            return date.toLocaleDateString();
        }
    };

    const handleView = () => {
        onView(submission);
    };

    const handleStatusChange = (newStatus) => {
        onUpdateStatus(submission.id, newStatus);
    };

    const handleDelete = (e) => {
        e.stopPropagation();
        onDelete(submission.id);
    };

    const truncateMessage = (message, maxLength = 100) => {
        if (message.length <= maxLength) return message;
        return message.substring(0, maxLength) + '...';
    };

    return (
        <div 
            className={`dom5md-submission-card ${isSelected ? 'selected' : ''} ${submission.status}`}
            onClick={handleView}
        >
            <div className="submission-card-header">
                <div className="sender-info">
                    <h3 className="sender-name">{submission.sender_name}</h3>
                    <p className="sender-email">
                        <a 
                            href={`mailto:${submission.sender_email}`}
                            onClick={(e) => e.stopPropagation()}
                        >
                            {submission.sender_email}
                        </a>
                    </p>
                </div>
                {getStatusBadge(submission.status)}
            </div>

            <div className="submission-card-body">
                <div className="recipient-info">
                    <span className="recipient-label">
                        {__('To:', 'dom5-member-directory')}
                    </span>
                    <span className="recipient-name">
                        {submission.first_name} {submission.last_name}
                    </span>
                </div>

                <div className="message-preview">
                    {truncateMessage(submission.message)}
                </div>

                <div className="submission-meta">
                    <span className="submission-date">
                        <span className="dashicons dashicons-clock"></span>
                        {formatDate(submission.created_at)}
                    </span>
                    
                    {submission.ip_address && (
                        <span className="submission-ip">
                            <span className="dashicons dashicons-location"></span>
                            {submission.ip_address}
                        </span>
                    )}
                </div>
            </div>

            <div className="submission-card-actions" onClick={(e) => e.stopPropagation()}>
                {submission.status === 'unread' && (
                    <button 
                        className="button button-small"
                        onClick={() => handleStatusChange('read')}
                        title={__('Mark as Read', 'dom5-member-directory')}
                    >
                        <span className="dashicons dashicons-yes"></span>
                    </button>
                )}

                {submission.status === 'read' && (
                    <button 
                        className="button button-small"
                        onClick={() => handleStatusChange('replied')}
                        title={__('Mark as Replied', 'dom5-member-directory')}
                    >
                        <span className="dashicons dashicons-email-alt"></span>
                    </button>
                )}

                <button 
                    className="button button-small button-link-delete"
                    onClick={handleDelete}
                    title={__('Delete Submission', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-trash"></span>
                </button>
            </div>
        </div>
    );
};

export default SubmissionCard;
