import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';
import SubmissionsList from './components/SubmissionsList';
import SubmissionDetail from './components/SubmissionDetail';
import SearchFilter from './components/SearchFilter';

const ContactSubmission = () => {
    const [submissions, setSubmissions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [selectedSubmission, setSelectedSubmission] = useState(null);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Fetch submissions data
    useEffect(() => {
        fetchSubmissions();
    }, [currentPage, searchTerm, statusFilter, refreshTrigger]);

    const fetchSubmissions = async () => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '20',
                ...(searchTerm && { search: searchTerm }),
                ...(statusFilter && { status: statusFilter }),
            });

            const response = await apiFetch({
                path: `/dom5md/v1/submissions?${params}`,
                method: 'GET',
            });

            setSubmissions(response.submissions || []);
            setTotalPages(response.pages || 1);
        } catch (err) {
            setError(err.message || __('Failed to fetch submissions', 'dom5-member-directory'));
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (term) => {
        setSearchTerm(term);
        setCurrentPage(1);
    };

    const handleStatusFilter = (status) => {
        setStatusFilter(status);
        setCurrentPage(1);
    };

    const handleViewSubmission = (submission) => {
        setSelectedSubmission(submission);

        // Mark as read if it's unread
        if (submission.status === 'unread') {
            handleUpdateStatus(submission.id, 'read');
        }
    };

    const handleUpdateStatus = async (submissionId, newStatus) => {
        try {
            await apiFetch({
                path: `/dom5md/v1/submissions/${submissionId}`,
                method: 'PUT',
                data: { status: newStatus },
            });

            setRefreshTrigger(prev => prev + 1);

            // Update selected submission if it's the one being updated
            if (selectedSubmission && selectedSubmission.id === submissionId) {
                setSelectedSubmission(prev => ({
                    ...prev,
                    status: newStatus
                }));
            }
        } catch (err) {
            alert(err.message || __('Failed to update submission status', 'dom5-member-directory'));
        }
    };

    const handleDeleteSubmission = async (submissionId) => {
        if (!confirm(__('Are you sure you want to delete this submission?', 'dom5-member-directory'))) {
            return;
        }

        try {
            await apiFetch({
                path: `/dom5md/v1/submissions/${submissionId}`,
                method: 'DELETE',
            });

            setRefreshTrigger(prev => prev + 1);

            // Close detail view if the deleted submission was selected
            if (selectedSubmission && selectedSubmission.id === submissionId) {
                setSelectedSubmission(null);
            }
        } catch (err) {
            alert(err.message || __('Failed to delete submission', 'dom5-member-directory'));
        }
    };

    const handleCloseDetail = () => {
        setSelectedSubmission(null);
    };

    return (
        <div className="dom5md-submissions-page">
            <div className="dom5md-page-header">
                <h1>{__('Contact Submissions', 'dom5-member-directory')}</h1>
                <p className="page-description">
                    {__('Manage contact form submissions from your member directory.', 'dom5-member-directory')}
                </p>
            </div>

            <SearchFilter
                searchTerm={searchTerm}
                statusFilter={statusFilter}
                onSearch={handleSearch}
                onStatusFilter={handleStatusFilter}
            />

            {error && (
                <div className="notice notice-error">
                    <p>{error}</p>
                </div>
            )}

            <div className="submissions-layout">
                <div className="submissions-list-container">
                    <SubmissionsList
                        submissions={submissions}
                        loading={loading}
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={setCurrentPage}
                        onView={handleViewSubmission}
                        onUpdateStatus={handleUpdateStatus}
                        onDelete={handleDeleteSubmission}
                        selectedSubmissionId={selectedSubmission?.id}
                    />
                </div>

                {selectedSubmission && (
                    <div className="submission-detail-container">
                        <SubmissionDetail
                            submission={selectedSubmission}
                            onClose={handleCloseDetail}
                            onUpdateStatus={handleUpdateStatus}
                            onDelete={handleDeleteSubmission}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ContactSubmission;