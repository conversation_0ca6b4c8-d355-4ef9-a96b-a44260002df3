import { __ } from '@wordpress/i18n';
import { route_path } from '../../../utils/data';

const QuickActions = () => {
    const quickActions = [
        {
            title: __('Add New Member', 'dom5-member-directory'),
            description: __('Create a new member profile', 'dom5-member-directory'),
            icon: 'dashicons-plus-alt',
            link: `${route_path}admin.php?page=dom5-member-directory-members`,
            color: 'blue'
        },
        {
            title: __('Add New Team', 'dom5-member-directory'),
            description: __('Create a new team', 'dom5-member-directory'),
            icon: 'dashicons-networking',
            link: `${route_path}admin.php?page=dom5-member-directory-teams`,
            color: 'green'
        },
        {
            title: __('View Submissions', 'dom5-member-directory'),
            description: __('Check contact form submissions', 'dom5-member-directory'),
            icon: 'dashicons-email',
            link: `${route_path}admin.php?page=dom5-member-directory-submissions`,
            color: 'orange'
        },
        {
            title: __('Plugin Settings', 'dom5-member-directory'),
            description: __('Configure plugin options', 'dom5-member-directory'),
            icon: 'dashicons-admin-settings',
            link: `${route_path}admin.php?page=dom5-member-directory-settings`,
            color: 'purple'
        }
    ];

    const handleActionClick = (link) => {
        window.location.href = link;
    };

    return (
        <div className="dom5md-quick-actions">
            <h2>{__('Quick Actions', 'dom5-member-directory')}</h2>
            
            <div className="actions-list">
                {quickActions.map((action, index) => (
                    <button
                        key={index}
                        className={`action-card ${action.color}`}
                        onClick={() => handleActionClick(action.link)}
                    >
                        <div className="action-icon">
                            <span className={`dashicons ${action.icon}`}></span>
                        </div>
                        <div className="action-content">
                            <h3>{action.title}</h3>
                            <p>{action.description}</p>
                        </div>
                        <div className="action-arrow">
                            <span className="dashicons dashicons-arrow-right-alt2"></span>
                        </div>
                    </button>
                ))}
            </div>

            <div className="help-section">
                <h3>{__('Need Help?', 'dom5-member-directory')}</h3>
                <p>{__('Check out our documentation for detailed guides and tutorials.', 'dom5-member-directory')}</p>
                <a 
                    href="https://dom5.com/member-directory-docs" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="button button-secondary"
                >
                    <span className="dashicons dashicons-external"></span>
                    {__('View Documentation', 'dom5-member-directory')}
                </a>
            </div>
        </div>
    );
};

export default QuickActions;
