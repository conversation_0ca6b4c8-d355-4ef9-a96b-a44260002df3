import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';

const RecentActivity = () => {
    const [recentMembers, setRecentMembers] = useState([]);
    const [recentSubmissions, setRecentSubmissions] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchRecentActivity();
    }, []);

    const fetchRecentActivity = async () => {
        setLoading(true);
        
        try {
            const [membersResponse, submissionsResponse] = await Promise.all([
                apiFetch({ 
                    path: '/dom5md/v1/members?per_page=5&orderby=created_at&order=DESC' 
                }),
                apiFetch({ 
                    path: '/dom5md/v1/submissions?per_page=5&orderby=created_at&order=DESC' 
                })
            ]);

            setRecentMembers(membersResponse.members || []);
            setRecentSubmissions(submissionsResponse.submissions || []);
        } catch (err) {
            console.error('Failed to fetch recent activity:', err);
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return __('Today', 'dom5-member-directory');
        } else if (diffDays === 2) {
            return __('Yesterday', 'dom5-member-directory');
        } else if (diffDays <= 7) {
            return `${diffDays - 1} ${__('days ago', 'dom5-member-directory')}`;
        } else {
            return date.toLocaleDateString();
        }
    };

    if (loading) {
        return (
            <div className="dom5md-recent-activity">
                <h2>{__('Recent Activity', 'dom5-member-directory')}</h2>
                <div className="activity-loading">
                    <p>{__('Loading recent activity...', 'dom5-member-directory')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="dom5md-recent-activity">
            <h2>{__('Recent Activity', 'dom5-member-directory')}</h2>
            
            <div className="activity-sections">
                <div className="activity-section">
                    <h3>
                        <span className="dashicons dashicons-groups"></span>
                        {__('Recent Members', 'dom5-member-directory')}
                    </h3>
                    
                    {recentMembers.length > 0 ? (
                        <ul className="activity-list">
                            {recentMembers.map(member => (
                                <li key={member.id} className="activity-item">
                                    <div className="activity-content">
                                        <strong>{member.first_name} {member.last_name}</strong>
                                        <span className="activity-meta">
                                            {member.email} • {formatDate(member.created_at)}
                                        </span>
                                    </div>
                                    <span className={`status-badge ${member.status}`}>
                                        {member.status === 'active' 
                                            ? __('Active', 'dom5-member-directory')
                                            : __('Draft', 'dom5-member-directory')
                                        }
                                    </span>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <p className="no-activity">
                            {__('No recent members', 'dom5-member-directory')}
                        </p>
                    )}
                </div>

                <div className="activity-section">
                    <h3>
                        <span className="dashicons dashicons-email"></span>
                        {__('Recent Contact Submissions', 'dom5-member-directory')}
                    </h3>
                    
                    {recentSubmissions.length > 0 ? (
                        <ul className="activity-list">
                            {recentSubmissions.map(submission => (
                                <li key={submission.id} className="activity-item">
                                    <div className="activity-content">
                                        <strong>{submission.sender_name}</strong>
                                        <span className="activity-meta">
                                            {__('to', 'dom5-member-directory')} {submission.first_name} {submission.last_name} • {formatDate(submission.created_at)}
                                        </span>
                                        <div className="submission-preview">
                                            {submission.message.length > 100 
                                                ? submission.message.substring(0, 100) + '...'
                                                : submission.message
                                            }
                                        </div>
                                    </div>
                                    <span className={`status-badge ${submission.status}`}>
                                        {submission.status === 'unread' && __('Unread', 'dom5-member-directory')}
                                        {submission.status === 'read' && __('Read', 'dom5-member-directory')}
                                        {submission.status === 'replied' && __('Replied', 'dom5-member-directory')}
                                    </span>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <p className="no-activity">
                            {__('No recent submissions', 'dom5-member-directory')}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
};

export default RecentActivity;
