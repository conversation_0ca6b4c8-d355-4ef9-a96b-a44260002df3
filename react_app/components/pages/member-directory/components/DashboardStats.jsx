import { __ } from '@wordpress/i18n';

const DashboardStats = ({ stats, loading }) => {
    const statItems = [
        {
            key: 'totalMembers',
            label: __('Total Members', 'dom5-member-directory'),
            value: stats.totalMembers,
            icon: 'dashicons-groups',
            color: 'blue'
        },
        {
            key: 'activeMembers',
            label: __('Active Members', 'dom5-member-directory'),
            value: stats.activeMembers,
            icon: 'dashicons-yes-alt',
            color: 'green'
        },
        {
            key: 'draftMembers',
            label: __('Draft Members', 'dom5-member-directory'),
            value: stats.draftMembers,
            icon: 'dashicons-edit',
            color: 'orange'
        },
        {
            key: 'totalTeams',
            label: __('Total Teams', 'dom5-member-directory'),
            value: stats.totalTeams,
            icon: 'dashicons-networking',
            color: 'purple'
        },
        {
            key: 'totalSubmissions',
            label: __('Contact Submissions', 'dom5-member-directory'),
            value: stats.totalSubmissions,
            icon: 'dashicons-email',
            color: 'teal'
        },
        {
            key: 'unreadSubmissions',
            label: __('Unread Submissions', 'dom5-member-directory'),
            value: stats.unreadSubmissions,
            icon: 'dashicons-email-alt',
            color: 'red'
        }
    ];

    if (loading) {
        return (
            <div className="dom5md-dashboard-stats">
                <h2>{__('Overview', 'dom5-member-directory')}</h2>
                <div className="stats-grid">
                    {Array.from({ length: 6 }).map((_, index) => (
                        <div key={index} className="stat-card loading">
                            <div className="stat-icon">
                                <span className="dashicons dashicons-update"></span>
                            </div>
                            <div className="stat-content">
                                <div className="stat-value">...</div>
                                <div className="stat-label">{__('Loading...', 'dom5-member-directory')}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="dom5md-dashboard-stats">
            <h2>{__('Overview', 'dom5-member-directory')}</h2>
            <div className="stats-grid">
                {statItems.map(item => (
                    <div key={item.key} className={`stat-card ${item.color}`}>
                        <div className="stat-icon">
                            <span className={`dashicons ${item.icon}`}></span>
                        </div>
                        <div className="stat-content">
                            <div className="stat-value">{item.value}</div>
                            <div className="stat-label">{item.label}</div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default DashboardStats;
