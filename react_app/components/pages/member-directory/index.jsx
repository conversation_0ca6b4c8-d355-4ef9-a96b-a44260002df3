import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';
import DashboardStats from './components/DashboardStats';
import RecentActivity from './components/RecentActivity';
import QuickActions from './components/QuickActions';

const MemberDirectory = () => {
    const [stats, setStats] = useState({
        totalMembers: 0,
        activeMembers: 0,
        draftMembers: 0,
        totalTeams: 0,
        totalSubmissions: 0,
        unreadSubmissions: 0
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Fetch members stats
            const [membersResponse, teamsResponse, submissionsResponse] = await Promise.all([
                apiFetch({ path: '/dom5md/v1/members?per_page=1' }),
                apiFetch({ path: '/dom5md/v1/teams?per_page=1' }),
                apiFetch({ path: '/dom5md/v1/submissions?per_page=1' })
            ]);

            // Fetch active/draft members separately
            const [activeMembersResponse, draftMembersResponse] = await Promise.all([
                apiFetch({ path: '/dom5md/v1/members?status=active&per_page=1' }),
                apiFetch({ path: '/dom5md/v1/members?status=draft&per_page=1' })
            ]);

            // Fetch unread submissions
            const unreadSubmissionsResponse = await apiFetch({
                path: '/dom5md/v1/submissions?status=unread&per_page=1'
            });

            setStats({
                totalMembers: membersResponse.total || 0,
                activeMembers: activeMembersResponse.total || 0,
                draftMembers: draftMembersResponse.total || 0,
                totalTeams: teamsResponse.total || 0,
                totalSubmissions: submissionsResponse.total || 0,
                unreadSubmissions: unreadSubmissionsResponse.total || 0
            });
        } catch (err) {
            setError(err.message || __('Failed to fetch dashboard data', 'dom5-member-directory'));
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="dom5md-dashboard">
            <div className="dom5md-page-header">
                <h1>{__('Member Directory Dashboard', 'dom5-member-directory')}</h1>
                <p className="page-description">
                    {__('Welcome to the DOM5 Member Directory. Manage your members, teams, and contact submissions from here.', 'dom5-member-directory')}
                </p>
            </div>

            {error && (
                <div className="notice notice-error">
                    <p>{error}</p>
                </div>
            )}

            <div className="dashboard-content">
                <div className="dashboard-main">
                    <DashboardStats
                        stats={stats}
                        loading={loading}
                    />

                    <RecentActivity />
                </div>

                <div className="dashboard-sidebar">
                    <QuickActions />
                </div>
            </div>
        </div>
    );
};

export default MemberDirectory;