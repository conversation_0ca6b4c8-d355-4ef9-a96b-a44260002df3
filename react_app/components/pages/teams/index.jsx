import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';
import TeamsList from './components/TeamsList';
import TeamForm from './components/TeamForm';
import SearchFilter from './components/SearchFilter';

const Teams = () => {
    const [teams, setTeams] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [showForm, setShowForm] = useState(false);
    const [editingTeam, setEditingTeam] = useState(null);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Fetch teams data
    useEffect(() => {
        fetchTeams();
    }, [currentPage, searchTerm, refreshTrigger]);

    const fetchTeams = async () => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '12',
                ...(searchTerm && { search: searchTerm }),
            });

            const response = await apiFetch({
                path: `/dom5md/v1/teams?${params}`,
                method: 'GET',
            });

            setTeams(response.teams || []);
            setTotalPages(response.pages || 1);
        } catch (err) {
            setError(err.message || __('Failed to fetch teams', 'dom5-member-directory'));
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (term) => {
        setSearchTerm(term);
        setCurrentPage(1);
    };

    const handleAddTeam = () => {
        setEditingTeam(null);
        setShowForm(true);
    };

    const handleEditTeam = (team) => {
        setEditingTeam(team);
        setShowForm(true);
    };

    const handleDeleteTeam = async (teamId) => {
        if (!confirm(__('Are you sure you want to delete this team?', 'dom5-member-directory'))) {
            return;
        }

        try {
            await apiFetch({
                path: `/dom5md/v1/teams/${teamId}`,
                method: 'DELETE',
            });

            setRefreshTrigger(prev => prev + 1);
        } catch (err) {
            alert(err.message || __('Failed to delete team', 'dom5-member-directory'));
        }
    };

    const handleFormSubmit = async (teamData) => {
        try {
            if (editingTeam) {
                await apiFetch({
                    path: `/dom5md/v1/teams/${editingTeam.id}`,
                    method: 'PUT',
                    data: teamData,
                });
            } else {
                await apiFetch({
                    path: '/dom5md/v1/teams',
                    method: 'POST',
                    data: teamData,
                });
            }

            setShowForm(false);
            setEditingTeam(null);
            setRefreshTrigger(prev => prev + 1);
        } catch (err) {
            throw new Error(err.message || __('Failed to save team', 'dom5-member-directory'));
        }
    };

    const handleFormCancel = () => {
        setShowForm(false);
        setEditingTeam(null);
    };

    return (
        <div className="dom5md-teams-page">
            <div className="dom5md-page-header">
                <h1>{__('Teams', 'dom5-member-directory')}</h1>
                <button
                    className="button button-primary"
                    onClick={handleAddTeam}
                >
                    {__('Add New Team', 'dom5-member-directory')}
                </button>
            </div>

            {showForm && (
                <TeamForm
                    team={editingTeam}
                    onSubmit={handleFormSubmit}
                    onCancel={handleFormCancel}
                />
            )}

            <SearchFilter
                searchTerm={searchTerm}
                onSearch={handleSearch}
            />

            {error && (
                <div className="notice notice-error">
                    <p>{error}</p>
                </div>
            )}

            <TeamsList
                teams={teams}
                loading={loading}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                onEdit={handleEditTeam}
                onDelete={handleDeleteTeam}
            />
        </div>
    );
};

export default Teams;