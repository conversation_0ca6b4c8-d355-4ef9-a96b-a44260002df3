import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';

const SearchFilter = ({ searchTerm, onSearch }) => {
    const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

    // Debounce search input
    useEffect(() => {
        const timer = setTimeout(() => {
            onSearch(localSearchTerm);
        }, 500);

        return () => clearTimeout(timer);
    }, [localSearchTerm, onSearch]);

    const handleSearchChange = (e) => {
        setLocalSearchTerm(e.target.value);
    };

    const clearSearch = () => {
        setLocalSearchTerm('');
    };

    return (
        <div className="dom5md-search-filter">
            <div className="filter-row">
                <div className="search-group">
                    <label htmlFor="team-search" className="screen-reader-text">
                        {__('Search Teams', 'dom5-member-directory')}
                    </label>
                    <input
                        type="text"
                        id="team-search"
                        className="search-input"
                        placeholder={__('Search teams by name or description...', 'dom5-member-directory')}
                        value={localSearchTerm}
                        onChange={handleSearchChange}
                    />
                    <span className="search-icon dashicons dashicons-search"></span>
                </div>

                {localSearchTerm && (
                    <button 
                        className="button clear-filters"
                        onClick={clearSearch}
                        title={__('Clear search', 'dom5-member-directory')}
                    >
                        <span className="dashicons dashicons-dismiss"></span>
                        {__('Clear', 'dom5-member-directory')}
                    </button>
                )}
            </div>

            {localSearchTerm && (
                <div className="active-filters">
                    <span className="filters-label">
                        {__('Searching for:', 'dom5-member-directory')}
                    </span>
                    
                    <span className="filter-tag">
                        "{localSearchTerm}"
                        <button 
                            onClick={clearSearch}
                            className="remove-filter"
                        >
                            <span className="dashicons dashicons-no"></span>
                        </button>
                    </span>
                </div>
            )}
        </div>
    );
};

export default SearchFilter;
