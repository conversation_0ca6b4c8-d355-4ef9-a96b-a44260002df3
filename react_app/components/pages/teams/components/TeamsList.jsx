import { __ } from '@wordpress/i18n';
import TeamCard from './TeamCard';
import Pagination from '../../../utils/Pagination';

const TeamsList = ({ 
    teams, 
    loading, 
    currentPage, 
    totalPages, 
    onPageChange, 
    onEdit, 
    onDelete 
}) => {
    if (loading) {
        return (
            <div className="dom5md-loading">
                <p>{__('Loading teams...', 'dom5-member-directory')}</p>
            </div>
        );
    }

    if (!teams || teams.length === 0) {
        return (
            <div className="dom5md-no-results">
                <p>{__('No teams found.', 'dom5-member-directory')}</p>
            </div>
        );
    }

    return (
        <div className="dom5md-teams-list">
            <div className="dom5md-teams-grid">
                {teams.map(team => (
                    <TeamCard
                        key={team.id}
                        team={team}
                        onEdit={onEdit}
                        onDelete={onDelete}
                    />
                ))}
            </div>

            {totalPages > 1 && (
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                />
            )}
        </div>
    );
};

export default TeamsList;
