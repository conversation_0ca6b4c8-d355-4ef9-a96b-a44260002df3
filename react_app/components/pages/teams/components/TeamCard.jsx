import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';
import apiFetch from '@wordpress/api-fetch';

const TeamCard = ({ team, onEdit, onDelete }) => {
    const [memberCount, setMemberCount] = useState(0);
    const [loadingMembers, setLoadingMembers] = useState(true);

    useEffect(() => {
        fetchMemberCount();
    }, [team.id]);

    const fetchMemberCount = async () => {
        try {
            const response = await apiFetch({
                path: `/dom5md/v1/teams/${team.id}/members?per_page=1`,
                method: 'GET',
            });
            setMemberCount(response.total || 0);
        } catch (err) {
            console.error('Failed to fetch member count:', err);
        } finally {
            setLoadingMembers(false);
        }
    };

    const handleEdit = () => {
        onEdit(team);
    };

    const handleDelete = () => {
        onDelete(team.id);
    };

    return (
        <div className="dom5md-team-card">
            <div className="team-card-header">
                <h3 className="team-name">{team.name}</h3>
                <span className="team-slug">/{team.slug}</span>
            </div>

            <div className="team-card-body">
                {team.short_description && (
                    <p className="team-description">
                        {team.short_description}
                    </p>
                )}

                <div className="team-stats">
                    <div className="stat-item">
                        <span className="stat-label">
                            {__('Members:', 'dom5-member-directory')}
                        </span>
                        <span className="stat-value">
                            {loadingMembers ? '...' : memberCount}
                        </span>
                    </div>
                </div>

                <div className="team-meta">
                    <small>
                        {__('Created:', 'dom5-member-directory')} {new Date(team.created_at).toLocaleDateString()}
                    </small>
                </div>
            </div>

            <div className="team-card-actions">
                <button 
                    className="button button-small"
                    onClick={handleEdit}
                    title={__('Edit Team', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-edit"></span>
                    {__('Edit', 'dom5-member-directory')}
                </button>
                
                <button 
                    className="button button-small button-link-delete"
                    onClick={handleDelete}
                    title={__('Delete Team', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-trash"></span>
                    {__('Delete', 'dom5-member-directory')}
                </button>

                <button 
                    className="button button-small"
                    title={__('Manage Members', 'dom5-member-directory')}
                    onClick={() => {
                        // TODO: Open member management modal
                        console.log('Manage members for team:', team.id);
                    }}
                >
                    <span className="dashicons dashicons-groups"></span>
                    {__('Members', 'dom5-member-directory')}
                </button>
            </div>
        </div>
    );
};

export default TeamCard;
