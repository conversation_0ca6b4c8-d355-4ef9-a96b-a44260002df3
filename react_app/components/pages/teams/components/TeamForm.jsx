import { useState, useEffect } from 'react';
import { __ } from '@wordpress/i18n';

const TeamForm = ({ team, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
        name: '',
        short_description: ''
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    // Populate form with team data if editing
    useEffect(() => {
        if (team) {
            setFormData({
                name: team.name || '',
                short_description: team.short_description || ''
            });
        }
    }, [team]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // Clear error for this field
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = __('Team name is required', 'dom5-member-directory');
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        
        try {
            await onSubmit(formData);
        } catch (error) {
            setErrors({ submit: error.message });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="dom5md-team-form-overlay">
            <div className="dom5md-team-form-modal">
                <div className="modal-header">
                    <h2>
                        {team 
                            ? __('Edit Team', 'dom5-member-directory')
                            : __('Add New Team', 'dom5-member-directory')
                        }
                    </h2>
                    <button 
                        className="modal-close"
                        onClick={onCancel}
                        type="button"
                    >
                        <span className="dashicons dashicons-no"></span>
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="dom5md-team-form">
                    {errors.submit && (
                        <div className="notice notice-error">
                            <p>{errors.submit}</p>
                        </div>
                    )}

                    <div className="form-group">
                        <label htmlFor="name">
                            {__('Team Name', 'dom5-member-directory')} *
                        </label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className={errors.name ? 'error' : ''}
                            required
                        />
                        {errors.name && (
                            <span className="error-message">{errors.name}</span>
                        )}
                    </div>

                    <div className="form-group">
                        <label htmlFor="short_description">
                            {__('Short Description', 'dom5-member-directory')}
                        </label>
                        <textarea
                            id="short_description"
                            name="short_description"
                            value={formData.short_description}
                            onChange={handleInputChange}
                            rows="4"
                            placeholder={__('Brief description of the team...', 'dom5-member-directory')}
                        />
                    </div>

                    <div className="form-actions">
                        <button 
                            type="submit" 
                            className="button button-primary"
                            disabled={loading}
                        >
                            {loading 
                                ? __('Saving...', 'dom5-member-directory')
                                : (team 
                                    ? __('Update Team', 'dom5-member-directory')
                                    : __('Add Team', 'dom5-member-directory')
                                )
                            }
                        </button>
                        
                        <button 
                            type="button" 
                            className="button"
                            onClick={onCancel}
                            disabled={loading}
                        >
                            {__('Cancel', 'dom5-member-directory')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default TeamForm;
