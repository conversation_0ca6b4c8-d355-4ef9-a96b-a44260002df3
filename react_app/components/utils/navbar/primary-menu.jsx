import { useQuery } from "../../../App";
import { pages } from "../../../components/pages";
import { Link } from "react-router";
import { route_path } from "../data";

const PrimaryMenu = () => {
    const query = useQuery();
    const page = query.get("page") ?? "dnxte-essential";
    return ( 
        <>
            <h1>DOM5 Member Directory Plugin</h1>
            <ul>
                {Object.entries(pages).map(([key, value]) => (
                    <li className={key === page ? "active" : ""} key={key}> 
                       <Link to={route_path + `admin.php?page=${key}`}>
                        {value.label}
                        </Link>
                    </li>
                ))}
            </ul>
        </>
     );
}
 
export default PrimaryMenu;