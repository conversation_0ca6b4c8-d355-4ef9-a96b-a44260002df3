import { __ } from '@wordpress/i18n';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
    if (totalPages <= 1) {
        return null;
    }

    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 7;
        
        if (totalPages <= maxVisiblePages) {
            // Show all pages if total is small
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Show first page
            pages.push(1);
            
            if (currentPage > 4) {
                pages.push('...');
            }
            
            // Show pages around current page
            const start = Math.max(2, currentPage - 1);
            const end = Math.min(totalPages - 1, currentPage + 1);
            
            for (let i = start; i <= end; i++) {
                if (!pages.includes(i)) {
                    pages.push(i);
                }
            }
            
            if (currentPage < totalPages - 3) {
                pages.push('...');
            }
            
            // Show last page
            if (!pages.includes(totalPages)) {
                pages.push(totalPages);
            }
        }
        
        return pages;
    };

    const handlePageClick = (page) => {
        if (page !== '...' && page !== currentPage) {
            onPageChange(page);
        }
    };

    const handlePrevious = () => {
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };

    const handleNext = () => {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };

    return (
        <div className="dom5md-pagination">
            <div className="pagination-info">
                <span>
                    {__('Page', 'dom5-member-directory')} {currentPage} {__('of', 'dom5-member-directory')} {totalPages}
                </span>
            </div>
            
            <div className="pagination-controls">
                <button
                    className="pagination-btn prev"
                    onClick={handlePrevious}
                    disabled={currentPage === 1}
                    title={__('Previous page', 'dom5-member-directory')}
                >
                    <span className="dashicons dashicons-arrow-left-alt2"></span>
                    {__('Previous', 'dom5-member-directory')}
                </button>

                <div className="page-numbers">
                    {getPageNumbers().map((page, index) => (
                        <button
                            key={index}
                            className={`page-number ${page === currentPage ? 'current' : ''} ${page === '...' ? 'ellipsis' : ''}`}
                            onClick={() => handlePageClick(page)}
                            disabled={page === '...' || page === currentPage}
                        >
                            {page}
                        </button>
                    ))}
                </div>

                <button
                    className="pagination-btn next"
                    onClick={handleNext}
                    disabled={currentPage === totalPages}
                    title={__('Next page', 'dom5-member-directory')}
                >
                    {__('Next', 'dom5-member-directory')}
                    <span className="dashicons dashicons-arrow-right-alt2"></span>
                </button>
            </div>
        </div>
    );
};

export default Pagination;
