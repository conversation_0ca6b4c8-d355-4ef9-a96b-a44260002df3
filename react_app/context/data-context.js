import React, { createContext, useContext, useState } from "react";
const DataContext = createContext();

function DataContextProvider({ children }) {
  const [data, setData] = useState();
  const [teams, setTeams] = useState([]);
  const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);
  const updateData = (newValue) => {
    setData((prev) => {
      return {
        ...prev,
        ...newValue,
      };
    });
  };

  const value = {
    data,
    updateData,
    teams,
    setTeams,
    members,
    setMembers,
    loading,
    setLoading,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
}

function useDataContext() {
  return useContext(DataContext);
}

export {
  DataContextProvider,
  useDataContext,
};