import { createRoot } from "react-dom";
import App from "./App.jsx";
import { createHooks } from "@wordpress/hooks";
import { BrowserRouter } from "react-router";
import "../assets/scss/dom5-member-directory.scss";
import { DataContextProvider } from "./context/data-context";

window.dom5_member_directory_hooks = createHooks();

document.addEventListener("DOMContentLoaded", function () {
	const body = document.getElementById("dom5-member-directory-body");
	const root = createRoot(body);

	root.render(
		<DataContextProvider>
			<BrowserRouter>
				<App />
			</BrowserRouter>
		</DataContextProvider>,
	);
});
