<?php
/**
 * Plugin Name:       DOM5 Member Directory
 * Plugin URI:        https://dom5.com/member-directory
 * Description:       A comprehensive member directory plugin with team management, contact forms, and advanced member profiles.
 * Version:           1.0.0
 * Author:            DOM5
 * Author URI:        https://dom5.com
 * License:           GPL-3.0+
 * License URI:       http://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       dom5-member-directory
 * Domain Path:       /languages
 * Requires at least: 6.0
 * Tested up to:      6.4
 * Requires PHP:      8.0
 * Network:           false
 */

declare(strict_types=1);

if (!defined('ABSPATH')) {
    exit();
}

if (file_exists(dirname(__FILE__) . '/vendor/autoload.php')) {
    require_once dirname(__FILE__) . '/vendor/autoload.php';
}

if (!class_exists('DOM5MemberDirectory')) {
    final class DOM5MemberDirectory
    {
        private function __construct()
        {
            $this->define_constants();
            register_activation_hook(__FILE__, [$this, 'activate']);
            register_deactivation_hook(__FILE__, [$this, 'deactivate']);
            add_action('init', [$this, 'on_plugins_loaded']);
            add_action('dom5_member-directory_loaded', [$this, 'init_plugin']);
        }


        public static function init()
        {
            static $instance = false;

            if (!$instance) {
                $instance = new self();
            }

            return $instance;
        }
        public function define_constants(): void
        {
            /**
             * Define plugin constants
             */
            define('DOM5MD_VERSION', '1.0.0');
            define('DOM5MD_SLUG', 'dom5-member-directory');
            define('DOM5MD_PLUGIN_FILE', __FILE__);
            define('DOM5MD_PLUGIN_ROOT_URI', plugins_url('/', __FILE__));
            define('DOM5MD_ROOT_DIR_PATH', plugin_dir_path(__FILE__));
            define('DOM5MD_INCLUDES_DIR_PATH', DOM5MD_ROOT_DIR_PATH . 'includes/');
            define('DOM5MD_ASSETS_DIR_PATH', DOM5MD_ROOT_DIR_PATH . 'assets/');
            define('DOM5MD_ASSETS_URI', DOM5MD_PLUGIN_ROOT_URI . 'assets/');
            define('DOM5MD_TEMPLATES_DIR_PATH', DOM5MD_ROOT_DIR_PATH . 'templates/');
            define('DOM5MD_LANGUAGES_DIR_PATH', DOM5MD_ROOT_DIR_PATH . 'languages/');
        }


        public function on_plugins_loaded()
        {
            do_action('dom5_member-directory_loaded');
        }

        /**
         * Initialize the plugin
         */
        public function init_plugin(): void
        {
            $this->load_textdomain();

            if (is_admin()) {
                new DOM5MemberDirectory\Admin();
            }

            // Initialize frontend components
            new DOM5MemberDirectory\Frontend();

            // Initialize database handler
            new DOM5MemberDirectory\Database();

            // Initialize REST API
            new DOM5MemberDirectory\API();
        }

        /**
         * Load plugin textdomain for internationalization
         */
        public function load_textdomain(): void
        {
            load_plugin_textdomain(
                'dom5-member-directory',
                false,
                dirname(plugin_basename(__FILE__)) . '/languages/'
            );
        }

        /**
         * Plugin activation hook
         */
        public function activate(): void
        {
            // Create database tables
            $database = new DOM5MemberDirectory\Database();
            $database->create_tables();

            // Set default options
            $this->set_default_options();

            // Flush rewrite rules
            flush_rewrite_rules();
        }

        /**
         * Plugin deactivation hook
         */
        public function deactivate(): void
        {
            // Flush rewrite rules
            flush_rewrite_rules();
        }

        /**
         * Set default plugin options
         */
        private function set_default_options(): void
        {
            $default_options = [
                'dom5md_version' => DOM5MD_VERSION,
                'dom5md_db_version' => '1.0.0',
                'dom5md_members_per_page' => 12,
                'dom5md_teams_per_page' => 12,
                'dom5md_enable_contact_form' => true,
                'dom5md_contact_form_email_admin' => true,
            ];

            foreach ($default_options as $option_name => $option_value) {
                if (get_option($option_name) === false) {
                    add_option($option_name, $option_value);
                }
            }
        }
    }
}

/**
 * Initializes the main plugin
 *
 * @return \DOM5MemberDirectory
 */
if (!function_exists('DOM5MemberDirectory_Start')) {
    function DOM5MemberDirectory_Start()
    {
        return DOM5MemberDirectory::init();

    }
}

// Plugin Start
DOM5MemberDirectory_Start();