<?php
/*
 * Plugin Name:		DOM5 Member Directory
 * Plugin URI:		
 * Description:		A great plugin!
 * Version:			1.00
 * Author:			DOM5
 * Author URI:		
 * License:			GPL-3.0+
 * License URI:		http://www.gnu.org/licenses/gpl-3.0.txt
 * Author URI:		
 * Text Domain:		dom5-member-directory
 * Domain Path:		/languages
 */

if (!defined('ABSPATH')) {
    exit();
}

if (file_exists(dirname(__FILE__) . '/vendor/autoload.php')) {
    require_once dirname(__FILE__) . '/vendor/autoload.php';
}

if (!class_exists('DOM5MemberDirectory')) {
    final class DOM5MemberDirectory
    {
        private function __construct()
        {
            $this->define_constants();
            register_activation_hook(__FILE__, [$this, 'activate']);
            register_deactivation_hook(__FILE__, [$this, 'deactivate']);
            add_action('init', [$this, 'on_plugins_loaded']);
            add_action('dom5_member-directory_loaded', [$this, 'init_plugin']);
        }


        public static function init()
        {
            static $instance = false;

            if (!$instance) {
                $instance = new self();
            }

            return $instance;
        }
        public function define_constants()
        {
            /**
             * Defines CONSTANTS for Whole plugins.
             */
            define('DOM5MD_SLUG', 'dom5-member-directory');
            define('DOM5MD_PLUGIN_ROOT_URI', plugins_url('/', __FILE__));
            define('DOM5MD_ROOT_DIR_PATH', plugin_dir_path(__FILE__));
            define('DOM5MD_ASSETS_DIR_PATH', DOM5MD_ROOT_DIR_PATH . 'assets/');
            define('DOM5MD_ASSETS_URI', DOM5MD_PLUGIN_ROOT_URI . 'assets/');
        }


        public function on_plugins_loaded()
        {
            do_action('dom5_member-directory_loaded');
        }

        /**
         * Initialize the plugin
         *
         * @return void
         */
        public function init_plugin()
        {
            if (is_admin()) {
                new DOM5MemberDirectory\Admin();
            }
        }


        public function load_textdomain()
        {
            // load_plugin_textdomain('betterlinks', false, dirname(plugin_basename(__FILE__)) . '/languages/');
        }


        public function activate(){}
        public function deactivate()
        {
            // new BetterLinks\Uninstall();
        }
    }
}

/**
 * Initializes the main plugin
 *
 * @return \DOM5MemberDirectory
 */
if (!function_exists('DOM5MemberDirectory_Start')) {
    function DOM5MemberDirectory_Start()
    {
        return DOM5MemberDirectory::init();

    }
}

// Plugin Start
DOM5MemberDirectory_Start();